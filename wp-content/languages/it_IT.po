# Translation of WordPress - 4.9.x in Italian
# This file is distributed under the same license as the WordPress - 4.9.x package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-09-02 18:07:10+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: it\n"
"Project-Id-Version: WordPress - 4.9.x\n"

#: wp-includes/class-wp.php:293 wp-includes/ms-deprecated.php:275
#: wp-includes/ms-deprecated.php:294
msgid "Sorry, you are not allowed to view this item."
msgstr "Non hai i permessi per visualizzare questo elemento."

#: wp-includes/class-wp.php:293 wp-includes/ms-deprecated.php:275
#: wp-includes/ms-deprecated.php:294
msgid "A variable mismatch has been detected."
msgstr "È stata rilevata una mancata corrispondenza della variabile."

#: wp-activate.php:30
msgid "A key value mismatch has been detected. Please follow the link provided in your activation email."
msgstr "È stata rilevata una mancata corrispondenza del valore della chiave. Visita il link che trovi nella tua mail di attivazione."

#: wp-includes/taxonomy.php:512
msgid "Edit Category"
msgstr "Modifica categoria"

#: wp-includes/taxonomy.php:507
msgid "Search Categories"
msgstr "Cerca categorie"

#: wp-signup.php:184
msgid "No"
msgstr "No"

#: wp-includes/taxonomy.php:514
msgid "Update Category"
msgstr "Aggiorna categoria"

#: wp-includes/taxonomy.php:111
msgid "Link Categories"
msgstr "Categorie dei link"

#: wp-includes/taxonomy.php:512
msgid "Edit Tag"
msgstr "Modifica tag"

#: wp-includes/widgets/class-wp-widget-tag-cloud.php:48
msgid "Tags"
msgstr "Tag"

#: wp-includes/widgets/class-wp-widget-rss.php:30
msgid "RSS"
msgstr "RSS"

#: wp-includes/widgets/class-wp-widget-recent-comments.php:30
#: wp-includes/widgets/class-wp-widget-recent-comments.php:75
msgid "Recent Comments"
msgstr "Commenti recenti"

#: wp-includes/widgets/class-wp-widget-rss.php:72
msgid "Unknown Feed"
msgstr "Feed sconosciuto"

#: wp-includes/widgets.php:179
msgid "Sidebar"
msgstr "Barra laterale"

#: wp-login.php:807
msgid "Username"
msgstr "Nome utente"

#: wp-includes/script-loader.php:704
msgid "Save as Pending"
msgstr "Salva come in sospeso"

#: wp-includes/script-loader.php:710
msgid "Privately Published"
msgstr "Pubblicato privatamente"

#: wp-includes/script-loader.php:707
msgid "Public"
msgstr "Pubblico"

#: wp-includes/taxonomy.php:518
msgid "Add or remove tags"
msgstr "Aggiungi o rimuovi tag"

#: wp-includes/taxonomy.php:509
msgid "All Categories"
msgstr "Tutte le categorie"

#: wp-includes/widgets/class-wp-widget-links.php:29
msgid "Links"
msgstr "Link"

#: wp-includes/widgets/class-wp-widget-pages.php:130
#: wp-includes/widgets/class-wp-widget-categories.php:168
#: wp-includes/widgets/class-wp-widget-archives.php:160
#: wp-includes/widgets/class-wp-nav-menu-widget.php:137
#: wp-includes/widgets/class-wp-widget-search.php:70
#: wp-includes/widgets/class-wp-widget-recent-comments.php:156
#: wp-includes/widgets/class-wp-widget-tag-cloud.php:126
#: wp-includes/widgets/class-wp-widget-meta.php:113
#: wp-includes/widgets/class-wp-widget-recent-posts.php:135
#: wp-includes/widgets/class-wp-widget-calendar.php:101
#: wp-includes/widgets/class-wp-widget-text.php:474
#: wp-includes/widgets/class-wp-widget-text.php:507
#: wp-includes/widgets/class-wp-widget-media.php:406
#: wp-includes/widgets/class-wp-widget-custom-html.php:253
msgid "Title:"
msgstr "Titolo:"

#: wp-includes/taxonomy.php:517
msgid "Separate tags with commas"
msgstr "Separa i tag con delle virgole"

#: wp-includes/widgets/class-wp-widget-meta.php:60
msgid "Comments <abbr title=\"Really Simple Syndication\">RSS</abbr>"
msgstr "<abbr title=\"Really Simple Syndication\">RSS</abbr> dei commenti"

#: wp-includes/script-loader.php:866
msgid "Select default color"
msgstr "Seleziona il colore predefinito"

#: wp-includes/widgets/class-wp-widget-pages.php:136
msgid "Page title"
msgstr "Titolo della pagina"

#: wp-includes/widgets/class-wp-widget-pages.php:137
msgid "Page order"
msgstr "Ordine della pagina"

#: wp-includes/widgets/class-wp-widget-pages.php:138
msgid "Page ID"
msgstr "ID pagina"

#: wp-includes/widgets/class-wp-widget-pages.php:142
msgid "Exclude:"
msgstr "Escludi:"

#: wp-includes/widgets/class-wp-widget-pages.php:145
msgid "Page IDs, separated by commas."
msgstr "ID pagina, separati da virgole."

#: wp-includes/widgets/class-wp-widget-links.php:26
msgid "Your blogroll"
msgstr "Il blogroll"

#: wp-includes/widgets/class-wp-widget-links.php:148
msgid "Show Link Image"
msgstr "Mostra immagine link"

#: wp-includes/widgets/class-wp-widget-links.php:150
msgid "Show Link Name"
msgstr "Mostra nome link"

#: wp-includes/widgets/class-wp-widget-links.php:152
msgid "Show Link Description"
msgstr "Mostra descrizione link"

#: wp-includes/widgets/class-wp-widget-links.php:154
msgid "Show Link Rating"
msgstr "Mostra valutazione link"

#: wp-includes/widgets/class-wp-widget-archives.php:85
msgid "Select Month"
msgstr "Seleziona mese"

#: wp-includes/widgets/class-wp-widget-categories.php:175
#: wp-includes/widgets/class-wp-widget-archives.php:164
msgid "Show post counts"
msgstr "Mostra conteggio articoli"

#: wp-includes/widgets/class-wp-widget-meta.php:32
#: wp-includes/widgets/class-wp-widget-meta.php:45
#: wp-includes/theme-compat/sidebar.php:103
msgid "Meta"
msgstr "Meta"

#: wp-includes/widgets/class-wp-widget-calendar.php:39
msgid "Calendar"
msgstr "Calendario"

#: wp-includes/widgets/class-wp-widget-text.php:42
msgid "Text"
msgstr "Testo"

#: wp-includes/widgets/class-wp-widget-categories.php:75
msgid "Select Category"
msgstr "Seleziona una categoria"

#: wp-includes/widgets/class-wp-widget-categories.php:178
msgid "Show hierarchy"
msgstr "Visualizza la gerarchia"

#: wp-includes/widgets/class-wp-widget-recent-posts.php:30
#: wp-includes/widgets/class-wp-widget-recent-posts.php:48
msgid "Recent Posts"
msgstr "Articoli recenti"

#: wp-includes/widgets/class-wp-widget-recent-posts.php:138
msgid "Number of posts to show:"
msgstr "Numero di articoli da visualizzare:"

#. translators: comments widget: 1: comment author, 2: post link
#: wp-includes/widgets/class-wp-widget-recent-comments.php:115
msgctxt "widgets"
msgid "%1$s on %2$s"
msgstr "%1$s su %2$s"

#: wp-includes/widgets/class-wp-widget-recent-comments.php:159
msgid "Number of comments to show:"
msgstr "Numero di commenti da visualizzare:"

#: wp-includes/widgets.php:1430
msgid "Untitled"
msgstr "Senza titolo"

#: wp-includes/widgets.php:1530
msgid "Display item content?"
msgstr "Visualizza il contenuto delle voci?"

#: wp-includes/widgets/class-wp-widget-tag-cloud.php:29
msgid "Tag Cloud"
msgstr "Tag Cloud"

#: wp-includes/user.php:1480
msgid "Cannot create a user with an empty login name."
msgstr "Impossibile creare un utente senza un nome di login."

#: wp-includes/user.php:2068
msgid "AIM"
msgstr "AIM"

#: wp-includes/user.php:2069
msgid "Yahoo IM"
msgstr "Yahoo IM"

#: wp-includes/user.php:2070
msgid "Jabber / Google Talk"
msgstr "Jabber / Google Talk"

#: wp-includes/script-loader.php:499
msgctxt "password strength"
msgid "Medium"
msgstr "Media"

#: wp-includes/script-loader.php:694
msgid "Publish on:"
msgstr "Pubblica il:"

#: wp-includes/script-loader.php:696
msgid "Published on:"
msgstr "Pubblicato il:"

#: wp-includes/script-loader.php:699
msgid "Show more comments"
msgstr "Mostra altri commenti"

#: wp-includes/script-loader.php:700
msgid "No more comments found."
msgstr "Nessun altro commento trovato."

#: wp-includes/script-loader.php:709
msgid "Password Protected"
msgstr "Protetto da password"

#: wp-includes/script-loader.php:724
msgid "Submitted on:"
msgstr "Inviato il:"

#: wp-includes/script-loader.php:752 wp-includes/script-loader.php:761
msgid "Error while saving the changes."
msgstr "Errore nel salvataggio delle modifiche."

#: wp-includes/script-loader.php:889
msgid "Saving..."
msgstr "Salvataggio in corso..."

#: wp-includes/script-loader.php:890
msgid "Could not set that as the thumbnail image. Try a different attachment."
msgstr "Impossibile impostare questo elemento come miniatura della pagina. Provare con un differente allegato."

#: wp-includes/script-loader.php:713
msgid "Saving Draft&#8230;"
msgstr "Salvataggio bozza&#8230;"

#: wp-includes/taxonomy.php:748 wp-includes/taxonomy.php:2657
#: wp-includes/taxonomy.php:3958
msgid "Empty Term."
msgstr "Termine vuoto"

#: wp-includes/taxonomy.php:4170
msgid "Invalid object ID."
msgstr "ID oggetto non valido."

#: wp-includes/user.php:135
msgid "<strong>ERROR</strong>: The username field is empty."
msgstr "<strong>ERRORE</strong>: il campo nome utente è vuoto."

#: wp-includes/user.php:138 wp-includes/user.php:211
msgid "<strong>ERROR</strong>: The password field is empty."
msgstr "<strong>ERRORE</strong>: il campo password è vuoto."

#: wp-includes/widgets.php:177 wp-includes/widgets.php:248
msgid "Sidebar %d"
msgstr "Barra laterale %d"

#: wp-login.php:854 wp-includes/user.php:2227 wp-includes/user.php:2230
#: wp-includes/user.php:2234 wp-includes/user.php:2259
#: wp-includes/user.php:2268 wp-includes/user.php:2272
#: wp-includes/user.php:2289 wp-includes/user.php:3437
#: wp-includes/user.php:3449 wp-includes/user.php:3467
msgid "Invalid key"
msgstr "Chiave non valida"

#: wp-login.php:601
msgid "Get New Password"
msgstr "Ottieni una nuova password"

#: wp-login.php:803
msgid "Registration Form"
msgstr "Modulo di registrazione"

#: wp-login.php:803
msgid "Register For This Site"
msgstr "Registrazione per questo sito"

#: wp-login.php:987
msgid "User registration is currently not allowed."
msgstr "La registrazione degli utenti non è al momento permessa."

#: wp-mail.php:40
msgid "Slow down cowboy, no need to check for new mails so often!"
msgstr "Rallenta cowboy, non serve controllare così spesso le nuove email!"

#. translators: Post author email address
#: wp-mail.php:128
msgid "Author is %s"
msgstr "L'autore è %s"

#. translators: 1: Database error message, 2: SQL query, 3: Name of the calling
#. function
#: wp-includes/wp-db.php:1363
msgid "WordPress database error %1$s for query %2$s made by %3$s"
msgstr "WordPress errore sul database %1$s per la query %2$s fatta da %3$s"

#. translators: 1: Database error message, 2: SQL query
#: wp-includes/wp-db.php:1366
msgid "WordPress database error %1$s for query %2$s"
msgstr "WordPress errore database %1$s per la query %2$s"

#. translators: 1: Number of updates available to WordPress
#: wp-includes/update.php:622
msgid "%d WordPress Update"
msgstr "%d aggiornamento WordPress"

#. translators: 1: Number of updates available to plugins
#: wp-includes/update.php:626
msgid "%d Plugin Update"
msgid_plural "%d Plugin Updates"
msgstr[0] "%d aggiornamento plugin"
msgstr[1] "%d aggiornamenti plugin"

#. translators: 1: Number of updates available to themes
#: wp-includes/update.php:630
msgid "%d Theme Update"
msgid_plural "%d Theme Updates"
msgstr[0] "%d aggiornamento tema"
msgstr[1] "%d aggiornamenti tema"

#: wp-includes/widgets/class-wp-widget-tag-cloud.php:165
msgid "Taxonomy:"
msgstr "Tassonomia:"

#: wp-includes/widgets/class-wp-nav-menu-widget.php:29
#: wp-includes/taxonomy.php:99
msgid "Navigation Menu"
msgstr "Menu di navigazione"

#: wp-includes/widgets/class-wp-nav-menu-widget.php:141
msgid "Select Menu:"
msgstr "Selezionare il menu:"

#: wp-includes/taxonomy.php:98
msgid "Navigation Menus"
msgstr "Menu di navigazione"

#: wp-includes/user.php:315
msgid "<strong>ERROR</strong>: Your account has been marked as a spammer."
msgstr "<strong>ERRORE</strong>: l'account è stato contrassegnato come un account spammer."

#. translators: 1: WordPress version number, 2: Minimum required MySQL version
#. number
#: wp-includes/wp-db.php:3311
msgid "<strong>ERROR</strong>: WordPress %1$s requires MySQL %2$s or higher"
msgstr "<strong>ERRORE</strong>: WordPress %1$s richiede MySQL %2$s o superiore"

#. translators: 1: Site name
#: wp-links-opml.php:31
msgid "Links for %s"
msgstr "Link per %s"

#: wp-mail.php:15 wp-mail.php:20
msgid "This action has been disabled by the administrator."
msgstr "Questa azione è stata disabilitata dall'amministratore"

#: wp-signup.php:108
msgid "Site Name:"
msgstr "Nome sito:"

#: wp-signup.php:110
msgid "Site Domain:"
msgstr "Dominio sito:"

#: wp-signup.php:123
msgid "sitename"
msgstr "nome sito"

#. translators: %s: site address
#: wp-signup.php:129
msgid "Must be at least 4 characters, letters and numbers only. It cannot be changed, so choose carefully!"
msgstr "Deve essere di almeno 4 caratteri, solo lettere e numeri. Non può venir cambiato quindi sceglilo con attenzione!"

#: wp-signup.php:134
msgid "Site Title:"
msgstr "Titolo sito:"

#: wp-signup.php:175
msgid "Privacy:"
msgstr "Privacy:"

#: wp-signup.php:235
msgid "(Must be at least 4 characters, letters and numbers only.)"
msgstr "(Deve essere di almeno 4 caratteri, solo lettere e numeri.)"

#: wp-signup.php:309
msgid "Get <em>another</em> %s site in seconds"
msgstr "Ottieni <em>un altro</em> %s sito in pochi secondi"

#: wp-signup.php:315
msgid "Welcome back, %s. By filling out the form below, you can <strong>add another site to your account</strong>. There is no limit to the number of sites you can have, so create to your heart&#8217;s content, but write responsibly!"
msgstr "Bentornato %s. Compilando il seguente modulo è possibile <strong>aggiungere un nuovo sito al proprio account</strong>. Non vi è limite al numero di blog che è possibile avere, quindi create i contenuti secondo il vostro cuore ma scrivete con responsabilità."

#: wp-signup.php:330
msgid "If you&#8217;re not going to use a great site domain, leave it for a new user. Now have at it!"
msgstr "Se non si ha  intenzione di utilizzare un grande dominio per il sito, lasciarlo per un nuovo utente. Ora precedete!"

#. translators: %s: name of the network
#: wp-signup.php:540
msgid "Get your own %s account in seconds"
msgstr "Ottieni il tuo account %s in pochi secondi"

#: wp-signup.php:557
msgid "Gimme a site!"
msgstr "Dammi un nuovo sito!"

#: wp-signup.php:560
msgid "Just a username, please."
msgstr "Solo un nome utente, grazie."

#. translators: %s: username
#: wp-signup.php:610
msgid "%s is your new username"
msgstr "%s è il nuovo nome utente"

#: wp-signup.php:614
msgid "If you do not activate your username within two days, you will have to sign up again."
msgstr "Se non si attiva il proprio nome utente entro due giorni, occorrerà registrarsi nuovamente."

#: wp-signup.php:678
msgid "Signup"
msgstr "Registrazione"

#: wp-signup.php:757
msgid "But, before you can start using your site, <strong>you must activate it</strong>."
msgstr "Ma, prima di poter iniziare ad utilizzare queso sito <strong>occorre attivarlo</strong>."

#: wp-signup.php:760
msgid "If you do not activate your site within two days, you will have to sign up again."
msgstr "Se non si attiva il proprio sito entro due giorni si dovrà ripetere la sottoscrizione."

#: wp-signup.php:855
msgid "Registration has been disabled."
msgstr "La registrazione è stata disabilitata."

#: wp-signup.php:873
msgid "Site registration has been disabled."
msgstr "La registrazione dei blog è stata disabilitata."

#: wp-signup.php:894
msgid "You are logged in already. No need to register again!"
msgstr "Si è già fatto il login. Non serve registrarsi nuovamente!"

#: wp-signup.php:892
msgid "Sorry, new registrations are not allowed at this time."
msgstr "Al momento non si accettano nuove registrazioni."

#: wp-signup.php:766
msgid "Check the junk or spam folder of your email client. Sometime emails wind up there by mistake."
msgstr "Controllare la cartella Cestino o Spam del proprio programma email. A volte le email finiscono lì per errore."

#: wp-signup.php:765
msgid "Wait a little longer. Sometimes delivery of email can be delayed by processes outside of our control."
msgstr "Attendere un po'. Alcune volte la consegna delle email può venir ritardata da processi al di fuori del nostro controllo."

#. translators: %s: site address
#: wp-signup.php:755
msgid "Congratulations! Your new site, %s, is almost ready."
msgstr "Congratulazioni! Il tuo nuovo blog, %s, è quasi pronto."

#: wp-signup.php:611
msgid "But, before you can start using your new username, <strong>you must activate it</strong>."
msgstr "Ma prima di poter inziare ad utilizzare il proprio nome utente <strong>bisogna attivarlo</strong>."

#: wp-signup.php:242
msgid "We send your registration email to this address. (Double-check your email address before continuing.)"
msgstr "Invieremo l'email di registrazione a questo indirizzo. (Controllare attentamente l'indirizzo email prima di proseguire.)"

#: wp-includes/theme-compat/sidebar.php:56
msgid "F, Y"
msgstr "F Y"

#: wp-includes/theme-compat/sidebar.php:48
msgid "l, F jS, Y"
msgstr "l, j F Y"

#. translators: %s: category name
#: wp-includes/theme-compat/sidebar.php:39
msgid "You are currently browsing the archives for the %s category."
msgstr "Stai visualizzando gli archivi per la categoria %s."

#. translators: 1: blog name, 2: WordPress
#: wp-includes/theme-compat/footer.php:26
msgid "%1$s is proudly powered by %2$s"
msgstr "%1$s utilizza %2$s"

#: wp-includes/taxonomy.php:2141
msgid "A term with the name provided already exists with this parent."
msgstr "Un termine con il nome indicato esiste già per questo genitore."

#: wp-includes/taxonomy.php:516
msgid "New Category Name"
msgstr "Nuovo nome di categoria"

#: wp-includes/taxonomy.php:515
msgid "Add New Category"
msgstr "Aggiungi una nuova categoria"

#: wp-includes/taxonomy.php:516
msgid "New Tag Name"
msgstr "Nuovo nome tag"

#: wp-includes/taxonomy.php:514
msgid "Update Tag"
msgstr "Aggiorna tag"

#: wp-includes/taxonomy.php:515
msgid "Add New Tag"
msgstr "Aggiungi nuovo tag"

#: wp-includes/taxonomy.php:508
msgid "Popular Tags"
msgstr "Tag più popolari"

#: wp-includes/taxonomy.php:509
msgid "All Tags"
msgstr "Tutti i tag"

#: wp-includes/taxonomy.php:505
msgctxt "taxonomy general name"
msgid "Categories"
msgstr "Categorie"

#: wp-includes/taxonomy.php:506
msgctxt "taxonomy singular name"
msgid "Category"
msgstr "Categoria"

#: wp-includes/taxonomy.php:507
msgid "Search Tags"
msgstr "Cerca tag"

#: wp-includes/widgets/class-wp-nav-menu-widget.php:133
msgid "No menus have been created yet. <a href=\"%s\">Create some</a>."
msgstr "Non è stato creato ancora alcun menu. <a href=\"%s\">Crearne uno</a>"

#: wp-includes/script-loader.php:888
msgid "Use as featured image"
msgstr "Utilizza come immagine in evidenza"

#: wp-includes/taxonomy.php:112
msgid "Link Category"
msgstr "Categoria link"

#: wp-includes/taxonomy.php:113
msgid "Search Link Categories"
msgstr "Ricerca categorie link"

#: wp-includes/taxonomy.php:115
msgid "All Link Categories"
msgstr "Tutte le categorie link"

#: wp-includes/taxonomy.php:117
msgid "Update Link Category"
msgstr "Aggiorna categoria link"

#: wp-includes/taxonomy.php:118
msgid "Add New Link Category"
msgstr "Aggiungere una nuova categoria di link"

#: wp-includes/taxonomy.php:119
msgid "New Link Category Name"
msgstr "Nuovo nome di categoria link"

#: wp-includes/widgets/class-wp-widget-categories.php:172
#: wp-includes/widgets/class-wp-widget-archives.php:162
msgid "Display as dropdown"
msgstr "Visualizza come menu a discesa"

#: wp-login.php:367
msgid "If this was a mistake, just ignore this email and nothing will happen."
msgstr "Se si è trattato di un errore, ignorare questa email e non accadrà nulla."

#: wp-login.php:660
msgid "The passwords do not match."
msgstr "Le password non corrispondono."

#: wp-login.php:683
msgid "Enter your new password below."
msgstr "Digitare nuovamente la password."

#: wp-login.php:691
msgid "New password"
msgstr "Nuova password"

#: wp-signup.php:125
msgid "domain"
msgstr "dominio"

#: wp-includes/script-loader.php:898
msgid ""
"You are about to permanently delete this menu. \n"
" 'Cancel' to stop, 'OK' to delete."
msgstr ""
"Si sta per eliminare definitivamente questo menu.\n"
" 'Annulla' per interrompere, 'OK' per eliminare."

#: wp-login.php:578
msgid "Lost Password"
msgstr "Password persa"

#: wp-includes/widgets/class-wp-widget-text.php:489
msgid "Automatically add paragraphs"
msgstr "Aggiungi automaticamente paragrafi"

#: wp-includes/script-loader.php:652
msgid "Approve and Reply"
msgstr "Approvare e rispondere"

#: wp-includes/user.php:2834
msgid "User"
msgstr "Utente"

#: wp-includes/taxonomy.php:513
msgid "View Category"
msgstr "Visualizza categoria"

#: wp-includes/taxonomy.php:505
msgctxt "taxonomy general name"
msgid "Tags"
msgstr "Tag"

#: wp-includes/taxonomy.php:506
msgctxt "taxonomy singular name"
msgid "Tag"
msgstr "Tag"

#: wp-includes/widgets/class-wp-widget-links.php:129
msgctxt "links widget"
msgid "All Links"
msgstr "Tutti i link"

#: wp-includes/widgets/class-wp-widget-links.php:140
msgid "Link title"
msgstr "Titolo del link"

#: wp-includes/widgets/class-wp-widget-links.php:141
msgid "Link rating"
msgstr "Punteggio link"

#: wp-includes/widgets/class-wp-widget-links.php:142
msgid "Link ID"
msgstr "ID link"

#: wp-includes/widgets/class-wp-widget-links.php:157
msgid "Number of links to show:"
msgstr "Numero di link da visualizzare:"

#: wp-includes/script-loader.php:584
msgid "Allowed Files"
msgstr "File permessi"

#: wp-includes/script-loader.php:560 wp-includes/script-loader.php:734
msgid "Saved"
msgstr "Salvato"

#: wp-includes/taxonomy.php:519
msgid "Choose from the most used tags"
msgstr "Scegli un tag fra quelli più utilizzati"

#. translators: %s: site address
#: wp-signup.php:129
msgid "Your address will be %s."
msgstr "Il tuo indirizzo sarà %s."

#: wp-signup.php:176
msgid "Allow search engines to index this site."
msgstr "Permetti ai motori di ricerca di indicizzare questo sito."

#: wp-load.php:90
msgid "Create a Configuration File"
msgstr "Crea un file di configurazione"

#: wp-login.php:368
msgid "To reset your password, visit the following address:"
msgstr "Per reimpostare la tua password visita il seguente indirizzo:"

#: wp-includes/script-loader.php:548
msgid "Save &amp; Publish"
msgstr "Salva &amp; pubblica"

#. translators: 1: first name, 2: last name
#: wp-includes/user.php:1600
msgctxt "Display name based on first name and last name"
msgid "%1$s %2$s"
msgstr "%1$s %2$s"

#: wp-includes/script-loader.php:867
msgid "Select Color"
msgstr "Seleziona colore"

#: wp-includes/widgets/class-wp-widget-links.php:143
msgctxt "Links widget"
msgid "Random"
msgstr "Casuale"

#: wp-includes/script-loader.php:500
msgctxt "password strength"
msgid "Strong"
msgstr "Forte"

#: wp-includes/script-loader.php:501
msgctxt "password mismatch"
msgid "Mismatch"
msgstr "Mancata corrispondenza"

#: wp-includes/taxonomy.php:520
msgid "No tags found."
msgstr "Nessun tag trovato."

#: wp-includes/widgets/class-wp-widget-meta.php:74
msgctxt "meta widget link text"
msgid "WordPress.org"
msgstr "WordPress.org"

#: wp-login.php:402
msgid "Possible reason: your host may have disabled the mail() function."
msgstr "Possibili motivi: il tuo host può aver disabilitato la funzione mail()."

#. translators: %s: login URL
#: wp-signup.php:859
msgid "You must first <a href=\"%s\">log in</a>, and then you can create a new site."
msgstr "Devi prima <a href=\"%s\">accedere</a> e dopo potrai creare un nuovo sito."

#. translators: %s: email address
#: wp-signup.php:769
msgid "Have you entered your email correctly? You have entered %s, if it&#8217;s incorrect, you will not receive your email."
msgstr "Hai inserito l&#8217;indirizzo email correttamente? Hai inserito %s, nel caso non fosse corretta, non riceverai alcuna email."

#: wp-includes/script-loader.php:556
msgid "Invalid"
msgstr "Non valido"

#: wp-includes/update.php:633
msgid "Translation Updates"
msgstr "Aggiornamenti delle traduzioni"

#. translators: If there are characters in your language that are not supported
#. * by Open Sans, translate this to 'off'. Do not translate into your own
#. language.
#: wp-includes/script-loader.php:947
msgctxt "Open Sans font: on or off"
msgid "on"
msgstr "on"

#: wp-includes/widgets/class-wp-widget-search.php:27
msgid "A search form for your site."
msgstr "Un modulo di ricerca per il tuo sito."

#: wp-includes/widgets/class-wp-widget-archives.php:27
msgid "A monthly archive of your site&#8217;s Posts."
msgstr "Un archivio mensile degli articoli contenuti nel tuo sito."

#: wp-includes/widgets/class-wp-widget-recent-posts.php:27
msgid "Your site&#8217;s most recent Posts."
msgstr "Gli articoli più recenti nel tuo sito."

#: wp-includes/widgets/class-wp-widget-recent-comments.php:27
msgid "Your site&#8217;s most recent comments."
msgstr "I commenti più recenti nel tuo sito."

#: wp-includes/widgets/class-wp-widget-tag-cloud.php:26
msgid "A cloud of your most used tags."
msgstr "Una nuvola contenente tutti i tag più usati."

#: wp-includes/widgets/class-wp-widget-calendar.php:36
msgid "A calendar of your site&#8217;s Posts."
msgstr "Un calendario degli articoli del tuo sito."

#: wp-includes/widgets/class-wp-widget-categories.php:27
msgid "A list or dropdown of categories."
msgstr "Una lista o un elenco a discesa di categorie."

#: wp-includes/widgets/class-wp-widget-rss.php:26
msgid "Entries from any RSS or Atom feed."
msgstr "Contenuti ricavati da qualsiasi feed RSS o Atom."

#: wp-includes/widgets/class-wp-widget-search.php:30
msgctxt "Search widget"
msgid "Search"
msgstr "Cerca"

#: wp-includes/wp-db.php:1230
msgid "The query argument of %s must have a placeholder."
msgstr "L'argomento %s della query deve avere un valore."

#. translators: 1: Browser cookie documentation URL
#: wp-login.php:925
msgid "<strong>ERROR</strong>: Cookies are blocked or not supported by your browser. You must <a href=\"%s\">enable cookies</a> to use WordPress."
msgstr "<strong>ERRORE</strong>: i cookies sono bloccati o non supportati dal tuo browser. Per utilizzare WordPress devi <a href=\"%s\">abilitare i cookie</a>."

#: wp-login.php:822
msgid "Registration confirmation will be emailed to you."
msgstr "La conferma della registrazione ti arriverà per email."

#: wp-includes/script-loader.php:509
msgid "Hide"
msgstr "Nascondi"

#: wp-includes/script-loader.php:508
msgid "Show"
msgstr "Visualizza"

#: wp-login.php:557
msgid "Your password reset link has expired. Please request a new link below."
msgstr "Il link per il reset della password è scaduto. Richiedi un nuovo link qua sotto."

#: wp-login.php:555
msgid "Your password reset link appears to be invalid. Please request a new link below."
msgstr "Il link per il reset della password non appare valido. Richiedi un nuovo link qua sotto."

#: wp-includes/wp-db.php:1379 wp-includes/wp-db.php:1396
msgid "WordPress database error:"
msgstr "Errore sul database di WordPress:"

#: wp-includes/user.php:2108
msgid "Hint: The password should be at least twelve characters long. To make it stronger, use upper and lower case letters, numbers, and symbols like ! \" ? $ % ^ &amp; )."
msgstr "Suggerimento: la password dovrebbe essere lunga almeno dodici caratteri. Per renderla più sicura utilizza lettere maiuscole e minuscole, numeri e simboli come ! \" ? $ % ^ &amp; )."

#. translators: User email change notification email subject. 1: Site name
#: wp-includes/user.php:1949
msgid "[%s] Notice of Email Change"
msgstr "[%s] Avviso di modifica indirizzo email"

#: wp-includes/script-loader.php:654
msgid ""
"Are you sure you want to edit this comment?\n"
"The changes you made will be lost."
msgstr ""
"Sei sicuro di voler editare questo commento?\n"
"Le modifiche fatte saranno perse."

#. translators: %s: Error string for a failed update
#: wp-includes/script-loader.php:790
msgid "Update Failed: %s"
msgstr "Aggiornamento fallito: %s"

#. translators: User password change notification email subject. 1: Site name
#: wp-includes/user.php:1893
msgid "[%s] Notice of Password Change"
msgstr "[%s] Avviso di cambiamento della password"

#: wp-includes/widgets/class-wp-widget-text.php:485
#: wp-includes/widgets/class-wp-widget-text.php:546
#: wp-includes/widgets/class-wp-widget-custom-html.php:258
msgid "Content:"
msgstr "Messaggio:"

#: wp-includes/taxonomy.php:521
msgid "No tags"
msgstr "Nessun tag"

#: wp-includes/script-loader.php:753
msgid "Remove From Bulk Edit"
msgstr "Rimuovi dalla modifica di gruppo"

#. translators: %s: post title
#: wp-includes/theme-compat/comments.php:37
msgid "One response to %s"
msgstr "Una replica a %s"

#: wp-login.php:921 wp-login.php:926
msgid "https://codex.wordpress.org/Cookies"
msgstr "https://codex.wordpress.org/Cookies"

#. translators: Accessibility text
#: wp-includes/script-loader.php:802
msgid "Update canceled."
msgstr "Aggiornamento annullato."

#: wp-includes/taxonomy.php:381 wp-includes/taxonomy.php:382
msgid "Taxonomy names must be between 1 and 32 characters in length."
msgstr "I nomi di tassonomia devono essere con lunghezza tra 1 e 32 caratteri."

#: wp-includes/widgets/class-wp-widget-archives.php:91
msgid "Select Week"
msgstr "Scegli la settimana"

#: wp-includes/widgets/class-wp-widget-archives.php:94
msgid "Select Post"
msgstr "Scegli articolo"

#: wp-includes/script-loader.php:781
msgid "Updating..."
msgstr "Aggiornamento in corso..."

#: wp-includes/taxonomy.php:3595
msgid "Could not split shared term."
msgstr "Impossibile dividere termini condivisi."

#. translators: %s: taxonomy label, %l: list of terms formatted as per
#. $term_template
#: wp-includes/taxonomy.php:4099
msgid "%s: %l."
msgstr "%s: %l."

#: wp-login.php:995
msgid "<strong>You have successfully updated WordPress!</strong> Please log back in to see what&#8217;s new."
msgstr "<strong>Hai aggiornato WordPress!</strong> Effettua nuovamente il login per vedere cosa c'è di nuovo.."

#: wp-includes/taxonomy.php:142 wp-includes/taxonomy.php:143
msgctxt "post format"
msgid "Format"
msgstr "Formato"

#: wp-includes/theme-compat/comments.php:66
msgid "Comments are closed."
msgstr "I commenti sono chiusi."

#: wp-includes/script-loader.php:1142
msgid "Previous"
msgstr "Precedente"

#. translators: %s: database access abstraction class, usually wpdb or a class
#. extending wpdb
#: wp-includes/wp-db.php:1110
msgid "%s must set a database connection for use with escaping."
msgstr "%s deve impostare una connessione ad un database da utilizzare per l'escaping."

#: wp-includes/theme.php:2262
msgid "You need to pass an array of types."
msgstr "Occorre passare un array di tipi."

#. translators: 1: Theme support 2: hook name
#: wp-includes/theme.php:2418
msgid "Theme support for %1$s should be registered before the %2$s hook."
msgstr "Il supporto del tema a %1$s deve essere registrato prima dell'hook %2$s."

#: wp-includes/taxonomy.php:510
msgid "Parent Category"
msgstr "Categoria genitore"

#: wp-includes/taxonomy.php:520
msgid "No categories found."
msgstr "Nessuna categoria trovata"

#: wp-includes/script-loader.php:511
msgid "Show password"
msgstr "Mostra password"

#: wp-includes/script-loader.php:512
msgid "Hide password"
msgstr "Nascondi password"

#: wp-includes/script-loader.php:506
msgid "Your new password has not been saved."
msgstr "La tua nuova password non è stata salvata."

#. translators: To add an additional Open Sans character subset specific to
#. your language, * translate this to 'greek', 'cyrillic' or 'vietnamese'. Do
#. not translate into your own language.
#: wp-includes/script-loader.php:953
msgctxt "Open Sans font: add new subset (greek, cyrillic, vietnamese)"
msgid "no-subset"
msgstr "no-subset"

#: wp-login.php:701
msgid "Strength indicator"
msgstr "Livello di sicurezza"

#: wp-mail.php:232
msgid "Author:"
msgstr "Autore:"

#: wp-includes/wp-db.php:1737
msgid "Error reconnecting to the database"
msgstr "Errore di riconnessione al database"

#: wp-signup.php:148
msgid "Site Language:"
msgstr "Lingua del sito:"

#: wp-includes/wp-db.php:1033
msgid "Can&#8217;t select database"
msgstr "Impossibile selezionare il database"

#: wp-includes/wp-db.php:1588
msgid "Are you sure that you have typed the correct hostname?"
msgstr "Sei sicuro di aver immesso l'hostname corretto?"

#: wp-includes/wp-db.php:1589 wp-includes/wp-db.php:1746
msgid "Are you sure that the database server is running?"
msgstr "Sei sicuro che il server del database sia attivo?"

#. translators: 1: database user, 2: database name
#: wp-includes/wp-db.php:1046
msgid "Does the user %1$s have permission to use the %2$s database?"
msgstr "L'utente %1$s ha i permessi per usare il database %2$s?"

#: wp-mail.php:233
msgid "Posted title:"
msgstr "Titolo pubblicato:"

#: wp-includes/theme-compat/embed-404.php:14
msgid "Oops! That embed can&#8217;t be found."
msgstr "Oops! Quell'embed non può essere trovato."

#: wp-includes/shortcodes.php:67
msgid "Invalid shortcode name: Empty name given."
msgstr "Nome per lo shortcode non valido: il nome non può essere vuoto."

#: wp-includes/wp-db.php:1747
msgid "Are you sure that the database server is not under particularly heavy load?"
msgstr "Sei sicuro che il server del database non sia sovraccarico?"

#. translators: %s: site address
#: wp-signup.php:901
msgid "The site you were looking for, %s, does not exist, but you can create it now!"
msgstr "Il sito che stavi cercando, %s, non esiste, ma puoi crearlo adesso!"

#. translators: 1: site link, 2: search query
#: wp-includes/theme-compat/sidebar.php:70
msgid "You have searched the %1$s blog archives for <strong>&#8216;%2$s&#8217;</strong>. If you are unable to find anything in these search results, you can try one of these links."
msgstr "Hai cercato negli archivi del blog %1$s  <strong>&#8216;%2$s&#8217;</strong>. Se non hai trovato nulla in questi risultati  prova uno di questi link."

#. translators: %s: template name
#: wp-includes/theme-compat/sidebar.php:11
#: wp-includes/theme-compat/header.php:11
#: wp-includes/theme-compat/footer.php:11
#: wp-includes/theme-compat/comments.php:12
msgid "Theme without %s"
msgstr "Tema senza %s"

#: wp-includes/taxonomy.php:523
msgid "Tags list"
msgstr "Elenco dei tag"

#: wp-includes/taxonomy.php:523
msgid "Categories list"
msgstr "Elenco delle categorie"

#. translators: 1: site link, 2: archive date
#: wp-includes/theme-compat/sidebar.php:46
msgid "You are currently browsing the %1$s blog archives for the day %2$s."
msgstr "Stai sfogliando gli archivi del blog %1$s del giorno %2$s."

#. translators: %s: site link
#: wp-includes/theme-compat/sidebar.php:78
msgid "You are currently browsing the %s blog archives."
msgstr "Stai sfogliando gli archivi del blog %s."

#. translators: 1: site link, 2: archive month
#: wp-includes/theme-compat/sidebar.php:54
msgid "You are currently browsing the %1$s blog archives for %2$s."
msgstr "Stai sfogliando gli archivi del blog %1$s di %2$s."

#. translators: 1: site link, 2: archive year
#: wp-includes/theme-compat/sidebar.php:62
msgid "You are currently browsing the %1$s blog archives for the year %2$s."
msgstr "Stai sfogliando gli archivi del blog %1$s dell'anno %2$s."

#. translators: %s: email address
#: wp-signup.php:613 wp-signup.php:759
msgid "Check your inbox at %s and click the link given."
msgstr "Controlla la tua posta in arrivo a %s e fai clic sul link."

#. translators: %s: site address
#: wp-signup.php:906
msgid "The site you were looking for, %s, does not exist."
msgstr "Il sito che stavi cercando, %s, non esiste."

#. translators: %s: a link to the embedded site
#: wp-includes/theme-compat/embed-404.php:21
msgid "It looks like nothing was found at this location. Maybe try visiting %s directly?"
msgstr "Sembra che non sia stato trovato nulla a questo indirizzo. Vuoi provare a visitare %s direttamente?"

#. translators: %s: wp-config.php
#: wp-load.php:87
msgid "You can create a %s file through a web interface, but this doesn't work for all server setups. The safest way is to manually create the file."
msgstr "Puoi creare un file %s tramite una interfaccia web ma non funziona con tutte le configurazioni di server. Il metodo più sicuro è creare il file manualmente."

#: wp-load.php:83
msgid "https://codex.wordpress.org/Editing_wp-config.php"
msgstr "https://codex.wordpress.org/Editing_wp-config.php"

#: wp-includes/user.php:2198
msgid "Could not save password reset key to database."
msgstr "Non posso salvare la chiave di reset della password nel database."

#: wp-includes/taxonomy.php:1144 wp-includes/taxonomy.php:1226
msgid "Term meta cannot be added to terms that are shared between taxonomies."
msgstr "I meta non possono essere aggiunti a termini che sono condivisi tra tassonomie."

#. translators: %s: support forums URL
#: wp-includes/wp-db.php:1061
msgid "If you don&#8217;t know how to set up a database you should <strong>contact your host</strong>. If all else fails you may find help at the <a href=\"%s\">WordPress Support Forums</a>."
msgstr "Se non sai come configurare un database <strong>contatta il tuo provider di hosting</strong>. Se tutto il resto non funziona puoi trovare aiuto su <a href=\"%s\">forum di supporto WordPress</a>"

#. translators: %s: database host
#: wp-includes/wp-db.php:1741
msgid "This means that we lost contact with the database server at %s. This could mean your host&#8217;s database server is down."
msgstr "Vuol dire che abbiamo perso il collegamento col server database %s. Potrebbe voler dire che il tuo database è irraggiungibile."

#. translators: 1: wp-config.php. 2: database host
#: wp-includes/wp-db.php:1581
msgid "This either means that the username and password information in your %1$s file is incorrect or we can&#8217;t contact the database server at %2$s. This could mean your host&#8217;s database server is down."
msgstr "Questo potrebbe voler dire che nome utente e password nel file %1$s sono sbagliate o che non possiamo contattare il database %2$s. Potrebbe voler dire che il tuo database è irraggiungibile."

#: wp-includes/taxonomy.php:522
msgid "Tags list navigation"
msgstr "Navigazione elenco dei tag"

#: wp-includes/taxonomy.php:522
msgid "Categories list navigation"
msgstr "Navigazione elenco categorie"

#. translators: %s: shortcode tag
#: wp-includes/shortcodes.php:294
msgid "Attempting to parse a shortcode without a valid callback: %s"
msgstr "È stata tentata l'espansione di uno shortcode senza che vi fosse una funzione callback valida: %s"

#: wp-includes/wp-db.php:1587
msgid "Are you sure you have the correct username and password?"
msgstr "Sei sicuro di avere nome utente e password corretti?"

#. translators: %s: database name
#: wp-includes/wp-db.php:1037
msgid "We were able to connect to the database server (which means your username and password is okay) but not able to select the %s database."
msgstr "Siamo riusciti a connetterci al server del database (il che significa che il tuo nome utente e password sono ok), ma non siamo riusciti a selezionare il database %s."

#: wp-includes/user.php:147
msgid "<strong>ERROR</strong>: Invalid username."
msgstr "<strong>ERRORE</strong>: nome utente non valido."

#. translators: %s: template name
#: wp-includes/theme-compat/sidebar.php:15
#: wp-includes/theme-compat/header.php:15
#: wp-includes/theme-compat/footer.php:15
#: wp-includes/theme-compat/comments.php:16
msgid "Please include a %s template in your theme."
msgstr "Includi un modello %s nel tuo tema."

#: wp-includes/script-loader.php:708
msgid "Public, Sticky"
msgstr "Pubblico, in evidenza"

#. translators: 1: Taxonomy term slug
#: wp-includes/taxonomy.php:2743
msgid "The slug &#8220;%s&#8221; is already in use by another term."
msgstr "L'abbreviazione &#8220;%s&#8221; è già utilizzata da un altro termine"

#. translators: %s: site name
#: wp-signup.php:471
msgid "The site %s is yours."
msgstr "Il sito %s è di tua proprietà."

#. translators: 1: the id argument, 2: sidebar name, 3: recommended id value
#: wp-includes/widgets.php:262
msgid "No %1$s was set in the arguments array for the \"%2$s\" sidebar. Defaulting to \"%3$s\". Manually set the %1$s to \"%3$s\" to silence this notice and keep existing sidebar content."
msgstr "Nessun %1$s è stato impostato nell'array degli argomenti per la sidebar \"%2$s\". Si utilizzerà il valore predefinito \"%3$s\". Impostare manualmente %1$s a \"%3$s\" per nascondere questo avvertimento e mantenere il contenuto attuale della barra laterale."

#: wp-login.php:339
msgid "<strong>ERROR</strong>: Invalid username or email."
msgstr "<strong>ERRORE</strong>: nome utente o email non validi."

#: wp-login.php:993
msgid "Registration complete. Please check your email."
msgstr "Registrazione completata. Verifica la tua email."

#: wp-login.php:989
msgid "Check your email for the confirmation link."
msgstr "Controllare la propria casella email per il link di conferma."

#: wp-includes/script-loader.php:714
msgid "Permalink saved"
msgstr "Permalink salvato"

#: wp-includes/widgets/class-wp-widget-pages.php:27
msgid "A list of your site&#8217;s Pages."
msgstr "La lista delle pagine del tuo sito."

#: wp-includes/user.php:1509
msgid "Nicename may not be longer than 50 characters."
msgstr "Il nicename non può essere più lungo di 50 caratteri."

#. translators: %s: support forums URL
#: wp-includes/wp-db.php:1594 wp-includes/wp-db.php:1752
msgid "If you&#8217;re unsure what these terms mean you should probably contact your host. If you still need help you can always visit the <a href=\"%s\">WordPress Support Forums</a>."
msgstr "Se non sei sicuro di cosa vogliano dire questi termini prova a contattare il tuo fornitore di hosting. Se hai ancora bisogno di aiuto puoi sempre visitare il <a href=\"%s\">forum di supporto di WordPress</a>."

#: wp-includes/script-loader.php:756 wp-includes/script-loader.php:762
msgid "Changes saved."
msgstr "Cambiamenti salvati"

#. translators: %s: comments count
#: wp-includes/script-loader.php:658
msgid "Comments (%s)"
msgstr "Commenti (%s)"

#. translators: %s: database name
#: wp-includes/wp-db.php:1053
msgid "On some systems the name of your database is prefixed with your username, so it would be like <code>username_%1$s</code>. Could that be the problem?"
msgstr "In alcuni sistemi il nome del tuo database ha il tuo nome utente come prefisso, ovvero <code>username_%1$s</code>. Potrebbe essere questo il problema?"

#: wp-includes/taxonomy.php:2144
msgid "A term with the name provided already exists in this taxonomy."
msgstr "In questa tassonomia esiste già un termine con questo nome."

#: wp-includes/script-loader.php:788
msgid "Update Failed!"
msgstr "Aggiornamento non riuscito!"

#: wp-includes/widgets/class-wp-widget-recent-posts.php:142
msgid "Display post date?"
msgstr "Visualizzare la data dell’articolo?"

#: wp-includes/update.php:154 wp-includes/update.php:346
#: wp-includes/update.php:528
msgid "(WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.)"
msgstr "(WordPress non può stabilire una connessione sicura a WordPress.org. Contatta l’amministratore del server.)"

#. translators: 1: Browser cookie documentation URL, 2: Support forums URL
#: wp-login.php:920
msgid "<strong>ERROR</strong>: Cookies are blocked due to unexpected output. For help, please see <a href=\"%1$s\">this documentation</a> or try the <a href=\"%2$s\">support forums</a>."
msgstr "<strong>ERRORE</strong>: i cookie sono bloccati a causa di un output inaspettato. Per un aiuto, vedi  <a href=\"%1$s\">questa documentazione</a> oppure prova nei <a href=\"%2$s\">forum di supporto</a>."

#: wp-includes/script-loader.php:883
msgid "Could not load the preview image. Please reload the page and try again."
msgstr "Impossibile caricare l'immagine di anteprima. Ricarica la pagina e riprova."

#: wp-includes/script-loader.php:568
msgid "Site Preview"
msgstr "Anteprima sito"

#: wp-includes/widgets/class-wp-widget-tag-cloud.php:147
msgid "The tag cloud will not be displayed since there are no taxonomies that support the tag cloud widget."
msgstr "La tag cloud non verrà visualizzata fino a che non vi sono tassonomie che supportano il widget della tag cloud."

#: wp-includes/user.php:2360
msgid "<strong>ERROR</strong>: Sorry, that username is not allowed."
msgstr "<strong>ERRORE</strong>: nome utente non permesso."

#: wp-includes/wp-db.php:1042
msgid "Are you sure it exists?"
msgstr "Sei sicuro che esista?"

#. translators: %s: Codex URL
#: wp-load.php:82
msgid "Need more help? <a href='%s'>We got it</a>."
msgstr "Hai bisogno di ulteriore aiuto? <a href='%s'>Chiedicelo</a>."

#. translators: %s: wp-config.php
#: wp-load.php:77
msgid "There doesn't seem to be a %s file. I need this before we can get started."
msgstr "Sembra che il file %s non esista. Mi serve per poter iniziare."

#: wp-includes/widgets/class-wp-widget-archives.php:82
msgid "Select Year"
msgstr "Seleziona anno"

#: wp-includes/widgets/class-wp-widget-archives.php:88
msgid "Select Day"
msgstr "Seleziona giorno"

#: wp-includes/widgets/class-wp-widget-meta.php:29
msgid "Login, RSS, &amp; WordPress.org links."
msgstr "Aggiungi i link di login, RSS e WordPress.org."

#: wp-includes/taxonomy.php:513
msgid "View Tag"
msgstr "Visualizza tag"

#: wp-includes/taxonomy.php:511
msgid "Parent Category:"
msgstr "Categoria genitore:"

#: wp-login.php:402
msgid "The email could not be sent."
msgstr "Impossibile inviare l’email."

#: wp-mail.php:60
msgid "There doesn&#8217;t seem to be any new mail."
msgstr "Sembra non ci siano nuove email."

#: wp-includes/widgets/class-wp-widget-links.php:127
msgid "Select Link Category:"
msgstr "Seleziona la categoria del link:"

#. translators: 1: shortcode name, 2: space separated list of reserved
#. characters
#: wp-includes/shortcodes.php:74
msgid "Invalid shortcode name: %1$s. Do not use spaces or reserved characters: %2$s"
msgstr "Nome shortcode non valido: %1$s. Non utilizzare spazi o i caratteri riservati: %2$s"

#: wp-login.php:944
msgid "You have logged in successfully."
msgstr "Ti sei autenticato correttamente."

#: wp-signup.php:763
msgid "If you haven&#8217;t received your email yet, there are a number of things you can do:"
msgstr "Se non hai ancora ricevuta l'email, ci sono una serie di cose che puoi fare:"

#. translators: 1: display name, 2: user_login
#: wp-includes/user.php:1126
msgctxt "user dropdown"
msgid "%1$s (%2$s)"
msgstr "%1$s (%2$s)"

#: wp-login.php:981
msgid "Your session has expired. Please log in to continue where you left off."
msgstr "La sessione è scaduta. Accedi di nuovo per continuare da dove eri rimasto."

#: wp-includes/script-loader.php:572
msgid "(Untitled)"
msgstr "(Senza titolo)"

#: wp-includes/script-loader.php:676
msgid "Drag boxes here"
msgstr "Trascina qui i riquadri"

#: wp-includes/taxonomy.php:427
msgid "Unregistering a built-in taxonomy is not allowed."
msgstr "Non è permesso de-registrare una tassonomia incorporata "

#: wp-includes/script-loader.php:769
msgid "Are you sure you want to install this plugin?"
msgstr "Sei davvero sicuro di voler installare questo plugin?"

#: wp-login.php:831 wp-login.php:1069 wp-includes/user.php:149
#: wp-includes/user.php:175 wp-includes/user.php:227 wp-includes/user.php:247
msgid "Lost your password?"
msgstr "Password dimenticata?"

#: wp-includes/script-loader.php:768
msgid "Plugin details"
msgstr "Dettagli del plugin"

#: wp-includes/script-loader.php:767
msgid "Plugin:"
msgstr "Plugin:"

#: wp-includes/script-loader.php:528
msgid "Link selected."
msgstr "Link selezionato."

#: wp-includes/script-loader.php:529
msgid "Link inserted."
msgstr "Link inserito."

#: wp-includes/theme.php:2963
msgid "Customizer"
msgstr "Personalizza"

#. translators: Do not translate USERNAME, ADMIN_EMAIL, EMAIL, SITENAME,
#. SITEURL: those are placeholders.
#: wp-includes/user.php:1877
msgid ""
"Hi ###USERNAME###,\n"
"\n"
"This notice confirms that your password was changed on ###SITENAME###.\n"
"\n"
"If you did not change your password, please contact the Site Administrator at\n"
"###ADMIN_EMAIL###\n"
"\n"
"This email has been sent to ###EMAIL###\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao ###USERNAME###,\n"
"\n"
"questo messaggio è la conferma che la tua password è stata modificata su ###SITENAME###.\n"
"\n"
"Se non hai modificato la tua password, contatta l’amministratore del sito all’email\n"
"###ADMIN_EMAIL###\n"
"\n"
"Questa email è stata inviata a ###EMAIL###\n"
"\n"
"Saluti,\n"
"il team di ###SITENAME###\n"
"###SITEURL###"

#: wp-includes/script-loader.php:851
msgid "Connection lost or the server is busy. Please try again later."
msgstr "Connessione persa oppure il server è occupato. Riprova più tardi."

#. translators: 1: number of comments, 2: post title
#: wp-includes/theme-compat/comments.php:40
msgid "%1$s response to %2$s"
msgid_plural "%1$s responses to %2$s"
msgstr[0] "%1$s risposta a %2$s "
msgstr[1] "%1$s risposte a %2$s "

#: wp-signup.php:321
msgid "Sites you are already a member of:"
msgstr "Siti di cui sei già membro:"

#: wp-signup.php:345
msgid "Create Site"
msgstr "Crea sito"

#: wp-includes/widgets/class-wp-widget-meta.php:59
msgid "Entries <abbr title=\"Really Simple Syndication\">RSS</abbr>"
msgstr "<abbr title=\"Really Simple Syndication\">RSS</abbr> degli articoli"

#: wp-includes/script-loader.php:695
msgid "Schedule for:"
msgstr "Pianifica per:"

#: wp-login.php:118
msgid "Powered by WordPress"
msgstr "Powered by WordPress"

#. translators: %s: POP3 error
#: wp-mail.php:238
msgid "Oops: %s"
msgstr "Ops: %s"

#: wp-signup.php:238
msgid "Email&nbsp;Address:"
msgstr "Indirizzo&nbsp;email:"

#: wp-signup.php:761
msgid "Still waiting for your email?"
msgstr "Stai ancora aspettando l’email?"

#: wp-signup.php:867
msgid "User registration has been disabled."
msgstr "La registrazione utenti è stata disabilitata."

#: wp-includes/user.php:2371
msgid "<strong>ERROR</strong>: This email is already registered, please choose another one."
msgstr "<strong>ERRORE</strong>: questa email è già registrata, scegli un indirizzo email diverso."

#: wp-signup.php:180
msgid "Yes"
msgstr "Sì"

#: wp-signup.php:312
msgid "There was a problem, please correct the form below and try again."
msgstr "Si era verificato un problema, correggi il modulo sottostante e riprova."

#: wp-includes/widgets/class-wp-widget-meta.php:73
msgid "Powered by WordPress, state-of-the-art semantic personal publishing platform."
msgstr "Powered by WordPress, allo stato dell’arte una piattaforma di editoria personale semantica-"

#: wp-login.php:683 wp-login.php:730
msgid "Reset Password"
msgstr "Reimposta password"

#: wp-login.php:675
msgid "Password Reset"
msgstr "Reimpostazione password"

#: wp-login.php:675
msgid "Your password has been reset."
msgstr "La password è stata reimpostata."

#: wp-login.php:362
msgid "Someone has requested a password reset for the following account:"
msgstr "Qualcuno ha richiesto la reimpostazione della password per il seguente account."

#. translators: Password reset email subject. %s: Site name
#: wp-login.php:372
msgid "[%s] Password Reset"
msgstr "Reimpostazione password per [%s] "

#: wp-includes/user.php:2172
msgid "Password reset is not allowed for this user"
msgstr "La reimpostazione della password non è consentita per questo utente."

#: wp-login.php:578
msgid "Please enter your username or email address. You will receive a link to create a new password via email."
msgstr "Inserisci il tuo nome utente o il tuo indirizzo email. Riceverai tramite email un link per creare una nuova password."

#: wp-includes/theme-compat/comments.php:24
msgid "This post is password protected. Enter the password to view comments."
msgstr "Questo articolo è protetto da password. Inserisci la password per visualizzare i commenti."

#: wp-includes/user.php:2351
msgid "<strong>ERROR</strong>: This username is invalid because it uses illegal characters. Please enter a valid username."
msgstr "<strong>ERRORE</strong>: questo nome utente non è valido perché utilizza dei caratteri non ammessi. Inserisci un nome utente valido."

#: wp-includes/widgets.php:1514
msgid "Enter the RSS feed URL here:"
msgstr "Inserisci l'URL del feed RSS:"

#: wp-includes/taxonomy.php:2165
msgid "Could not insert term into the database."
msgstr "Non è possibile inserire il termine nel database"

#: wp-includes/user.php:2349
msgid "<strong>ERROR</strong>: Please enter a username."
msgstr "<strong>ERRORE</strong>: inserisci un nome utente."

#: wp-includes/widgets.php:1517
msgid "Give the feed a title (optional):"
msgstr "Inserisci un titolo per il feed RSS (opzionale):"

#: wp-includes/widgets.php:1536
msgid "Display item date?"
msgstr "Visualizza la data delle voci?"

#: wp-includes/widgets.php:1520
msgid "How many items would you like to display?"
msgstr "Quante voci vuoi visualizzare?"

#: wp-includes/widgets.php:1533
msgid "Display item author if available?"
msgstr "Se disponibile, visualizza l'autore delle voci?"

#: wp-includes/script-loader.php:833
msgid "Are you sure you want to delete the selected plugins and their data?"
msgstr "Desideri eliminare i plugin selezionati ed i loro dati?"

#: wp-includes/script-loader.php:803
msgid "Updates may not complete if you navigate away from this page."
msgstr "Gli aggiornamenti potrebbero non completarsi se navighi via da questa pagina."

#: wp-includes/script-loader.php:834
msgid "Caution: These themes may be active on other sites in the network. Are you sure you want to proceed?"
msgstr "Attenzione: questi temi potrebbero essere attivi su altri siti del network. Desideri procedere?"

#. translators: %s: Error string for a failed deletion
#: wp-includes/script-loader.php:837
msgid "Deletion failed: %s"
msgstr "Eliminazione fallita: %s"

#: wp-includes/script-loader.php:1138
msgid "Today"
msgstr "Oggi"

#: wp-includes/wp-db.php:1849 wp-includes/wp-db.php:1855
msgid "Unable to retrieve the error message from MySQL"
msgstr "Impossibile ottenere il messaggio di errore da MySQL"

#. translators: %s: site title
#: wp-login.php:254
msgctxt "site"
msgid "&larr; Back to %s"
msgstr "&larr; Torna a %s"

#. translators: %s: Theme name
#: wp-includes/script-loader.php:830
msgid "Are you sure you want to delete %s?"
msgstr "Desideri eliminare %s?"

#: wp-includes/script-loader.php:655
msgid ""
"Are you sure you want to do this?\n"
"The comment changes you made will be lost."
msgstr ""
"Desideri farlo?\n"
"Perderai le modifiche fatte al commento."

#: wp-includes/script-loader.php:779
msgid "You do not appear to have any plugins available at this time."
msgstr "In questo momento non hai plugin disponibili."

#. translators: %s: Plugin name
#: wp-includes/script-loader.php:832
msgid "Are you sure you want to delete %s and its data?"
msgstr "Desideri eliminare %s e i suoi dati?"

#: wp-login.php:706 wp-includes/script-loader.php:507
msgid "Confirm use of weak password"
msgstr "Conferma l'uso della password debole."

#. translators: %s: Search string
#: wp-includes/script-loader.php:777
msgid "Search results for &#8220;%s&#8221;"
msgstr "Risultati della ricerca per &#8220;%s&#8221;"

#: wp-includes/script-loader.php:780
msgid "Please select at least one item to perform this action on."
msgstr "Seleziona almeno un elemento per effettuare l'azione."

#: wp-includes/script-loader.php:785
msgid "Update Now"
msgstr "Aggiorna ora"

#: wp-includes/script-loader.php:804
msgid "Install Now"
msgstr "Installa ora"

#. translators: %s: Error string for a failed installation
#: wp-includes/script-loader.php:812
msgid "Installation failed: %s"
msgstr "Installazione fallita: %s"

#: wp-includes/script-loader.php:853
msgid "Number of plugins found: %d"
msgstr "Numero di plugin trovati: %d"

#: wp-includes/script-loader.php:854
msgid "No plugins found. Try a different search."
msgstr "Nessun plugin trovato. Prova una ricerca differente."

#. translators: %s: Plugin name and version
#: wp-includes/script-loader.php:822
msgctxt "plugin"
msgid "%s installation failed"
msgstr "Installazione di %s non riuscita"

#. translators: %s: Theme name and version
#: wp-includes/script-loader.php:824
msgctxt "theme"
msgid "%s installation failed"
msgstr "Installazione di %s non riuscita"

#. translators: %s: Plugin name
#: wp-includes/script-loader.php:844
msgctxt "plugin"
msgid "Network Activate %s"
msgstr "Attiva %s sul network"

#. translators: %s: Activation URL
#: wp-includes/script-loader.php:828
msgid "Importer installed successfully. <a href=\"%s\">Run importer</a>"
msgstr "L'importatore è stato installato. <a href=\"%s\">Avvia l'importazione</a>"

#: wp-includes/script-loader.php:847
msgid "Run Importer"
msgstr "Avvia l'importazione"

#. translators: %s: Importer name
#: wp-includes/script-loader.php:849
msgid "Run %s"
msgstr "Avvia %s"

#: wp-includes/taxonomy.php:2055 wp-includes/taxonomy.php:2681
msgid "A name is required for this term."
msgstr "È richiesto un nome per questo termine."

#: wp-includes/script-loader.php:778
msgid "Search Results"
msgstr "Risultati della ricerca"

#: wp-includes/script-loader.php:687
msgid "Term removed."
msgstr "Termine rimosso."

#: wp-includes/script-loader.php:686
msgid "Term added."
msgstr "Termine aggiunto."

#: wp-includes/script-loader.php:685
msgid "Term selected."
msgstr "Termine selezionato."

#: wp-includes/script-loader.php:684
msgid "Remove term:"
msgstr "Rimuovi il termine:"

#: wp-trackback.php:125
msgid "We already have a ping from that URL for this post."
msgstr "Questo articolo ha già ricevuto un ping da questo URL"

#: wp-trackback.php:111
msgid "Sorry, trackbacks are closed for this item."
msgstr "I trackback per questo elemento sono disabilitati"

#: wp-trackback.php:85
msgid "I really need an ID for this to work."
msgstr "Ho davvero bisogno di un ID per farlo funzionare"

#: wp-login.php:712
msgid "Confirm new password"
msgstr "Conferma la nuova password"

#. translators: %s: the message ID
#: wp-mail.php:246
msgid "Mission complete. Message %s deleted."
msgstr "Missione completata. Il messaggio %s è stato eliminato."

#. translators: %s: Theme name and version
#: wp-includes/script-loader.php:816
msgctxt "theme"
msgid "Installing %s..."
msgstr "Installazione in corso del tema %s..."

#. translators: %s: Plugin name and version
#: wp-includes/script-loader.php:814
msgctxt "plugin"
msgid "Installing %s..."
msgstr "Installazione in corso del plugin %s..."

#: wp-includes/theme.php:2082
msgctxt "Theme starter content"
msgid "A homepage section"
msgstr "Una sezione della homepage"

#: wp-includes/theme.php:2073
msgctxt "Theme starter content"
msgid "Blog"
msgstr "Blog"

#: wp-includes/theme.php:2063
msgctxt "Theme starter content"
msgid "About"
msgstr "Chi siamo"

#: wp-includes/theme.php:1985 wp-includes/theme.php:2058
msgctxt "Theme starter content"
msgid "Home"
msgstr "Homepage"

#: wp-includes/theme.php:2015
msgctxt "Theme starter content"
msgid "Email"
msgstr "Email"

#: wp-includes/theme.php:2031
msgctxt "Theme starter content"
msgid "Instagram"
msgstr "Instagram"

#: wp-includes/theme.php:2043
msgctxt "Theme starter content"
msgid "Twitter"
msgstr "Twitter"

#: wp-includes/theme.php:2019
msgctxt "Theme starter content"
msgid "Facebook"
msgstr "Facebook"

#: wp-includes/theme.php:2047
msgctxt "Theme starter content"
msgid "Yelp"
msgstr "Yelp"

#: wp-includes/theme.php:1949
msgctxt "Theme starter content"
msgid "Monday&mdash;Friday: 9:00AM&ndash;5:00PM"
msgstr "lunedì&mdash;venerdì: 9:00&ndash;17:00"

#: wp-includes/theme.php:1948
msgctxt "Theme starter content"
msgid "Hours"
msgstr "Orari"

#: wp-includes/theme.php:1947
msgctxt "Theme starter content"
msgid "New York, NY 10001"
msgstr "20100 Milano (MI)"

#: wp-includes/theme.php:1947
msgctxt "Theme starter content"
msgid "123 Main Street"
msgstr "P.zza Duomo 123"

#: wp-includes/theme.php:1946
msgctxt "Theme starter content"
msgid "Address"
msgstr "Indirizzo"

#: wp-includes/theme.php:1944
msgctxt "Theme starter content"
msgid "Find Us"
msgstr "Come trovarci"

#: wp-includes/script-loader.php:582
msgid "This theme doesn&#8217;t support video headers on this page. Navigate to the front page or another page that supports video headers."
msgstr "Su questa pagina questo tema non supporta gli header con video. Naviga alla pagina principale o in un'altra pagina che supporti gli header con video."

#: wp-includes/theme.php:1949
msgctxt "Theme starter content"
msgid "Saturday &amp; Sunday: 11:00AM&ndash;3:00PM"
msgstr "sabato e domenica: 11:00&ndash;15:00"

#: wp-includes/theme.php:2068
msgctxt "Theme starter content"
msgid "Contact"
msgstr "Contatti"

#: wp-includes/theme.php:1967
msgctxt "Theme starter content"
msgid "Categories"
msgstr "Categorie"

#: wp-includes/theme.php:1979
msgctxt "Theme starter content"
msgid "Search"
msgstr "Ricerca"

#: wp-includes/theme.php:1961
msgctxt "Theme starter content"
msgid "Archives"
msgstr "Archivi"

#: wp-includes/theme.php:2077
msgctxt "Theme starter content"
msgid "News"
msgstr "News"

#: wp-includes/theme.php:1976
msgctxt "Theme starter content"
msgid "Recent Posts"
msgstr "Articoli recenti"

#: wp-includes/theme.php:1973
msgctxt "Theme starter content"
msgid "Recent Comments"
msgstr "Commenti recenti"

#: wp-includes/theme.php:1970
msgctxt "Theme starter content"
msgid "Meta"
msgstr "Meta"

#: wp-includes/theme.php:1964
msgctxt "Theme starter content"
msgid "Calendar"
msgstr "Calendario"

#: wp-includes/theme.php:2051
msgctxt "Theme starter content"
msgid "YouTube"
msgstr "YouTube"

#: wp-includes/theme.php:2039
msgctxt "Theme starter content"
msgid "Pinterest"
msgstr "Pinterest"

#: wp-includes/theme.php:2023
msgctxt "Theme starter content"
msgid "Foursquare"
msgstr "Foursquare"

#: wp-includes/theme.php:2035
msgctxt "Theme starter content"
msgid "LinkedIn"
msgstr "LinkedIn"

#: wp-includes/theme.php:2027
msgctxt "Theme starter content"
msgid "GitHub"
msgstr "GitHub"

#: wp-includes/theme.php:2059
msgctxt "Theme starter content"
msgid "Welcome to your site! This is your homepage, which is what most visitors will see when they come to your site for the first time."
msgstr "Benvenuto nel tuo sito! Questa è la tua pagina home, quella che la maggioranza dei visitatori vede quando arrivano per la prima volta sul tuo sito."

#: wp-includes/theme.php:2069
msgctxt "Theme starter content"
msgid "This is a page with some basic contact information, such as an address and phone number. You might also try a plugin to add a contact form."
msgstr "Questa è una pagina con alcune informazioni di base per un contatto, quali un indirizzo ed un n numero di telefono. Puoi anche provare un plugin per aggiungere modulo di contatto."

#: wp-includes/theme.php:2064
msgctxt "Theme starter content"
msgid "You might be an artist who would like to introduce yourself and your work here or maybe you&rsquo;re a business with a mission to describe."
msgstr "Potresti essere un artista che vorrebbe presentare se stesso ed il suo lavoro oppure sei una azienda con una attività da descrivere."

#: wp-includes/theme.php:1398
msgid "Video is playing."
msgstr "Il video è in riproduzione"

#: wp-includes/theme.php:1397
msgid "Video is paused."
msgstr "Il video è in pausa."

#: wp-includes/widgets.php:1399 wp-includes/widgets.php:1508
msgid "RSS Error:"
msgstr "Errore RSS:"

#. translators: %s: support forums URL
#: wp-includes/update.php:152 wp-includes/update.php:344
#: wp-includes/update.php:526
msgid "An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href=\"%s\">support forums</a>."
msgstr "Si è verificato un errore inaspettato. C'è qualche cosa di sbagliato con WordPress.org o con la configurazione di questo server. Se continui ad avere problemi, chiedi aiuto sul <a href=\"%s\">forum di supporto</a>."

#: wp-includes/theme.php:1955
msgctxt "Theme starter content"
msgid "About This Site"
msgstr "Informazioni su questo sito"

#: wp-includes/theme.php:1956
msgctxt "Theme starter content"
msgid "This may be a good place to introduce yourself and your site or include some credits."
msgstr "Questo può essere un buon posto per presentare te stesso ed il tuo sito o per includere alcuni crediti."

#: wp-includes/script-loader.php:570
msgctxt "label for hide controls button without length constraints"
msgid "Hide Controls"
msgstr "Nascondi i controlli"

#: wp-includes/script-loader.php:571
msgctxt "label for hide controls button without length constraints"
msgid "Show Controls"
msgstr "Mostra i controlli"

#: wp-includes/widgets/class-wp-widget-pages.php:134
#: wp-includes/widgets/class-wp-widget-links.php:138
msgid "Sort by:"
msgstr "Ordina per:"

#: wp-login.php:985
msgid "You are now logged out."
msgstr "Disconnessione effettuata."

#: wp-login.php:991
msgid "Check your email for your new password."
msgstr "Controllare la tua email per la tua nuova password."

#: wp-includes/widgets/class-wp-widget-media-gallery.php:54
#: wp-includes/widgets/class-wp-widget-media.php:148
msgid "Title for the widget"
msgstr "Titolo del widget"

#. translators: placeholder is image filename
#: wp-includes/widgets/class-wp-widget-media-image.php:342
msgid "Current image: %s"
msgstr "Immagine attuale: %s"

#: wp-includes/widgets/class-wp-widget-media.php:142
msgid "URL to the media file"
msgstr "URL al file media"

#: wp-includes/widgets/class-wp-widget-media.php:70
msgid "Add to Widget"
msgstr "Aggiungi al widget"

#: wp-includes/widgets/class-wp-widget-media-image.php:26
msgid "Displays an image."
msgstr "Visualizza un'immagine."

#: wp-includes/widgets/class-wp-widget-media-audio.php:26
msgid "Displays an audio player."
msgstr "Visualizza un player audio."

#: wp-includes/widgets/class-wp-widget-media-video.php:26
msgid "Displays a video from the media library or from YouTube, Vimeo, or another provider."
msgstr "Visualizza un video della libreria dei media o da YouTube, Vimeo o altri provider."

#. translators: %s: URL to media library
#: wp-includes/widgets/class-wp-widget-media.php:73
msgid "We can&#8217;t find that file. Check your <a href=\"%s\">media library</a> and make sure it wasn&#8217;t deleted."
msgstr "Non riusciamo a trovare questo file. Controlla la tua <a href=\"%s\">libreria dei media</a> e assicurati di non averlo eliminato."

#: wp-signup.php:834
msgid "The network currently allows site registrations."
msgstr "Al momento il network permette le registrazioni al sito."

#: wp-signup.php:831
msgid "The network currently disallows registrations."
msgstr "Al momento il network non permette le registrazioni."

#: wp-includes/widgets/class-wp-widget-media.php:135
msgid "Attachment post ID"
msgstr "ID dell'allegato dell'articolo"

#. translators: %s: video extension
#: wp-includes/widgets/class-wp-widget-media-video.php:91
msgid "URL to the %s video source file"
msgstr "URL del file sorgente per il video %s"

#. translators: %s: URL to media library
#: wp-includes/widgets/class-wp-widget-media-video.php:37
msgid "We can&#8217;t find that video. Check your <a href=\"%s\">media library</a> and make sure it wasn&#8217;t deleted."
msgstr "Non è possibile trovare quel video. Controlla la tua <a href=\"%s\">libreria dei media</a> e assicurati che non sia stato eliminato."

#. translators: %s: URL to media library
#: wp-includes/widgets/class-wp-widget-media-image.php:37
msgid "We can&#8217;t find that image. Check your <a href=\"%s\">media library</a> and make sure it wasn&#8217;t deleted."
msgstr "Non è possibile trovare quell'immagine. Controlla la tua <a href=\"%s\">libreria dei media</a> e assicurati che non sia stata eliminata."

#: wp-includes/widgets/class-wp-widget-media-image.php:335
#: wp-includes/widgets/class-wp-widget-media-audio.php:192
#: wp-includes/widgets/class-wp-widget-media-video.php:235
msgid "Unable to preview media due to an unknown error."
msgstr "Impossibile mostrare una anteprima del media a causa di un errore sconosciuto."

#. translators: %s: audio extension
#: wp-includes/widgets/class-wp-widget-media-audio.php:81
msgid "URL to the %s audio source file"
msgstr "URL del file sorgente per l'audio %s"

#. translators: %s: URL to media library
#: wp-includes/widgets/class-wp-widget-media-audio.php:37
msgid "We can&#8217;t find that audio file. Check your <a href=\"%s\">media library</a> and make sure it wasn&#8217;t deleted."
msgstr "Non è possibile trovare quel file audio. Controlla la tua <a href=\"%s\">libreria dei media</a> e assicurati che non sia stato eliminato."

#: wp-includes/widgets/class-wp-widget-media-audio.php:43
msgid "Looks like this isn&#8217;t the correct kind of file. Please link to an audio file instead."
msgstr "Sembra che questo non sia il tipo di file corretto. Inserisci un link ad un file audio."

#: wp-includes/widgets/class-wp-widget-media.php:79
msgid "Looks like this isn&#8217;t the correct kind of file. Please link to an appropriate file instead."
msgstr "Sembra che questo non sia il tipo di file corretto. Inserisci un link ad un file appropriato."

#: wp-includes/script-loader.php:1191
msgid "An error occurred. Please try again."
msgstr "Si è verificato un errore. Riprova."

#: wp-includes/script-loader.php:1190
msgid "Enter your closest city to find nearby events."
msgstr "Inserisci la città più vicina a te per trovare gli eventi locali."

#: wp-includes/script-loader.php:1192
msgid "Attend an upcoming event near you."
msgstr "Partecipa al prossimo evento vicino a te."

#. translators: %d: widget count
#: wp-includes/widgets/class-wp-widget-media.php:77
msgid "Media Widget (%d)"
msgid_plural "Media Widget (%d)"
msgstr[0] "Widget Media (%d)"
msgstr[1] "Widget Media (%d)"

#: wp-includes/widgets/class-wp-widget-tag-cloud.php:140
msgid "Show tag counts"
msgstr "Mostra il conteggio dei tag"

#: wp-signup.php:840
msgid "The network currently allows both site and user registrations."
msgstr "Il network attualmente consente la registrazione sia dei siti che degli utenti."

#: wp-signup.php:837
msgid "The network currently allows user registrations."
msgstr "Il network attualmente consente la registrazione degli utenti."

#: wp-signup.php:826
msgid "Greetings Network Administrator!"
msgstr "Bentornato Amministratore del network!"

#: wp-includes/taxonomy.php:116
msgid "Edit Link Category"
msgstr "Modifica il link della categoria"

#: wp-includes/script-loader.php:841
msgid "Network Activate"
msgstr "Attiva nel network"

#. translators: %s is the name of the city we couldn't locate. * Replace the
#. examples with cities related to your locale. Test that * they match the
#. expected location and have upcoming events before * including them. If no
#. cities related to your locale have events, * then use cities related to your
#. locale that would be recognizable * to most users. Use only the city name
#. itself, without any region * or country. Use the endonym (native locale
#. name) instead of the * English name if possible.
#: wp-includes/script-loader.php:1209
msgid "We couldn&#8217;t locate %s. Please try another nearby city. For example: Kansas City; Springfield; Portland."
msgstr "Non riusciamo a trovare %s. Prova con un'altra città vicina. Ad esempio: Milano; Torino; Napoli."

#. translators: %s: Theme name
#: wp-includes/script-loader.php:846
msgctxt "theme"
msgid "Network Activate %s"
msgstr "Attiva %s nel network"

#: wp-includes/widgets/class-wp-widget-text.php:514
msgid "New Custom HTML Widget"
msgstr "Nuovo widget HTML personalizzato"

#: wp-includes/widgets/class-wp-widget-custom-html.php:53
msgid "Custom HTML"
msgstr "HTML personalizzato"

#: wp-includes/widgets/class-wp-widget-custom-html.php:271
msgid "Some HTML tags are not permitted, including:"
msgstr "Alcuni tag HTML non sono permessi, inclusi:"

#: wp-includes/widgets/class-wp-widget-text.php:35
msgid "Arbitrary text."
msgstr "Testo arbitrario."

#: wp-includes/widgets/class-wp-widget-custom-html.php:46
msgid "Arbitrary HTML code."
msgstr "Codice HTML arbitrario."

#: wp-includes/widgets/class-wp-widget-text.php:518
msgid "Did you know there is a &#8220;Custom HTML&#8221; widget now? You can find it by scanning the list of available widgets on this screen. Check it out to add some custom code to your site!"
msgstr "Lo sai che ora c'è un nuovo widget &#8220;HTML personalizzato&#8221;? Puoi trovarlo scorrendo la lista dei widget disponibili in questa schermata. Provalo per aggiungere del codice personalizzato al tuo sito!"

#: wp-includes/widgets/class-wp-widget-text.php:516
msgid "Did you know there is a &#8220;Custom HTML&#8221; widget now? You can find it by pressing the &#8220;<a class=\"add-widget\" href=\"#\">Add a Widget</a>&#8221; button and searching for &#8220;HTML&#8221;. Check it out to add some custom code to your site!"
msgstr "Lo sai che ora c'è un nuovo widget di &#8220;HTML personalizzato&#8221;? Puoi trovarlo premendo il pulsante &#8220;<a class=\"add-widget\" href=\"#\">Aggiungi un widget</a>&#8221; e cercando &#8220;HTML personalizzato&#8221;. Provalo per aggiungere del codice personalizzato al tuo sito!"

#: wp-includes/widgets/class-wp-widget-text.php:479
msgid "This widget may contain code that may work better in the &#8220;Custom HTML&#8221; widget. How about trying that widget instead?"
msgstr "Questo widget può contenere codice che potrebbe funzionare meglio nel nuovo widget &#8220;HTML personalizzato&#8221;. Quindi perché non provare questo nuovo widget?"

#: wp-includes/widgets/class-wp-widget-text.php:534
msgid "Hey there, looks like you just pasted HTML into the &#8220;Visual&#8221; tab of the Text widget. You may want to paste your code into the &#8220;Text&#8221; tab instead. Alternately, try out the new &#8220;Custom HTML&#8221; widget!"
msgstr "Sembra che tu abbia incollato testo HTML all'interno della scheda &#8220;Visuale#8221; del widget di testo. Puoi invece incollare il tuo codice nella scheda &#8220;Testo&#8221;. In alternativa puoi provare il nuovo widget &#8220;HTML personalizzato&#8221;!"

#: wp-includes/widgets/class-wp-widget-text.php:533
msgid "Did you just paste HTML?"
msgstr "Hai appena incollato codice HTML?"

#: wp-includes/widgets/class-wp-widget-text.php:481
msgid "This widget may have contained code that may work better in the &#8220;Custom HTML&#8221; widget. If you haven&#8217;t yet, how about trying that widget instead?"
msgstr "Può darsi che questo widget contenesse codice che potrebbe funzionare meglio nel nuovo widget &#8220;HTML personalizzato&#8221;. Se non l'hai già fatto, che ne diresti di provarlo?"

#. translators: %s: the name of a city
#: wp-includes/script-loader.php:1213
msgid "City updated. Listing events near %s."
msgstr "Città aggiornata. L'elenco eventi mostra quelli vicino a %s."

#: wp-includes/widgets/class-wp-widget-media-gallery.php:32
msgctxt "label for button in the gallery widget; should not be longer than ~13 characters long"
msgid "Add Images"
msgstr "Aggiungi immagini"

#: wp-includes/widgets/class-wp-widget-media-gallery.php:34
msgctxt "label for button in the gallery widget; should not be longer than ~13 characters long"
msgid "Edit Gallery"
msgstr "Modifica galleria"

#: wp-includes/taxonomy.php:526
msgid "&larr; Back to Tags"
msgstr "&larr; Torna ai tag"

#: wp-includes/widgets/class-wp-widget-media-image.php:322
msgid "Link to:"
msgstr "Link a:"

#: wp-includes/widgets/class-wp-widget-media-gallery.php:31
msgid "No images selected"
msgstr "Nessuna immagine selezionata"

#. translators: %s: site name
#: wp-login.php:364
msgid "Site Name: %s"
msgstr "Nome del sito: %s"

#: wp-includes/widgets/class-wp-widget-custom-html.php:319
msgid "Custom HTML Widget"
msgstr "Widget HTML personalizzato"

#: wp-includes/script-loader.php:810
msgid "Installation Failed!"
msgstr "Installazione fallita!"

#: wp-includes/script-loader.php:564
msgid "Discard changes"
msgstr "Annulla le modifiche"

#: wp-includes/widgets/class-wp-widget-media-gallery.php:26
msgid "Displays an image gallery."
msgstr "Visualizza una galleria di immagini."

#: wp-includes/widgets/class-wp-widget-media-gallery.php:25
msgid "Gallery"
msgstr "Galleria"

#: wp-includes/wp-db.php:1246
msgid "Unsupported value type (%s)."
msgstr "Tipo di valore non supportato (%s)."

#: wp-includes/taxonomy.php:526
msgid "&larr; Back to Categories"
msgstr "&larr; Torna alle categorie"

#: wp-includes/script-loader.php:868
msgid "Color value"
msgstr "Valore del colore"

#: wp-includes/script-loader.php:574
msgid "Downloading your new theme&hellip;"
msgstr "Scaricamento del tuo nuovo tema&hellip;"

#: wp-includes/script-loader.php:557
msgid "Please save your changes in order to share the preview."
msgstr "Salva le modifiche per condividere l'anteprima"

#: wp-includes/taxonomy.php:2389
msgid "Could not insert term relationship into the database."
msgstr "Impossibile inserire la relazione per il termine nel database."

#. translators: %s: register_widget()
#: wp-includes/widgets.php:1054
msgid "Widgets need to be registered using %s, before they can be displayed."
msgstr "I widget, prima di poter essere visualizzati, necessitano di essere registrati utilizzando %s, ."

#: wp-includes/widgets/class-wp-widget-custom-html.php:292
msgid "Use the Custom HTML widget to add arbitrary HTML code to your widget areas."
msgstr "Utilizza il widget dell'HTML personalizzato per aggiungere del codice HTML nella tua area dei widget."

#: wp-includes/script-loader.php:553
msgid "Updating"
msgstr "Aggiornamento"

#: wp-includes/script-loader.php:591
msgid "Homepage and posts page must be different."
msgstr "La Homepage e la pagina degli articoli devono essere differenti."

#: wp-includes/script-loader.php:575
msgid "Setting up your live preview. This may take a bit."
msgstr "Impostazione della anteprima live.Questo può prendere un po' di tempo."

#: wp-includes/script-loader.php:552
msgid "Draft Saved"
msgstr "Bozza salvata"

#: wp-includes/script-loader.php:605
msgid "Publish Settings"
msgstr "Impostazioni di pubblicazione"

#: wp-includes/script-loader.php:864
msgid "Clear color"
msgstr "Rimuovi colore"

#. translators: Login screen title. 1: Login screen name, 2: Network or site
#. name
#: wp-login.php:62
msgid "%1$s &lsaquo; %2$s &#8212; WordPress"
msgstr "%1$s &lsaquo; %2$s &#8212; WordPress"

#. translators: %s: Display name of the user who has taken over the changeset
#. in customizer.
#: wp-includes/script-loader.php:579
msgid "%s has taken over and is currently customizing."
msgstr "%s ha preso il controllo e sta attualmente personalizzando."

#: wp-includes/script-loader.php:598
msgid "Schedule your customization changes to publish (\"go live\") at a future date."
msgstr "Programma i cambiamenti (\"go Live\") delle tue personalizzazioni ad una specifica data."

#: wp-includes/script-loader.php:573
msgid "Looks like something&#8217;s gone wrong. Wait a couple seconds, and then try again."
msgstr "Sembra che qualcosa sia andato storto. Attendi un paio di secondi, quindi riprova. "

#. translators: 1: number of placeholders, 2: number of arguments passed
#: wp-includes/wp-db.php:1294
msgid "The query does not contain the correct number of placeholders (%1$d) for the number of arguments passed (%2$d)."
msgstr "La query non contiene il numero corretto di segnaposti (%1$d) in relazione al numero di argomenti trasmessi (%2$d)."

#: wp-includes/widgets/class-wp-widget-custom-html.php:313
msgid "Screen reader users: when in forms mode, you may need to press the Esc key twice."
msgstr "Per gli utenti con screen reader: quando in modalità modulo, potrebbe essere necessario usare il tasto Esc due volte."

#: wp-includes/script-loader.php:577
msgid "Are you sure you&#8217;d like to discard your unpublished changes?"
msgstr "Sei sicuro di voler scartare le tue modifiche non pubblicate?"

#. translators: %s: URL to the Customizer to load the autosaved version
#: wp-includes/script-loader.php:581
msgid "There is a more recent autosave of your changes than the one you are previewing. <a href=\"%s\">Restore the autosave</a>"
msgstr "C'è un salvataggio automatico più recente delle tue modifiche, rispetto a quello che stai visualizzando. <a href=\"%s\">Ripristina il salvataggio automatico</a>"

#: wp-includes/widgets/class-wp-nav-menu-widget.php:26
msgid "Add a navigation menu to your sidebar."
msgstr "Aggiungi un menu di navigazione alla barra laterale."

#: wp-includes/script-loader.php:702
msgctxt "post action/button label"
msgid "Schedule"
msgstr "Pianifica"

#. translators: 1: link to new site, 2: login URL, 3: username
#: wp-signup.php:476
msgid "%1$s is your new site. <a href=\"%2$s\">Log in</a> as &#8220;%3$s&#8221; using your existing password."
msgstr "%1$s è il tuo nuovo sito. <a href=\"%2$s\">Accedi</a> come &#8220;%3$s&#8221; utilizzando la tua password esistente."

#: wp-includes/theme.php:2083
msgctxt "Theme starter content"
msgid "This is an example of a homepage section. Homepage sections can be any page other than the homepage itself, including the page that shows your latest blog posts."
msgstr "Questo è un esempio di una sezione della homepage. Le sezioni della homepage possono essere pagine qualsiasi diverse dalla homepage stessa, compresa la pagina che mostra i tuoi ultimi articoli del blog."

#. translators: %s: a list of valid video file extensions
#: wp-includes/widgets/class-wp-widget-media-video.php:44
msgid "Sorry, we can&#8217;t load the video at the supplied URL. Please check that the URL is for a supported video file (%s) or stream (e.g. YouTube and Vimeo)."
msgstr "Spiacenti, non riusciamo a caricare il video all'URL fornito. Verifica che l'URL corrisponda a un file video supportato  (%s) o a servizi streaming abilitati (es. YouTube e Vimeo)."

#. translators: Tab heading when selecting from the most used terms
#: wp-includes/taxonomy.php:525
msgctxt "categories"
msgid "Most Used"
msgstr "Più utilizzate"

#. translators: Tab heading when selecting from the most used terms
#: wp-includes/taxonomy.php:525
msgctxt "tags"
msgid "Most Used"
msgstr "Più utilizzati"

#: wp-includes/script-loader.php:555
msgctxt "customizer changeset status"
msgid "Scheduled"
msgstr "Pianificati"

#. translators: %s: URL to Add Themes admin screen
#: wp-includes/script-loader.php:602
msgid "You won&#8217;t be able to install new themes from here yet since your install requires SFTP credentials. For now, please <a href=\"%s\">add themes in the admin</a>."
msgstr "Non sarai in grado di installare nuovi temi da qui, poiché la tua installazione richiede le credenziali SFTP. Per ora <a href=\"%s\">aggiungi i temi nell'amministrazione</a>."

#: wp-includes/wp-db.php:1283
msgid "The query only expected one placeholder, but an array of multiple placeholders was sent."
msgstr "La query si aspetta un placeholder, ma è stato passato un array di placeholder."

#: wp-includes/user.php:2708
msgid "[%s] New Email Address"
msgstr "[%s] Nuovo indirizzo email"

#: wp-includes/taxonomy.php:123
msgid "&larr; Back to Link Categories"
msgstr "&larr; Torna al link delle categorie"

#. translators: %d: error count
#: wp-includes/script-loader.php:587 wp-includes/script-loader.php:589
#: wp-includes/widgets/class-wp-widget-custom-html.php:217
#: wp-includes/widgets/class-wp-widget-custom-html.php:219
msgid "There is %d error which must be fixed before you can save."
msgid_plural "There are %d errors which must be fixed before you can save."
msgstr[0] "C'è %d errore che deve essere corretto prima che tu possa salvare."
msgstr[1] "Ci sono %d errori che devono essere corretti prima che tu possa salvare."

#: wp-includes/widgets/class-wp-widget-media.php:67
msgctxt "label for button in the media widget"
msgid "Add Media"
msgstr "Aggiungi media"

#: wp-includes/widgets/class-wp-widget-media-video.php:32
msgctxt "label for button in the video widget"
msgid "Add Video"
msgstr "Aggiungi video"

#: wp-includes/widgets/class-wp-widget-media-image.php:32
msgctxt "label for button in the image widget"
msgid "Add Image"
msgstr "Aggiungi immagine "

#: wp-includes/widgets/class-wp-widget-media-audio.php:32
msgctxt "label for button in the audio widget"
msgid "Add Audio"
msgstr "Aggiungi audio"

#: wp-includes/widgets/class-wp-widget-media.php:78
msgid "Media Widget"
msgstr "Widget media"

#: wp-includes/widgets/class-wp-widget-media.php:69
msgctxt "label for button in the media widget; should preferably not be longer than ~13 characters long"
msgid "Edit Media"
msgstr "Modifica media"

#: wp-includes/widgets/class-wp-widget-media.php:68
msgctxt "label for button in the media widget; should preferably not be longer than ~13 characters long"
msgid "Replace Media"
msgstr "Cambia media"

#: wp-includes/widgets/class-wp-widget-media.php:66
msgid "No media selected"
msgstr "Nessun media selezionato"

#: wp-includes/widgets/class-wp-widget-media.php:58
msgid "A media item."
msgstr "Elemento media."

#: wp-includes/widgets/class-wp-widget-media-video.php:42
msgid "Video Widget"
msgstr "Widget video"

#. translators: %d: widget count
#: wp-includes/widgets/class-wp-widget-media-video.php:41
msgid "Video Widget (%d)"
msgid_plural "Video Widget (%d)"
msgstr[0] "Widget video  (%d)"
msgstr[1] "Widget video  (%d)"

#: wp-includes/widgets/class-wp-widget-media-video.php:34
msgctxt "label for button in the video widget; should preferably not be longer than ~13 characters long"
msgid "Edit Video"
msgstr "Modifica video"

#: wp-includes/widgets/class-wp-widget-media-video.php:33
msgctxt "label for button in the video widget; should preferably not be longer than ~13 characters long"
msgid "Replace Video"
msgstr "Sostituisci video"

#: wp-includes/widgets/class-wp-widget-media-image.php:42
msgid "Image Widget"
msgstr "Widget immagine"

#. translators: %d: widget count
#: wp-includes/widgets/class-wp-widget-media-image.php:41
msgid "Image Widget (%d)"
msgid_plural "Image Widget (%d)"
msgstr[0] "Widget immagine (%d)"
msgstr[1] "Widget immagine (%d)"

#: wp-includes/widgets/class-wp-widget-media-image.php:34
msgctxt "label for button in the image widget; should preferably not be longer than ~13 characters long"
msgid "Edit Image"
msgstr "Modifica immagine"

#: wp-includes/widgets/class-wp-widget-media-image.php:33
msgctxt "label for button in the image widget; should preferably not be longer than ~13 characters long"
msgid "Replace Image"
msgstr "Sostituisci immagine"

#: wp-includes/widgets/class-wp-widget-media-audio.php:42
msgid "Audio Widget"
msgstr "Widget audio"

#. translators: %d: widget count
#: wp-includes/widgets/class-wp-widget-media-audio.php:41
msgid "Audio Widget (%d)"
msgid_plural "Audio Widget (%d)"
msgstr[0] "Widget audio (%d)"
msgstr[1] "Widget audio (%d)"

#: wp-includes/widgets/class-wp-widget-media-audio.php:34
msgctxt "label for button in the audio widget; should preferably not be longer than ~13 characters long"
msgid "Edit Audio"
msgstr "Modifica audio"

#: wp-includes/widgets/class-wp-widget-media-audio.php:33
msgctxt "label for button in the audio widget; should preferably not be longer than ~13 characters long"
msgid "Replace Audio"
msgstr "Sostituisci audio"

#: wp-includes/script-loader.php:599
msgid "Sorry, you can&#8217;t preview new themes when you have changes scheduled or saved as a draft. Please publish your changes, or wait until they publish to preview new themes."
msgstr "Non puoi vedere le anteprime dei nuovi temi quando ha delle modifiche programmate o salvate come bozze. Pubblica le tue modifiche o aspetta che vengano pubblicate prima di poter vedere le antepirme dei nuovi temi."

#: wp-includes/script-loader.php:547
msgid "Activate &amp; Publish"
msgstr "Attiva &amp; pubblica"

#: wp-includes/user.php:3453
msgid "Invalid action"
msgstr "Azione non valida"

#: wp-includes/user.php:3252
msgid "Export Personal Data"
msgstr "Esporta dati personali"

#: wp-login.php:845 wp-includes/user.php:3289 wp-includes/user.php:3429
msgid "Invalid request."
msgstr "Richiesta non valida."

#: wp-includes/user.php:3433
msgid "This link has expired."
msgstr "Questo link è scaduto."

#: wp-includes/script-loader.php:669
msgid "No personal data export file was generated."
msgstr "Nessun file di esportazione dei dati personali è stato generato."

#: wp-includes/user.php:3471
msgid "The confirmation email has expired."
msgstr "L'email di conferma è scaduta."

#: wp-includes/user.php:2788
msgid "User ID"
msgstr "ID utente"

#: wp-includes/user.php:2791
msgid "User Email"
msgstr "Email utente"

#: wp-includes/user.php:2798
msgid "User Description"
msgstr "Descrizione utente"

#: wp-includes/user.php:2792
msgid "User URL"
msgstr "URL utente"

#: wp-includes/user.php:2756
msgid "WordPress User"
msgstr "Utente WordPress"

#: wp-includes/user.php:2793
msgid "User Registration Date"
msgstr "Data registrazione utente"

#: wp-includes/user.php:3160
msgid "Action has been confirmed."
msgstr "L'azione è stata confermata."

#: wp-includes/user.php:2797
msgid "User Last Name"
msgstr "Cognome dell'utente"

#: wp-includes/user.php:2796
msgid "User First Name"
msgstr "Nome dell'utente"

#: wp-includes/user.php:2795
msgid "User Nickname"
msgstr "Soprannome dell'utente"

#: wp-includes/script-loader.php:670
msgid "An error occurred while attempting to export personal data."
msgstr "Si è verificato un errore durante il tentativo di esportazione dei dati personali."

#: wp-includes/user.php:3161
msgid "The site administrator has been notified and will fulfill your request as soon as possible."
msgstr "L'amministratore del sito è stato notificato, e soddisferà la tua richiesta il più presto possibile."

#: wp-includes/script-loader.php:667
msgid "Personal data was found for this user but some of the personal data found was not erased."
msgstr "Sono stati trovati dati personali per questo utente, ma alcuni di essi non sono stati rimossi."

#: wp-includes/script-loader.php:666
msgid "Personal data was found for this user but was not erased."
msgstr "I dati personali di questo utente sono stati trovati, ma non sono stati rimossi."

#: wp-includes/script-loader.php:665
msgid "All of the personal data found for this user was erased."
msgstr "Tutti i dati personali trovati di questo utente sono stati rimossi."

#: wp-includes/script-loader.php:664
msgid "No personal data was found for this user."
msgstr "Non sono stati trovati dati personali di questo utente."

#: wp-includes/user.php:3208
msgid "Invalid action name."
msgstr "Nome dell'azione non valido."

#: wp-includes/user.php:3224
msgid "A request for this email address already exists."
msgstr "Esiste già una richiesta per questo indirizzo email."

#: wp-includes/user.php:2794
msgid "User Display Name"
msgstr "Nome utente da visualizzare"

#: wp-login.php:876
msgid "User action confirmed."
msgstr "Azione dell'utente confermata."

#. translators: %s: action name
#: wp-includes/user.php:3259
msgid "Confirm the \"%s\" action"
msgstr "Conferma l'azione \"%s\""

#: wp-includes/script-loader.php:668
msgid "An error occurred while attempting to find and erase personal data."
msgstr "Si è verificato un errore durante il tentativo di trovare e rimuovere i dati personali."

#: wp-includes/user.php:2790
msgid "User Nice Name"
msgstr "Nome utente"

#: wp-includes/user.php:2789
msgid "User Login Name"
msgstr "Login utente"

#. translators: Do not translate DESCRIPTION, CONFIRM_URL, SITENAME, SITEURL:
#. those are placeholders.
#: wp-includes/user.php:3306
msgid ""
"Howdy,\n"
"\n"
"A request has been made to perform the following action on your account:\n"
"\n"
"     ###DESCRIPTION###\n"
"\n"
"To confirm this, please click on the following link:\n"
"###CONFIRM_URL###\n"
"\n"
"You can safely ignore and delete this email if you do not want to\n"
"take this action.\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao,\n"
"\n"
"È stata effettuata una richiesta per eseguire la seguente azione sul tuo account:\n"
"\n"
"     ###DESCRIPTION###\n"
"\n"
"Per confermare questa azione, fai clic sul seguente link:\n"
"###CONFIRM_URL###\n"
"\n"
"Puoi tranquillamente ignorare ed eliminare questa email se non vuoi\n"
"eseguire questa azione.\n"
"\n"
"Questa email è stata inviata a ###EMAIL###.\n"
"\n"
"Saluti,\n"
"a tutti da ###SITENAME###\n"
"###SITEURL###"

#. translators: Privacy data request subject. 1: Site name, 2: Name of the
#. action
#: wp-includes/user.php:3357
msgid "[%1$s] Confirm Action: %2$s"
msgstr "[%1$s] Conferma l'azione: %2$s"

#: wp-includes/user.php:3168
msgid "Thanks for confirming your erasure request."
msgstr "Grazie per aver confermato la tua richiesta di cancellazione."

#: wp-includes/user.php:3165
msgid "Thanks for confirming your export request."
msgstr "Grazie per aver confermato la tua richiesta di esportazione."

#. translators: %s: Site name.
#: wp-includes/user.php:3053
msgid "[%s] Erasure Request Fulfilled"
msgstr "[%s] La richiesta di cancellazione è stata completata"

#: wp-includes/user.php:3169
msgid "The site administrator has been notified. You will receive an email confirmation when they erase your data."
msgstr "L'amministratore del sito è stato notificato. Riceverai una email di conferma quando i tuoi dati saranno cancellati."

#. translators: Do not translate SITENAME, USER_EMAIL, DESCRIPTION, MANAGE_URL,
#. SITEURL; those are placeholders.
#: wp-includes/user.php:2924
msgid ""
"Howdy,\n"
"\n"
"A user data privacy request has been confirmed on ###SITENAME###:\n"
"\n"
"User: ###USER_EMAIL###\n"
"Request: ###DESCRIPTION###\n"
"\n"
"You can view and manage these data privacy requests here:\n"
"\n"
"###MANAGE_URL###\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao,\n"
"\n"
"La tua richiesta di privacy dei dati è stata confermata su ###SITENAME###:\n"
"\n"
"Utente: ###USER_EMAIL###\n"
"Richiesta: ###DESCRIPTION###\n"
"\n"
"Puoi visualizzare e gestire queste richieste sulla privacy dei dati qui:\n"
"\n"
"###MANAGE_URL###\n"
"\n"
"Saluti,\n"
"a tutti da ###SITENAME###\n"
"###SITEURL###"

#: wp-includes/user.php:3166
msgid "The site administrator has been notified. You will receive a link to download your export via email when they fulfill your request."
msgstr "L'amministratore del sito è stato notificato. Riceverai un link via email per scaricare i dati quando la tua richiesta verrà soddisfatta."

#. translators: %s: network settings URL
#: wp-signup.php:847
msgid "To change or disable registration go to your <a href=\"%s\">Options page</a>."
msgstr "Per cambiare o disabilitare la tua registrazione vai alla <a href=\"%s\">pagina delle opzioni</a>."

#. translators: Do not translate SITENAME, SITEURL; those are placeholders.
#: wp-includes/user.php:3080
msgid ""
"Howdy,\n"
"\n"
"Your request to erase your personal data on ###SITENAME### has been completed.\n"
"\n"
"If you have any follow-up questions or concerns, please contact the site administrator.\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao, \n"
"\n"
"La richiesta di cancellazione dei tuoi dati personali su ###SITENAME### è stata completata.\n"
"\n"
"In caso di domande o altri chiarimenti, contatta l'amministratore del sito. \n"
"\n"
"Saluti, \n"
"a tutti da ###SITENAME###\n"
"###SITEURL### "

#. translators: Do not translate SITENAME, SITEURL, PRIVACY_POLICY_URL; those
#. are placeholders.
#: wp-includes/user.php:3093
msgid ""
"Howdy,\n"
"\n"
"Your request to erase your personal data on ###SITENAME### has been completed.\n"
"\n"
"If you have any follow-up questions or concerns, please contact the site administrator.\n"
"\n"
"For more information, you can also read our privacy policy: ###PRIVACY_POLICY_URL###\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao,\n"
"\n"
"La richiesta di cancellazione dei tuoi dati personali su ###SITENAME### è stata completata.\n"
"\n"
"Se hai domande o vuoi altri chiarimenti, contatta l'amministratore del sito.\n"
"\n"
"Per ulteriori informazioni, puoi anche leggere la nostra privacy policy: ###PRIVACY_POLICY_URL###\n"
"\n"
"Saluti,\n"
"a tutti da ###SITENAME###\n"
"###SITEURL###"

#. translators: Do not translate USERNAME, ADMIN_EMAIL, NEW_EMAIL, EMAIL,
#. SITENAME, SITEURL: those are placeholders.
#: wp-includes/user.php:1933
msgid ""
"Hi ###USERNAME###,\n"
"\n"
"This notice confirms that your email address on ###SITENAME### was changed to ###NEW_EMAIL###.\n"
"\n"
"If you did not change your email, please contact the Site Administrator at\n"
"###ADMIN_EMAIL###\n"
"\n"
"This email has been sent to ###EMAIL###\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao ###USERNAME###,\n"
"\n"
"Questa notifica ti avvisa che la tua email su ###SITENAME### è stata cambiata in ###NEW_EMAIL###.\n"
"\n"
"Se non hai cambiato tu la tua email, ti preghiamo di contattare l'amministratore del sito all'indirizzo\n"
"###ADMIN_EMAIL###\n"
"\n"
"Questa email è stata inviata a ###EMAIL###\n"
"\n"
"Saluti,\n"
"a tutti da ###SITENAME###\n"
"###SITEURL###"

#. translators: 1: Site name. 2: Name of the confirmed action.
#: wp-includes/user.php:2978
msgid "[%1$s] Action Confirmed: %2$s"
msgstr "[%1$s] Azione confermata: %2$s"

#: wp-includes/script-loader.php:839
msgctxt "theme"
msgid "Deleted!"
msgstr "Tema eliminato!"

#: wp-includes/script-loader.php:838
msgctxt "plugin"
msgid "Deleted!"
msgstr "Plugin eliminato!"

#: wp-includes/script-loader.php:809
msgctxt "theme"
msgid "Installed!"
msgstr "Tema installato!"

#: wp-includes/script-loader.php:808
msgctxt "plugin"
msgid "Installed!"
msgstr "Plugin installato!"

#. translators: %s: Plugin name and version
#: wp-includes/script-loader.php:796
msgctxt "plugin"
msgid "%s update failed"
msgstr "Aggiornamento del plugin %s non riuscito"

#. translators: %s: Plugin name and version
#: wp-includes/script-loader.php:794
msgctxt "plugin"
msgid "%s updated!"
msgstr "Plugin %s aggiornato!"

#. translators: %s: Plugin name and version
#: wp-includes/script-loader.php:792
msgctxt "plugin"
msgid "Updating %s..."
msgstr "Aggiornamento del plugin %s in corso..."

#. translators: %s: Plugin name and version
#: wp-includes/script-loader.php:787
msgctxt "plugin"
msgid "Update %s now"
msgstr "Aggiorna il plugin %s ora"

#: wp-includes/script-loader.php:783
msgctxt "theme"
msgid "Updated!"
msgstr "Tema aggiornato!"

#: wp-includes/script-loader.php:782
msgctxt "plugin"
msgid "Updated!"
msgstr "Plugin aggiornato!"

#. translators: %s: Plugin name
#: wp-includes/script-loader.php:806
msgctxt "plugin"
msgid "Install %s now"
msgstr "Installa il plugin %s ora"

#. translators: Accessibility text
#: wp-includes/script-loader.php:798
msgid "Updating... please wait."
msgstr "Aggiornamento in corso, attendi..."

#: wp-includes/script-loader.php:825
msgid "Installing... please wait."
msgstr "Installazione in corso, attendi..."

#. translators: Accessibility text
#: wp-includes/script-loader.php:800
msgid "Update completed successfully."
msgstr "Aggiornamento completato con successo!"

#: wp-includes/script-loader.php:826
msgid "Installation completed successfully."
msgstr "Installazione completata con successo!"

#: wp-includes/user.php:3255
msgid "Erase Personal Data"
msgstr "Elimina dati personali"

#. translators: %s: Theme name
#: wp-includes/script-loader.php:846
msgctxt "theme"
msgid "Activate %s"
msgstr "Attiva il tema %s"

#. translators: %s: Plugin name
#: wp-includes/script-loader.php:844
msgctxt "plugin"
msgid "Activate %s"
msgstr "Attiva il plugin %s"

#. translators: %s: Plugin name and version
#: wp-includes/script-loader.php:818
msgctxt "plugin"
msgid "%s installed!"
msgstr "Plugin %s installato!"

#. translators: %s: Theme name and version
#: wp-includes/script-loader.php:820
msgctxt "theme"
msgid "%s installed!"
msgstr "Tema %s installato! "

#: wp-includes/script-loader.php:576
msgid "Reverting unpublished changes&hellip;"
msgstr "Ripristino delle modifiche non pubblicate in corso&hellip;"

#: wp-includes/script-loader.php:842
msgid "Network Enable"
msgstr "Abilita il network"

#: wp-includes/script-loader.php:835
msgid "Deleting..."
msgstr "Eliminazione..."

#: wp-includes/script-loader.php:807
msgid "Installing..."
msgstr "Installazione..."

#. translators: %s: New email address
#: wp-includes/user.php:2727
msgid "Your email address has not been updated yet. Please check your inbox at %s for a confirmation email."
msgstr "Il tuo indirizzo email non è stato ancora aggiornato. Per favore controlla la tua casella %s per la email di conferma."

#: wp-login.php:318
msgid "<strong>ERROR</strong>: There is no user registered with that email address."
msgstr "<strong>ERRORE</strong>: l'utente registrato con questo indirizzo email non esiste."

#: wp-login.php:314
msgid "<strong>ERROR</strong>: Enter a username or email address."
msgstr "<strong>ERRORE</strong>: inserisci un nome utente o un indirizzo email."

#: wp-includes/user.php:2645
msgid "<strong>ERROR</strong>: The email address is already used."
msgstr "<strong>ERRORE</strong>: questo indirizzo email è già in uso."

#: wp-includes/user.php:2411
msgid "<strong>ERROR</strong>: Couldn&#8217;t register you&hellip; please contact the <a href=\"mailto:%s\">webmaster</a> !"
msgstr "<strong>ERRORE</strong>: impossibile registrarti&hellip; contatta il <a href=\"mailto:%s\">Webmaster</a>!"

#. translators: Do not translate USERNAME, ADMIN_URL, EMAIL, SITENAME, SITEURL:
#. those are placeholders.
#: wp-includes/user.php:2663
msgid ""
"Howdy ###USERNAME###,\n"
"\n"
"You recently requested to have the email address on your account changed.\n"
"\n"
"If this is correct, please click on the following link to change it:\n"
"###ADMIN_URL###\n"
"\n"
"You can safely ignore and delete this email if you do not want to\n"
"take this action.\n"
"\n"
"This email has been sent to ###EMAIL###\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao ###USERNAME###,\n"
"\n"
"Hai recentemente richiesto di cambiare l'indirizzo email associato al tuo account.\n"
"\n"
"Se ciò è corretto, per confermare, fai clic sul seguente link:\n"
"###ADMIN_URL###\n"
"\n"
"Puoi tranquillamente ignorare e eliminare questa email\n"
"se non vuoi procedere con la richiesta.\n"
"\n"
"Questa email è stata inviata a ###EMAIL###\n"
"\n"
"Saluti,\n"
"###SITENAME###\n"
"###SITEURL###"

#: wp-includes/user.php:2368 wp-includes/user.php:2637
msgid "<strong>ERROR</strong>: The email address isn&#8217;t correct."
msgstr "<strong>ERRORE</strong>: l'indirizzo email non è corretto."

#: wp-includes/user.php:2366
msgid "<strong>ERROR</strong>: Please type your email address."
msgstr "<strong>ERRORE</strong>: inserisci il tuo indirizzo email."

#: wp-includes/user.php:2354
msgid "<strong>ERROR</strong>: This username is already registered. Please choose another one."
msgstr "<strong>ERRORE</strong>: questo nome utente è già registrato. Utilizzane un altro."

#. translators: %s: email address
#: wp-includes/user.php:243
msgid "<strong>ERROR</strong>: The password you entered for the email address %s is incorrect."
msgstr "<strong>ERRORE</strong>: la password che hai inserito per l'indirizzo email %s non è corretta."

#: wp-includes/user.php:225
msgid "<strong>ERROR</strong>: Invalid email address."
msgstr "<strong>ERRORE</strong>: indirizzo email non valido."

#: wp-includes/user.php:207
msgid "<strong>ERROR</strong>: The email field is empty."
msgstr "<strong>ERRORE</strong>: il campo email è vuoto."

#. translators: %s: user name
#: wp-includes/user.php:171
msgid "<strong>ERROR</strong>: The password you entered for the username %s is incorrect."
msgstr "<strong>ERRORE</strong>: la password inserita per il nome utente %s non è corretta."

#. translators: 1: month, 2: day, 3: year, 4: hour, 5: minute
#: wp-includes/script-loader.php:698 wp-includes/script-loader.php:726
msgid "%1$s %2$s, %3$s @ %4$s:%5$s"
msgstr "%2$s %1$s %3$s @ %4$s:%5$s"

#: wp-includes/post.php:747
msgid "Pending Review"
msgstr "In attesa di revisione"

#: wp-includes/revision.php:36
msgid "Excerpt"
msgstr "Riassunto"

#: wp-includes/widgets/class-wp-widget-pages.php:30
#: wp-includes/widgets/class-wp-widget-pages.php:43
#: wp-includes/post-template.php:1184 wp-includes/theme-compat/sidebar.php:89
msgid "Pages"
msgstr "Pagine"

#: wp-includes/revision.php:35
msgid "Content"
msgstr "Contenuto"

#: wp-includes/media.php:3495 wp-includes/media.php:3516
msgid "Add Media"
msgstr "Aggiungi media"

#: wp-includes/script-loader.php:706 wp-includes/post.php:748
#: wp-includes/post.php:768
msgid "Private"
msgstr "Privato"

#: wp-includes/script-loader.php:524 wp-includes/script-loader.php:703
#: wp-includes/script-loader.php:784 wp-includes/media.php:3499
msgid "Update"
msgstr "Aggiorna"

#: wp-includes/post.php:232
msgid "Published <span class=\"count\">(%s)</span>"
msgid_plural "Published <span class=\"count\">(%s)</span>"
msgstr[0] "Pubblicato <span class=\"count\">(%s)</span>"
msgstr[1] "Pubblicati <span class=\"count\">(%s)</span>"

#: wp-includes/post.php:246
msgid "Draft <span class=\"count\">(%s)</span>"
msgid_plural "Drafts <span class=\"count\">(%s)</span>"
msgstr[0] "Bozza <span class=\"count\">(%s)</span>"
msgstr[1] "Bozze <span class=\"count\">(%s)</span>"

#: wp-includes/post.php:260
msgid "Private <span class=\"count\">(%s)</span>"
msgid_plural "Private <span class=\"count\">(%s)</span>"
msgstr[0] "Privato <span class=\"count\">(%s)</span>"
msgstr[1] "Privati <span class=\"count\">(%s)</span>"

#: wp-includes/post.php:267
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] "Cestinato <span class=\"count\">(%s)</span>"
msgstr[1] "Cestinati <span class=\"count\">(%s)</span>"

#: wp-includes/post.php:2410
msgid "Images"
msgstr "Immagini"

#: wp-includes/post.php:1450
msgid "View Post"
msgstr "Visualizza articolo"

#: wp-includes/post.php:1450
msgid "View Page"
msgstr "Visualizza pagina"

#: wp-includes/media.php:3497
msgid "Select"
msgstr "Seleziona"

#: wp-includes/post.php:1449
msgid "New Page"
msgstr "Nuova pagina"

#: wp-includes/post.php:67
msgid "Edit Media"
msgstr "Modifica media"

#: wp-includes/script-loader.php:526 wp-includes/script-loader.php:754
#: wp-includes/widgets/class-wp-widget-recent-posts.php:91
#: wp-includes/media.php:3143
msgid "(no title)"
msgstr "(senza titolo)"

#: wp-includes/post.php:1448
msgid "Edit Post"
msgstr "Modifica articolo"

#: wp-includes/post-template.php:860
msgid "Pages:"
msgstr "Pagine:"

#: wp-includes/option.php:163
msgid "%s is a protected WP option and may not be modified"
msgstr "%s è un opzione protetta di WP e non può essere modificata"

#: wp-includes/script-loader.php:381
msgid "Mute"
msgstr "Muto"

#: wp-includes/media.php:3557
msgid "Edit Gallery"
msgstr "Modifica galleria"

#. translators: 1: blog name, 2: post title
#: wp-includes/pluggable.php:1503
msgid "[%1$s] Comment: \"%2$s\""
msgstr "[%1$s] Commento: \"%2$s\""

#. translators: 1: blog name, 2: post title
#: wp-includes/pluggable.php:1481
msgid "[%1$s] Trackback: \"%2$s\""
msgstr "[%1$s] Trackback: \"%2$s\""

#. translators: 1: blog name, 2: post title
#: wp-includes/pluggable.php:1492
msgid "[%1$s] Pingback: \"%2$s\""
msgstr "[%1$s] Pingback: \"%2$s\""

#. translators: Comment moderation. 1: Comment action URL
#: wp-includes/pluggable.php:1515 wp-includes/pluggable.php:1679
msgid "Spam it: %s"
msgstr "Marca come spam: %s"

#: wp-includes/pluggable.php:1650
msgid "Pingback excerpt: "
msgstr "Riassunto pingback:"

#. translators: Comment moderation. 1: Comment action URL
#: wp-includes/pluggable.php:1668
msgid "Approve it: %s"
msgstr "Approvalo: %s"

#. translators: %s: user login
#: wp-login.php:366 wp-includes/pluggable.php:1833
#: wp-includes/pluggable.php:1897
msgid "Username: %s"
msgstr "Nome utente: %s"

#. translators: Password change notification email subject. %s: Site title
#: wp-includes/pluggable.php:1840
msgid "[%s] New User Registration"
msgstr "[%s] Registrazione di un nuovo utente"

#. translators: Password change notification email subject. %s: Site title
#: wp-includes/pluggable.php:1906
msgid "[%s] Your username and password info"
msgstr "[%s] Il proprio nome utente e password"

#: wp-includes/post-template.php:130
msgid "Protected: %s"
msgstr "Protetto: %s"

#: wp-includes/post-template.php:145
msgid "Private: %s"
msgstr "Privato: %s"

#: wp-includes/post-template.php:866
msgid "Next page"
msgstr "Pagina successiva"

#: wp-includes/post-template.php:867
msgid "Previous page"
msgstr "Pagina precedente"

#: wp-includes/post-template.php:1343
msgid "Home"
msgstr "Home"

#: wp-includes/post.php:3227
msgid "Content, title, and excerpt are empty."
msgstr "Contenuto, titolo e riassunto sono vuoti."

#: wp-includes/post.php:3466
msgid "Could not update post in the database"
msgstr "Impossibile aggiornare l'articolo nel database"

#: wp-includes/revision.php:295
msgid "Cannot create a revision of a revision"
msgstr "Impossibile creare una revisione di una revisione"

#: wp-includes/script-loader.php:92
msgid "Close all open tags"
msgstr "Chiudi tutti i tag aperti"

#: wp-includes/script-loader.php:93
msgid "close tags"
msgstr "chiudi tag"

#: wp-includes/script-loader.php:94
msgid "Enter the URL"
msgstr "Inserisci l'URL"

#: wp-includes/script-loader.php:95
msgid "Enter the URL of the image"
msgstr "Inserisci l'URL dell'immagine"

#: wp-includes/script-loader.php:96
msgid "Enter a description of the image"
msgstr "Inserisci una descrizione per l'immagine"

#: wp-includes/script-loader.php:273
msgid "Next &gt;"
msgstr "Succ. &raquo;"

#: wp-includes/script-loader.php:274
msgid "&lt; Prev"
msgstr "&laquo; Prec."

#: wp-includes/script-loader.php:276
msgid "of"
msgstr "di"

#: wp-includes/script-loader.php:297
msgid "You may only upload 1 file."
msgstr "&Egrave; possibile caricare un solo file."

#: wp-includes/script-loader.php:298
msgid "HTTP error."
msgstr "Errore HTTP."

#: wp-includes/script-loader.php:304
msgid "Security error."
msgstr "Errore di sicurezza."

#: wp-includes/script-loader.php:306
msgid "Upload stopped."
msgstr "Caricamento interrotto."

#: wp-includes/script-loader.php:308
msgid "Crunching&hellip;"
msgstr "Elaborazione&hellip;"

#: wp-includes/script-loader.php:309
msgid "moved to the trash."
msgstr "spostato nel cestino."

#: wp-includes/post.php:95
msgid "Revisions"
msgstr "Revisioni"

#: wp-includes/ms-functions.php:266
msgid "That user does not exist."
msgstr "Questo utente non esiste."

#: wp-includes/ms-functions.php:513
msgid "That username is currently reserved but may be available in a couple of days."
msgstr "Il nome utente è attualmente riservato ma potrebbe rendersi disponibile in un paio di giorni."

#: wp-includes/ms-functions.php:1048
msgid "Invalid activation key."
msgstr "Chiave di attivazione non valida."

#: wp-includes/ms-functions.php:1068
msgid "Could not create user"
msgstr "Impossibile creare l'utente"

#: wp-includes/ms-functions.php:1076
msgid "That username is already activated."
msgstr "Il nome utente è già stato attivato."

#: wp-includes/ms-functions.php:1338
msgid "New User Registration: %s"
msgstr "Registrazione nuovo utente: %s"

#: wp-includes/post.php:96
msgid "Revision"
msgstr "Revisione"

#: wp-includes/post.php:113
msgid "Navigation Menu Items"
msgstr "Elementi del menu di navigazione"

#: wp-includes/post.php:114
msgid "Navigation Menu Item"
msgstr "Elemento del menu di navigazione"

#: wp-includes/post.php:253
msgid "Pending <span class=\"count\">(%s)</span>"
msgid_plural "Pending <span class=\"count\">(%s)</span>"
msgstr[0] "In sospeso <span class=\"count\">(%s)</span>"
msgstr[1] "In sospeso <span class=\"count\">(%s)</span>"

#: wp-includes/ms-load.php:481
msgid "If you&#8217;re still stuck with this message, then check that your database contains the following tables:"
msgstr "Se si continua a visualizzare questo messaggio, verificare che il database contenga le seguenti tabelle:"

#: wp-includes/ms-load.php:114
msgid "This site has been archived or suspended."
msgstr "Questo blog è stato archiviato o sospeso."

#: wp-includes/ms-functions.php:2099
msgid "An error occurred adding you to this site. Back to the <a href=\"%s\">homepage</a>."
msgstr "Si è verificato un errore nell'aggiunta del proprio sito. Tornare alla <a href=\"%s\">homepage</a>."

#: wp-includes/ms-functions.php:2017
msgid "This file is too big. Files must be less than %d KB in size."
msgstr "Il file è troppo grande. I file devono avere una dimensione inferiore a %d KB."

#. translators: New site notification email subject. 1: Network name, 2: New
#. site name
#: wp-includes/ms-functions.php:1608
msgid "New %1$s Site: %2$s"
msgstr "Nuovo %1$s sito: %2$s"

#: wp-includes/ms-functions.php:1295
msgid "New Site Registration: %s"
msgstr "Registrazione nuovo sito: %s"

#: wp-includes/ms-functions.php:1054
msgid "The site is already active."
msgstr "Il sito è già attivo."

#: wp-includes/ms-functions.php:1212
msgid "Could not create site."
msgstr "Impossibile creare il sito."

#: wp-includes/ms-functions.php:668
msgid "That site is currently reserved but may be available in a couple days."
msgstr "Questo sito è attualmente riservato ma potrebbe essere disponibile in un paio di giorni."

#: wp-includes/ms-functions.php:657
msgid "Sorry, that site is reserved!"
msgstr "Questo sito è riservato!"

#: wp-includes/ms-functions.php:653 wp-includes/ms-functions.php:1205
msgid "Sorry, that site already exists!"
msgstr "Questo sito esiste già!"

#: wp-includes/ms-functions.php:625
msgid "Sorry, site names must have letters too!"
msgstr "I nomi dei siti devono avere anche delle lettere!"

#: wp-includes/ms-functions.php:621
msgid "Sorry, you may not use that site name."
msgstr "Non è possibile usare questo nome per il sito."

#: wp-includes/user.php:1486 wp-includes/ms-functions.php:497
msgid "Sorry, that username already exists!"
msgstr "Questo nome utente esiste già!"

#: wp-includes/ms-functions.php:491
msgid "Sorry, that email address is not allowed!"
msgstr "Questo indirizzo email non è consentito!"

#: wp-includes/ms-functions.php:485
msgid "Sorry, usernames must have letters too!"
msgstr "Il nome utente deve avere anche delle lettere!"

#: wp-includes/ms-default-constants.php:139
msgid "<strong>Conflicting values for the constants VHOST and SUBDOMAIN_INSTALL.</strong> The value of SUBDOMAIN_INSTALL will be assumed to be your subdomain configuration setting."
msgstr "<strong>Conflitto fra i valori per le costanti VHOST e SUBDOMAIN_INSTALL.</strong> Verrà preso il valore di SUBDOMAIN_INSTALL come parametro di configurazione dei sottodomini."

#: wp-includes/option.php:1846
msgid "Site URL."
msgstr "URL sito"

#: wp-includes/post.php:1452
msgid "Search Posts"
msgstr "Cerca articoli"

#: wp-includes/post.php:1452
msgid "Search Pages"
msgstr "Cerca pagine"

#: wp-includes/post.php:1447
msgid "Add New Page"
msgstr "Aggiungi nuova pagina"

#: wp-includes/post.php:1448
msgid "Edit Page"
msgstr "Modifica pagina"

#: wp-includes/post.php:1447
msgid "Add New Post"
msgstr "Aggiungi nuovo articolo"

#: wp-includes/post.php:1444
msgctxt "post type general name"
msgid "Posts"
msgstr "Articoli"

#: wp-includes/post.php:1444
msgctxt "post type general name"
msgid "Pages"
msgstr "Pagine"

#. translators: %s: site title
#: wp-includes/pluggable.php:1831
msgid "New user registration on your site %s:"
msgstr "Nuovo utente registrato sul sito %s:"

#: wp-includes/nav-menu.php:404
msgid "The given object ID is not that of a menu item."
msgstr "L'ID dell'oggetto indicato non è quello di un elemento del menu."

#: wp-includes/pluggable.php:1507
msgid "Permalink: %s"
msgstr "Permalink: %s"

#: wp-includes/registration.php:7 wp-includes/registration-functions.php:7
msgid "This file no longer needs to be included."
msgstr "Il file non ha più la necessità di venire incluso"

#: wp-includes/plugin.php:810
msgid "Only a static class method or function can be used in an uninstall hook."
msgstr "Per disinstallare un hook occorre usare solo un metodo di classe statico o una funzione."

#: wp-includes/post-formats.php:68
msgid "Invalid post."
msgstr "Articolo non valido"

#: wp-includes/post.php:1453
msgid "No pages found."
msgstr "Nessun pagina trovata."

#: wp-includes/post.php:1454
msgid "No posts found in Trash."
msgstr "Nessun articolo trovato nel cestino."

#: wp-includes/post.php:1454
msgid "No pages found in Trash."
msgstr "Nessuna pagina presente nel cestino."

#: wp-includes/post.php:3046
msgid "Passing an integer number of posts is deprecated. Pass an array of arguments instead."
msgstr "Passare un numero intero di articoli è deprecato. Passare invece un array di argomenti."

#: wp-includes/post-formats.php:92
msgctxt "Post format"
msgid "Chat"
msgstr "Chat"

#: wp-includes/post-formats.php:93
msgctxt "Post format"
msgid "Gallery"
msgstr "Galleria"

#: wp-includes/post-formats.php:95
msgctxt "Post format"
msgid "Image"
msgstr "Immagine"

#: wp-includes/post-formats.php:97
msgctxt "Post format"
msgid "Status"
msgstr "Stato"

#: wp-includes/post-formats.php:98
msgctxt "Post format"
msgid "Video"
msgstr "Video"

#: wp-includes/post-formats.php:99
msgctxt "Post format"
msgid "Audio"
msgstr "Audio"

#: wp-includes/ms-functions.php:1052
msgid "The user is already active."
msgstr "Questo utente è già attivo."

#: wp-includes/script-loader.php:305
msgid "File canceled."
msgstr "File cancellato."

#: wp-includes/query.php:153 wp-includes/query.php:174
#: wp-includes/query.php:195 wp-includes/query.php:219
#: wp-includes/query.php:243 wp-includes/query.php:267
#: wp-includes/query.php:296 wp-includes/query.php:316
#: wp-includes/query.php:336 wp-includes/query.php:357
#: wp-includes/query.php:377 wp-includes/query.php:406
#: wp-includes/query.php:435 wp-includes/query.php:455
#: wp-includes/query.php:482 wp-includes/query.php:502
#: wp-includes/query.php:522 wp-includes/query.php:542
#: wp-includes/query.php:562 wp-includes/query.php:591
#: wp-includes/query.php:619 wp-includes/query.php:639
#: wp-includes/query.php:659 wp-includes/query.php:679
#: wp-includes/query.php:699 wp-includes/query.php:719
msgid "Conditional query tags do not work before the query is run. Before then, they always return false."
msgstr "I tag condizionali di una query non funzionano prima che la query sia stata eseguita. Prima dell'esecuzione restituiscono sempre il valore False."

#: wp-includes/post-template.php:385
msgid "There is no excerpt because this is a protected post."
msgstr "Non è disponibile alcun riassunto in quanto si tratta di un articolo protetto."

#: wp-includes/post.php:746 wp-includes/post.php:767
msgid "Draft"
msgstr "Bozza"

#: wp-includes/post.php:1453
msgid "No posts found."
msgstr "Nessun articolo trovato."

#: wp-includes/script-loader.php:550 wp-includes/script-loader.php:711
#: wp-includes/post.php:749 wp-includes/post.php:769
msgid "Published"
msgstr "Pubblicato"

#: wp-includes/post.php:1456
msgid "All Posts"
msgstr "Tutti gli articoli"

#: wp-includes/post.php:1456
msgid "All Pages"
msgstr "Tutte le pagine"

#: wp-includes/pluggable.php:1083 wp-includes/pluggable.php:1126
msgid "You should specify a nonce action to be verified by using the first parameter."
msgstr "È necessario specificare una azione nonce da verificare utilizzando il primo parametro."

#: wp-includes/script-loader.php:289
msgid "%s exceeds the maximum upload size for this site."
msgstr "%s supera la dimensione massima di caricamento per questo sito."

#: wp-includes/ms-deprecated.php:404
msgid "<strong>ERROR</strong>: Site URL already taken."
msgstr "<strong>ERRORE</strong>: URL sito già preso."

#: wp-includes/ms-deprecated.php:411
msgid "<strong>ERROR</strong>: problem creating site entry."
msgstr "<strong>ERRORE</strong>: problema nella creazione voce del sito."

#: wp-includes/script-loader.php:310
msgid "&#8220;%s&#8221; has failed to upload."
msgstr "&#8220;%s&#8221; non è stato caricato."

#. translators: Comment moderation. 1: Comment action URL
#: wp-includes/pluggable.php:1513 wp-includes/pluggable.php:1675
msgid "Delete it: %s"
msgstr "Eliminare: %s"

#. translators: Comment moderation. 1: Number of comments awaiting approval
#: wp-includes/pluggable.php:1682
msgid "Currently %s comment is waiting for approval. Please visit the moderation panel:"
msgid_plural "Currently %s comments are waiting for approval. Please visit the moderation panel:"
msgstr[0] "Attualmente è presente %s commento in attesa di approvazione. Visitare il pannello di moderazione:"
msgstr[1] "Attualmente sono presenti %s commenti in attesa di approvazione. Visitare il pannello di moderazione:"

#: wp-includes/post-formats.php:96
msgctxt "Post format"
msgid "Quote"
msgstr "Citazione"

#: wp-includes/script-loader.php:683 wp-includes/script-loader.php:755
#: wp-includes/post.php:4044
msgctxt "tag delimiter"
msgid ","
msgstr ","

#: wp-includes/ms-functions.php:603
msgid "That name is not allowed."
msgstr "Questo nome non è permesso."

#: wp-includes/ms-functions.php:477
msgid "Username must be at least 4 characters."
msgstr "Il nome utente deve essere di almeno 4 caratteri"

#: wp-includes/script-loader.php:97
msgid "text direction"
msgstr "direzione del testo"

#: wp-includes/post-formats.php:90
msgctxt "Post format"
msgid "Standard"
msgstr "Standard"

#: wp-includes/script-loader.php:98
msgid "Toggle Editor Text Direction"
msgstr "Inverti direzione del testo dell'editor"

#: wp-includes/script-loader.php:302
msgid "%s exceeds the maximum upload size for the multi-file uploader when used in your browser."
msgstr "%s supera la dimensione massima di caricamento per il multi-file uploader quando viene utilizzato nel tuo browser."

#. translators: 1: Opening link tag, 2: Closing link tag
#: wp-includes/script-loader.php:301
msgid "Please try uploading this file with the %1$sbrowser uploader%2$s."
msgstr "Prova a caricare il file con %1$s l'uploader del browser%2$s."

#: wp-includes/script-loader.php:293
msgid "Memory exceeded. Please try another smaller file."
msgstr "Memoria esaurita. Prova con un file più piccolo."

#: wp-includes/script-loader.php:294
msgid "This is larger than the maximum size. Please try another."
msgstr "Questo file è più grande della dimensione massima consentita. Prova con un file diverso."

#: wp-includes/script-loader.php:292
msgid "This file is not an image. Please try another."
msgstr "Questo file non è un'immagine. Prova con un file diverso."

#: wp-includes/ms-functions.php:161
msgid "The requested user does not exist."
msgstr "L'utente richiesto non esiste."

#: wp-includes/media.php:3559
msgid "Insert gallery"
msgstr "Inserisci galleria"

#: wp-includes/media.php:3515
msgid "Media Library"
msgstr "Libreria media"

#: wp-includes/media.php:3556
msgid "Create Gallery"
msgstr "Crea galleria"

#: wp-includes/ms-functions.php:1444
msgid "Already Installed"
msgstr "Già installato"

#. translators: New user notification email. 1: User login, 2: User IP address,
#. 3: Settings screen URL
#: wp-includes/ms-functions.php:1323
msgid ""
"New User: %1$s\n"
"Remote IP address: %2$s\n"
"\n"
"Disable these notifications: %3$s"
msgstr ""
"Nuovo utente: %1$s\n"
"Indirizzo IP: %2$s\n"
"\n"
"Disabilita queste notifiche: %3$s"

#. translators: New site notification email. 1: Site URL, 2: User IP address,
#. 3: Settings screen URL
#: wp-includes/ms-functions.php:1280
msgid ""
"New Site: %1$s\n"
"URL: %2$s\n"
"Remote IP address: %3$s\n"
"\n"
"Disable these notifications: %4$s"
msgstr ""
"Nuovo sito: %1$s\n"
"URL: %2$s\n"
"Indirizzo IP: %3$s\n"
"\n"
"Disabilita queste notifiche: %4$s"

#: wp-includes/media.php:3561
msgid "Add to gallery"
msgstr "Aggiungi alla galleria"

#: wp-includes/ms-functions.php:1444
msgid "You appear to have already installed WordPress. To reinstall please clear your old database tables first."
msgstr "Sembra che WordPress sia già installato. Per reinstallarlo bisogna prima cancellare le tabelle dal database."

#: wp-includes/media.php:3560
msgid "Update gallery"
msgstr "Aggiorna galleria"

#: wp-includes/post.php:1459
msgid "Insert into post"
msgstr "Inserisci nell'articolo"

#: wp-includes/media.php:3517
msgid "Create a new gallery"
msgstr "Crea una nuova galleria"

#: wp-includes/media.php:3562
msgid "Add to Gallery"
msgstr "Aggiungi alla galleria"

#: wp-includes/media.php:3563
msgid "Reverse order"
msgstr "Ordine inverso"

#: wp-includes/post.php:2410
msgid "Manage Images"
msgstr "Gestione immagini"

#: wp-includes/post.php:2410
msgid "Image <span class=\"count\">(%s)</span>"
msgid_plural "Images <span class=\"count\">(%s)</span>"
msgstr[0] "Immagine <span class=\"count\">(%s)</span>"
msgstr[1] "Immagini <span class=\"count\">(%s)</span>"

#: wp-includes/post.php:2411
msgid "Manage Audio"
msgstr "Gestione audio"

#: wp-includes/post.php:2411
msgid "Audio <span class=\"count\">(%s)</span>"
msgid_plural "Audio <span class=\"count\">(%s)</span>"
msgstr[0] "Audio <span class=\"count\">(%s)</span>"
msgstr[1] "Audio <span class=\"count\">(%s)</span>"

#: wp-includes/widgets/class-wp-widget-media-video.php:25
#: wp-includes/post.php:2412
msgid "Video"
msgstr "Video"

#: wp-includes/post.php:2412
msgid "Manage Video"
msgstr "Gestione video"

#: wp-includes/post.php:2412
msgid "Video <span class=\"count\">(%s)</span>"
msgid_plural "Video <span class=\"count\">(%s)</span>"
msgstr[0] "Video <span class=\"count\">(%s)</span>"
msgstr[1] "Video <span class=\"count\">(%s)</span>"

#: wp-includes/post.php:1459
msgid "Insert into page"
msgstr "Inserisci nella pagina"

#: wp-includes/post.php:1460
msgid "Uploaded to this page"
msgstr "Caricato in questa pagina"

#: wp-includes/media.php:3521
msgid "All media items"
msgstr "Tutti gli elementi media"

#: wp-includes/post.php:1460
msgid "Uploaded to this post"
msgstr "Caricato in questo articolo"

#: wp-includes/widgets/class-wp-widget-media-audio.php:25
#: wp-includes/post.php:2411
msgid "Audio"
msgstr "Audio"

#: wp-includes/media.php:3520
msgid "&#8592; Return to library"
msgstr "&#8592; Torna alla libreria"

#: wp-includes/media.php:3512
msgid "Upload Images"
msgstr "Caricamento immagini"

#: wp-includes/ms-functions.php:2101
msgid "WordPress &rsaquo; Success"
msgstr "WordPress &rsaquo; Successo"

#. translators: This is a would-be plural string used in the media manager. If
#. there is not a word you can use in your language to avoid issues with the
#. lack of plural support here, turn it into "selected: %d" then translate it.
#: wp-includes/media.php:3507
msgid "%d selected"
msgstr "%d selezionati"

#: wp-includes/media.php:3549
msgid "Insert from URL"
msgstr "Inserisci da URL"

#: wp-includes/script-loader.php:497
msgctxt "password strength"
msgid "Very weak"
msgstr "Molto debole"

#: wp-includes/script-loader.php:498
msgctxt "password strength"
msgid "Weak"
msgstr "Debole"

#: wp-includes/script-loader.php:165
msgid "Your session has expired. You can log in again from this page or go to the login page."
msgstr "La tua sessione &egrave; scaduta. Puoi loggarti nuovamente da questa pagina o attraverso la pagina di login."

#: wp-includes/script-loader.php:365
msgid "Download Video"
msgstr "Scarica il video"

#: wp-includes/script-loader.php:363
msgid "Turn off Fullscreen"
msgstr "Disattiva lo schermo intero"

#: wp-includes/script-loader.php:364
msgid "Go Fullscreen"
msgstr "Vai a tutto schermo"

#: wp-includes/script-loader.php:375
msgid "Captions/Subtitles"
msgstr "Didascalie/Sottotitoli"

#: wp-includes/post.php:786
msgid "Failed"
msgstr "Fallito"

#: wp-includes/post-template.php:1821
msgid "JavaScript must be enabled to use this feature."
msgstr "Per utilizzare questa funzionalità occorre che JavaScript sia abilitato."

#: wp-includes/post-template.php:273
msgid "(more&hellip;)"
msgstr "(altro&hellip;)"

#: wp-includes/media.php:3577
msgid "Cropping&hellip;"
msgstr "Ritaglio&hellip;"

#: wp-includes/media.php:3566
msgid "Image Details"
msgstr "Dettagli Immagine"

#: wp-includes/media.php:3607
msgid "Drag and drop to reorder videos."
msgstr "Trascina e rilascia per riordinare i video."

#: wp-includes/media.php:3518
msgid "Create a new playlist"
msgstr "Crea una nuova playlist"

#: wp-includes/media.php:3519
msgid "Create a new video playlist"
msgstr "Crea una nuova playlist video"

#: wp-includes/media.php:3567
msgid "Replace Image"
msgstr "Sostituisci Immagine"

#: wp-includes/media.php:3613
msgid "Add to video playlist"
msgstr "Aggiungi alla playlist video"

#: wp-includes/nav-menu.php:409
msgid "Invalid menu ID."
msgstr "ID menu non valido."

#: wp-includes/media.php:3580
msgid "There has been an error cropping your image."
msgstr "Si è verificato un errore durante il ridimensionamento dell&#8217;immagine."

#: wp-includes/media.php:3599
msgid "Edit Audio Playlist"
msgstr "Modifica playlist audio"

#: wp-includes/media.php:3601
msgid "Insert audio playlist"
msgstr "Inserisci playlist audio"

#: wp-includes/media.php:3604
msgid "Add to Audio Playlist"
msgstr "Aggiungi alla playlist audio"

#: wp-includes/media.php:3603
msgid "Add to audio playlist"
msgstr "Aggiungi alla playlist audio"

#: wp-includes/media.php:3602
msgid "Update audio playlist"
msgstr "Aggiorna playlist audio"

#: wp-includes/post-formats.php:91
msgctxt "Post format"
msgid "Aside"
msgstr "Digressione"

#: wp-includes/post.php:65
msgctxt "add new from admin bar"
msgid "Media"
msgstr "Media"

#: wp-includes/user.php:1482 wp-includes/ms-functions.php:480
msgid "Username may not be longer than 60 characters."
msgstr "Il nome utente non deve essere più lungo di 60 caratteri."

#: wp-includes/pluggable.php:1898
msgid "To set your password, visit the following address:"
msgstr "Per impostare la tua password, visita il seguente indirizzo:"

#: wp-includes/script-loader.php:106
msgid "Close blockquote tag"
msgstr "Chiudi il  tag citazione"

#: wp-includes/script-loader.php:119
msgid "Close code tag"
msgstr "Chiudi il tag code"

#: wp-includes/script-loader.php:103
msgid "Close italic tag"
msgstr "Chiudi il tag corsivo"

#: wp-includes/script-loader.php:115
msgid "Close numbered list tag"
msgstr "Chiudi il tag di elenco numerato"

#: wp-includes/script-loader.php:113
msgid "Close bulleted list tag"
msgstr "Chiudi il tag di elenco puntato"

#: wp-includes/script-loader.php:110
msgid "Close inserted text tag"
msgstr "Chiudi il tag di testo"

#: wp-includes/script-loader.php:101
msgid "Close bold tag"
msgstr "Chiudi il tag bold"

#: wp-includes/script-loader.php:109
msgid "Inserted text"
msgstr "Testo inserito"

#: wp-includes/post-template.php:1610
msgid "This content is password protected. To view it please enter your password below:"
msgstr "Il contenuto è protetto da password. Per visualizzarlo inserisci di seguito la password:"

#: wp-includes/post.php:239
msgid "Scheduled <span class=\"count\">(%s)</span>"
msgid_plural "Scheduled <span class=\"count\">(%s)</span>"
msgstr[0] "Pianificato <span class=\"count\">(%s)</span>"
msgstr[1] "Pianificati <span class=\"count\">(%s)</span>"

#: wp-includes/query.php:743
msgid "https://codex.wordpress.org/Function_Reference/is_main_query"
msgstr "https://codex.wordpress.org/Function_Reference/is_main_query"

#: wp-includes/script-loader.php:299
msgid "Upload failed."
msgstr "Caricamento non riuscito."

#: wp-includes/script-loader.php:81
msgid "Dismiss this notice."
msgstr "Nascondi questa notifica."

#. translators: %s: table name
#: wp-includes/ms-load.php:464
msgid "<strong>Database tables are missing.</strong> This means that MySQL is not running, WordPress was not installed properly, or someone deleted %s. You really should look at your database now."
msgstr "<strong>Le tabelle nel database sono mancanti.</strong> Questo significa che MySQL non è in esecuzione, WordPress non è stato installato correttamente, o qualcuno ha cancellato %s. Dovresti controllare il tuo database ora."

#: wp-includes/media.php:3508
msgid "Drag and drop to reorder media files."
msgstr "Trascina per riordinare i file."

#: wp-includes/media.php:3511
msgid "Upload Files"
msgstr "Carica file"

#: wp-includes/media.php:3598
msgid "Create Audio Playlist"
msgstr "Crea playlist audio "

#: wp-includes/media.php:3609
msgid "Edit Video Playlist"
msgstr "Modifica playlist video"

#: wp-includes/media.php:3611
msgid "Insert video playlist"
msgstr "Inserisci playlist video"

#: wp-includes/media.php:3608
msgid "Create Video Playlist"
msgstr "Crea playlist video"

#: wp-includes/media.php:3612
msgid "Update video playlist"
msgstr "Aggiorna playlist video"

#: wp-includes/media.php:3532
msgid "Bulk Select"
msgstr "Selezione multipla"

#: wp-includes/media.php:3539
msgid "Filter by date"
msgstr "Filtra per data"

#: wp-includes/media.php:3540
msgid "Filter by type"
msgstr "Filtra per tipo"

#: wp-includes/ms-load.php:93
msgid "This site is no longer available."
msgstr "Questo sito non è più disponibile"

#: wp-includes/media.php:3534
msgid "Trash Selected"
msgstr "Cestina i selezionati"

#: wp-includes/media.php:3535
msgid "Untrash Selected"
msgstr "Ripristina i selezionati"

#: wp-includes/media.php:3536
msgid "Delete Selected"
msgstr "Elimina i selezionati"

#: wp-includes/media.php:3522
msgid "All dates"
msgstr "Tutte le date"

#: wp-includes/media.php:3525
msgid "Unattached"
msgstr "Non allegato"

#: wp-includes/media.php:3541
msgid "Search Media"
msgstr "Cerca media"

#: wp-includes/media.php:3533
msgid "Cancel Selection"
msgstr "Annulla la selezione"

#: wp-includes/post.php:64
msgctxt "post type general name"
msgid "Media"
msgstr "Media"

#: wp-includes/post.php:23
msgctxt "add new from admin bar"
msgid "Post"
msgstr "Articolo"

#: wp-includes/post.php:43
msgctxt "add new from admin bar"
msgid "Page"
msgstr "Pagina"

#: wp-includes/post-formats.php:94
msgctxt "Post format"
msgid "Link"
msgstr "Link"

#: wp-includes/post.php:1445
msgctxt "post type singular name"
msgid "Page"
msgstr "Pagina"

#: wp-includes/post.php:1445
msgctxt "post type singular name"
msgid "Post"
msgstr "Articolo"

#. translators: 1: Post title
#: wp-includes/pluggable.php:1474
msgid "New trackback on your post \"%s\""
msgstr "Nuovo trackback all'articolo \"%s\""

#: wp-includes/ms-load.php:459
msgid "If you are the owner of this network please check that MySQL is running properly and all tables are error free."
msgstr "Se sei il proprietario di questo network verifica che MySQL stia girando correttamente e tutte le tabelle siano esenti da errori."

#: wp-includes/pluggable.php:1495
msgid "New comment on your post \"%s\""
msgstr "Nuovo commento all'articolo \"%s\""

#. translators: 1: Post title
#: wp-includes/pluggable.php:1485
msgid "New pingback on your post \"%s\""
msgstr "Nuovo pingback all'articolo \"%s\""

#. translators: 1: Post title
#: wp-includes/pluggable.php:1634
msgid "A new trackback on the post \"%s\" is waiting for your approval"
msgstr "Un nuovo trackback all'articolo \"%s\" è in attesa della tua approvazione"

#. translators: 1: Post title
#: wp-includes/pluggable.php:1644
msgid "A new pingback on the post \"%s\" is waiting for your approval"
msgstr "Un nuovo pingback all'articolo \"%s\" è in attesa della tua approvazione"

#. translators: 1: Post title
#: wp-includes/pluggable.php:1654
msgid "A new comment on the post \"%s\" is waiting for your approval"
msgstr "Un nuovo commento all'articolo \"%s\" è in attesa della tua approvazione"

#: wp-includes/ms-load.php:476
msgid "What do I do now?"
msgstr "Cosa devo fare ora?"

#: wp-includes/pluggable.php:1501
msgid "You can see all comments on this post here:"
msgstr "Puoi vedere tutti i commenti su questo post qui:"

#: wp-includes/pluggable.php:1490
msgid "You can see all pingbacks on this post here:"
msgstr "Puoi vedere tutti i pingbacks su questo post qui:"

#. translators: 1: Comment text
#: wp-includes/pluggable.php:1478 wp-includes/pluggable.php:1489
#: wp-includes/pluggable.php:1500 wp-includes/pluggable.php:1663
msgid "Comment: %s"
msgstr "Commento: %s"

#. translators: 1: Trackback/pingback/comment author URL
#: wp-includes/pluggable.php:1477 wp-includes/pluggable.php:1488
#: wp-includes/pluggable.php:1499 wp-includes/pluggable.php:1639
#: wp-includes/pluggable.php:1649 wp-includes/pluggable.php:1661
msgid "URL: %s"
msgstr "URL: %s"

#: wp-includes/user.php:1560 wp-includes/ms-functions.php:501
msgid "Sorry, that email address is already used!"
msgstr "Questo indirizzo email è già utilizzato!"

#: wp-includes/media.php:3527
msgctxt "noun"
msgid "Trash"
msgstr "Cestino"

#: wp-includes/script-loader.php:303
msgid "IO error."
msgstr "Errore di I/O."

#: wp-includes/ms-load.php:458
msgid "If your site does not display, please contact the owner of this network."
msgstr "Se il tuo sito non si visualizza contatta l'amministratore di questo network."

#: wp-includes/post.php:1455
msgid "Parent Page:"
msgstr "Pagina genitore:"

#: wp-includes/rest-api/class-wp-rest-server.php:975
msgid "No route was found matching the URL and request method"
msgstr "Nessun percorso fornisce una corrispondenza tra l'URL e le modalità di richiesta"

#: wp-includes/rest-api/class-wp-rest-server.php:854
msgid "The handler for the route is invalid"
msgstr "Il gestore del percorso non è valido"

#. translators: 1: function name, 2: WordPress version number, 3: error message
#: wp-includes/rest-api.php:519
msgid "%1$s (since %2$s; %3$s)"
msgstr "%1$s (dal %2$s; %3$s)"

#. translators: 1: function name, 2: WordPress version number
#: wp-includes/rest-api.php:498 wp-includes/rest-api.php:522
msgid "%1$s (since %2$s; no alternative available)"
msgstr "%1$s (da %2$s; nessuna alternativa disponibile)"

#. translators: 1: function name, 2: WordPress version number, 3: new function
#. name
#: wp-includes/rest-api.php:495
msgid "%1$s (since %2$s; use %3$s instead)"
msgstr "%1$s (da %2$s; utilizza invece %3$s )"

#: wp-includes/rest-api/class-wp-rest-server.php:1062
msgid "The specified namespace could not be found."
msgstr "Il namespace specificato non è stato trovato."

#: wp-includes/rest-api/class-wp-rest-request.php:805
#: wp-includes/rest-api/class-wp-rest-request.php:871
msgid "Invalid parameter(s): %s"
msgstr "Parametro(i) non valido(i): %s"

#: wp-includes/rest-api/class-wp-rest-request.php:843
msgid "Missing parameter(s): %s"
msgstr "Parametro(i) mancante(i): %s"

#: wp-includes/post.php:1465
msgid "Filter pages list"
msgstr "Filtra elenco pagine"

#: wp-includes/post.php:1465
msgid "Filter posts list"
msgstr "Filtra elenco articoli"

#: wp-includes/post.php:1457
msgid "Page Archives"
msgstr "Archivi pagine"

#: wp-includes/post.php:1457
msgid "Post Archives"
msgstr "Archivi articoli"

#: wp-includes/ms-functions.php:599
msgid "Site names can only contain lowercase letters (a-z) and numbers."
msgstr "I nomi sito possono contenere solo lettere minuscole (a-z) e numeri."

#: wp-includes/ms-functions.php:445
msgid "Usernames can only contain lowercase letters (a-z) and numbers."
msgstr "I nomi utente possono contenere solo lettere minuscole (a-z) e numeri."

#: wp-includes/rest-api/class-wp-rest-server.php:280
msgid "JSONP support is disabled on this site."
msgstr "Supporto JSONP disabilitato in questo sito."

#: wp-includes/post.php:1467
msgid "Posts list"
msgstr "Elenco degli articoli"

#: wp-includes/post.php:1467
msgid "Pages list"
msgstr "Elenco delle pagine"

#. translators: %s: menu name
#: wp-includes/nav-menu.php:324 wp-includes/nav-menu.php:337
msgid "The menu name %s conflicts with another menu name. Please try another."
msgstr "Il nome del menu %s è in conflitto con il nome di un altro menu. Provane un altro."

#. translators: %s: taxonomy name
#: wp-includes/post.php:3512
msgid "Invalid taxonomy: %s."
msgstr "Tassonomia non valida: %s."

#: wp-includes/post.php:1466
msgid "Pages list navigation"
msgstr "Navigazione elenco pagine"

#: wp-includes/post.php:1466
msgid "Posts list navigation"
msgstr "Navigazione elenco articoli"

#: wp-includes/ms-functions.php:471
msgid "Please enter a valid email address."
msgstr "Inserire un indirizzo email valido."

#: wp-includes/pluggable.php:1479
msgid "You can see all trackbacks on this post here:"
msgstr "Puoi vedere tutti i trackback su questo post qui:"

#: wp-includes/ms-functions.php:892
msgid ""
"To activate your blog, please click the following link:\n"
"\n"
"%s\n"
"\n"
"After you activate, you will receive *another email* with your login.\n"
"\n"
"After you activate, you can visit your site here:\n"
"\n"
"%s"
msgstr ""
"Per attivare il tuo blog, fai click sul seguente link:\n"
"\n"
"%s\n"
"\n"
"Dopo  l&#8217;attivazione, riceverai *un&#8217;altra email* con i dati di login e, successivamente, potrai visitare il tuo sito qui:\n"
"\n"
"%s"

#: wp-includes/media.php:3576
msgid "Crop your image"
msgstr "Ritaglia la tua immagine"

#. translators: 1: Comment author URL
#. translators: %s: user email address
#: wp-includes/pluggable.php:1498 wp-includes/pluggable.php:1659
#: wp-includes/pluggable.php:1835
msgid "Email: %s"
msgstr "Email: %s"

#: wp-includes/media.php:3589
msgid "Video Details"
msgstr "Dettagli video"

#: wp-includes/media.php:3590
msgid "Replace Video"
msgstr "Sostituisci video"

#: wp-includes/rest-api.php:784
msgid "Cookie nonce is invalid"
msgstr "Il nonce del cookie non valido"

#: wp-includes/post.php:68
msgid "View Attachment Page"
msgstr "Visualizza la pagina dell'allegato"

#. translators: 1: pre_get_posts 2: WP_Query->is_main_query() 3:
#. is_main_query() 4: link to codex is_main_query() page.
#: wp-includes/query.php:739
msgid "In %1$s, use the %2$s method, not the %3$s function. See %4$s."
msgstr "In %1$s, viene usato il metodo %2$s anziché la funzione %3$s. Si veda %4$s."

#. translators: %s: admin email link
#: wp-includes/ms-load.php:103
msgid "This site has not been activated yet. If you are having problems activating your site, please contact %s."
msgstr "Il sito non è ancora stato attivato. Se hai dei problemi nell'attivare il tuo sito, contatta %s."

#: wp-includes/media.php:3591
msgid "Add Video Source"
msgstr "Aggiungi sorgente video"

#: wp-includes/media.php:3614
msgid "Add to Video Playlist"
msgstr "Aggiungi alla playlist video"

#: wp-includes/ms-load.php:479
msgid "https://codex.wordpress.org/Debugging_a_WordPress_Network"
msgstr "https://codex.wordpress.org/Debugging_a_WordPress_Network"

#: wp-includes/rest-api/class-wp-rest-request.php:861
msgid "Invalid parameter."
msgstr "Parametro non valido."

#: wp-includes/ms-functions.php:596
msgid "Please enter a site name."
msgstr "Inserisci il nome del sito."

#: wp-includes/ms-functions.php:452
msgid "Please enter a username."
msgstr "Inserisci il nome utente."

#: wp-includes/ms-functions.php:642
msgid "Please enter a site title."
msgstr "Inserisci il titolo del sito."

#: wp-includes/ms-functions.php:2101
msgid "You have been added to this site. Please visit the <a href=\"%s\">homepage</a> or <a href=\"%s\">log in</a> using your username and password."
msgstr "Sei stato aggiunto a questo sito. Visita <a href=\"%s\">l'homepage </a> o <a href=\"%s\">accedi</a> utilizzando il tuo nome utente e la tua password."

#: wp-includes/ms-functions.php:473
msgid "You cannot use that email address to signup. We are having problems with them blocking some of our email. Please use another email provider."
msgstr "Non è possibile utilizzare questo indirizzo email per registrarsi. Stiamo avendo problemi con alcune nostre email che vengono bloccate. Utilizzare un altro fornitore di email."

#: wp-includes/ms-functions.php:523
msgid "That email address has already been used. Please check your inbox for an activation email. It will become available in a couple of days if you do nothing."
msgstr "Questo indirizzo email è stato già utilizzato. Verifica se nella tua posta è arrivata una email di attivazione. Se non fai nulla, l'indirizzo diverrà nuovamente disponibile in un paio di giorni."

#. translators: Comment moderation notification email subject. 1: Site name, 2:
#. Post title
#: wp-includes/pluggable.php:1687
msgid "[%1$s] Please moderate: \"%2$s\""
msgstr "[%1$s] Moderare: \"%2$s\""

#. translators: 1: site url, 2: table name, 3: database name
#: wp-includes/ms-load.php:470
msgid "<strong>Could not find site %1$s.</strong> Searched for table %2$s in database %3$s. Is that right?"
msgstr "<strong>Non trovo il sito %1$s.</strong> Ricerca per tabella %2$s nel database %3$s. È corretto?"

#. translators: %s: Codex URL
#: wp-includes/ms-load.php:478
msgid "Read the <a href=\"%s\" target=\"_blank\">bug report</a> page. Some of the guidelines there may help you figure out what went wrong."
msgstr "Leggi la pagina <a href=\"%s\" target=\"_blank\">Segnalazione bug</a>. Alcune delle linee guida potrebbero aiutarti a capire cosa è andato storto."

#: wp-includes/media.php:3600
msgid "&#8592; Cancel Audio Playlist"
msgstr "&#8592; Annulla la playlist audio"

#: wp-includes/media.php:3572
msgid "Choose Image"
msgstr "Scegli immagine"

#: wp-includes/media.php:3573
msgid "Select and Crop"
msgstr "Seleziona e ritaglia"

#: wp-includes/media.php:3594
msgid "Add Subtitles"
msgstr "Aggiungi sottotitoli"

#: wp-includes/media.php:3568 wp-includes/media.php:3586
#: wp-includes/media.php:3592
msgid "Cancel Edit"
msgstr "Cancella modifiche"

#: wp-includes/media.php:3583
msgid "Audio Details"
msgstr "Dettagli audio"

#: wp-includes/media.php:3597
msgid "Drag and drop to reorder tracks."
msgstr "Trascina e rilascia per riordinare le tracce."

#: wp-includes/media.php:3574
msgid "Skip Cropping"
msgstr "Salta ritaglio"

#: wp-includes/media.php:3575
msgid "Crop Image"
msgstr "Ritaglia immagine"

#: wp-includes/media.php:3584
msgid "Replace Audio"
msgstr "Sostituisci audio"

#: wp-includes/media.php:3585
msgid "Add Audio Source"
msgstr "Aggiungi sorgente audio"

#: wp-includes/media.php:3593
msgid "Select Poster Image"
msgstr "Seleziona immagine Poster"

#: wp-includes/media.php:3610
msgid "&#8592; Cancel Video Playlist"
msgstr "&#8592; Cancella playlist video"

#: wp-includes/post.php:229
msgctxt "post status"
msgid "Published"
msgstr "Pubblicato"

#: wp-includes/post.php:236
msgctxt "post status"
msgid "Scheduled"
msgstr "Programmato"

#: wp-includes/post.php:243
msgctxt "post status"
msgid "Draft"
msgstr "Bozza"

#: wp-includes/post.php:250
msgctxt "post status"
msgid "Pending"
msgstr "In attesa"

#: wp-includes/post.php:257
msgctxt "post status"
msgid "Private"
msgstr "Privato"

#. translators: Number of results found when using jQuery UI Autocomplete
#: wp-includes/script-loader.php:244
msgid "1 result found. Use up and down arrow keys to navigate."
msgstr "Trovato 1 risultato. Usa i tasti freccia su e giù per esplorarlo."

#. translators: %d: Number of results found when using jQuery UI Autocomplete
#: wp-includes/script-loader.php:246
msgid "%d results found. Use up and down arrow keys to navigate."
msgstr "Trovati %d risultati. Usa i tasti freccia su e giù per esplorarli. "

#: wp-includes/post-template.php:1611
msgctxt "post password form"
msgid "Enter"
msgstr "Invio"

#: wp-includes/post.php:264
msgctxt "post status"
msgid "Trash"
msgstr "Cestino"

#: wp-includes/rest-api.php:40
msgid "Route must be specified."
msgstr "Deve essere specificato il percorso."

#: wp-includes/rest-api.php:37
msgid "Routes must be namespaced with plugin or theme name and version."
msgstr "I percorsi devono essere \"namespaced\" con il nome e la versione del plugin o del tema."

#: wp-includes/media.php:3543
msgid "No media files found."
msgstr "Nessun file multimediale trovato."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1108
#: wp-includes/user.php:1499 wp-includes/ms-functions.php:460
#: wp-includes/ms-functions.php:467
msgid "Sorry, that username is not allowed."
msgstr "Questo nome utente non è consentito."

#: wp-includes/script-loader.php:380
msgid "Unmute"
msgstr "Togli il muto"

#: wp-includes/script-loader.php:378
msgid "Mute Toggle"
msgstr "Cambia il muto"

#. translators: New user notification email subject. 1: Network name, 2: New
#. user login
#: wp-includes/ms-functions.php:1696
msgid "New %1$s User: %2$s"
msgstr "Nuovo %1$s utente: %2$s"

#: wp-includes/script-loader.php:288
msgid "You have attempted to queue too many files."
msgstr "Hai tentato di mettere in coda troppi file."

#: wp-includes/script-loader.php:290
msgid "This file is empty. Please try another."
msgstr "Questo file è vuoto. Prova con un altro file."

#. translators: Do not translate USERNAME, PASSWORD, LOGINLINK, SITE_NAME:
#. those are placeholders.
#: wp-includes/ms-functions.php:2230
msgid ""
"Howdy USERNAME,\n"
"\n"
"Your new account is set up.\n"
"\n"
"You can log in with the following information:\n"
"Username: USERNAME\n"
"Password: PASSWORD\n"
"LOGINLINK\n"
"\n"
"Thanks!\n"
"\n"
"--The Team @ SITE_NAME"
msgstr ""
"Ciao USERNAME,\n"
"\n"
"il tuo nuovo account è pronto.\n"
"\n"
"Puoi effettuare l'accesso con le seguenti informazioni:\n"
"Nome utente: USERNAME\n"
"Password: PASSWORD\n"
"LOGINLINK\n"
"\n"
"Grazie!\n"
"Il team di SITE_NAME"

#. translators: Do not translate USERNAME, SITE_NAME, BLOG_URL, PASSWORD: those
#. are placeholders.
#: wp-includes/ms-functions.php:1556
msgid ""
"Howdy USERNAME,\n"
"\n"
"Your new SITE_NAME site has been successfully set up at:\n"
"BLOG_URL\n"
"\n"
"You can log in to the administrator account with the following information:\n"
"\n"
"Username: USERNAME\n"
"Password: PASSWORD\n"
"Log in here: BLOG_URLwp-login.php\n"
"\n"
"We hope you enjoy your new site. Thanks!\n"
"\n"
"--The Team @ SITE_NAME"
msgstr ""
"Ciao USERNAME,\n"
"\n"
"il tuo nuovo sito SITE_NAME è stato correttamente creato ed impostato su:\n"
"BLOG_URL\n"
"\n"
"Puoi effettuare l'accesso all’account di amministrazione con le seguenti informazioni:\n"
"\n"
"Nome utente: USERNAME\n"
"Password: PASSWORD\n"
"Effettua l'accesso qui: BLOG_URLwp-login.php\n"
"\n"
"Speriamo che il tuo nuovo sito ti piaccia. Grazie!\n"
"\n"
"Il team di SITE_NAME"

#: wp-includes/script-loader.php:278
msgid "This feature requires inline frames. You have iframes disabled or your browser does not support them."
msgstr "Questa funzionalità richiede il frame in linea. Hai gli iframe disabilitati o il browser non li supporta."

#: wp-includes/rss.php:917 wp-includes/widgets.php:1414
msgid "An error has occurred, which probably means the feed is down. Try again later."
msgstr "Si è verificato un errore; probabilmente il feed non è attivo. Riprova più tardi."

#: wp-includes/post.php:3481
msgid "Could not insert post into the database"
msgstr "È impossibile inserire l'articolo nel database"

#. translators: 1: VHOST, 2: SUBDOMAIN_INSTALL, 3: wp-config.php, 4:
#. is_subdomain_install()
#: wp-includes/ms-default-constants.php:132
msgid "The constant %1$s <strong>is deprecated</strong>. Use the boolean constant %2$s in %3$s to enable a subdomain configuration. Use %4$s to check whether a subdomain configuration is enabled."
msgstr "La costante %1$s <strong>è deprecata</strong>. Utilizza la costante booleana %2$s in %3$s per abilitare la configurazione dei sottodomini. Utilizza %4$s per verificare se una configurazione dei sottodomini è abilitata."

#. translators: %s: user name
#: wp-includes/pluggable.php:1755
msgid "Password changed for user: %s"
msgstr "Password cambiata per l'utente: %s"

#. translators: Password change notification email subject. %s: Site title
#: wp-includes/pluggable.php:1763
msgid "[%s] Password Changed"
msgstr "[%s] Password cambiata"

#: wp-includes/script-loader.php:496
msgctxt "password strength"
msgid "Password strength unknown"
msgstr "Efficacia della password sconosciuta"

#: wp-includes/revision.php:550
msgid "Sorry, you are not allowed to preview drafts."
msgstr "Non hai i permessi per visualizzare le bozze in anteprima."

#: wp-includes/script-loader.php:132 wp-includes/script-loader.php:644
#: wp-includes/rest-api/class-wp-rest-server.php:911
msgid "Sorry, you are not allowed to do that."
msgstr "Non hai i permessi per farlo."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:143
msgid "Sorry, you are not allowed to edit terms in this taxonomy."
msgstr "Non hai i permessi per modificare i termini di questa tassonomia."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:653
msgid "Sorry, you are not allowed to edit this comment."
msgstr "Non hai i permessi per modificare questo tipo di articoli."

#: wp-includes/script-loader.php:374
msgid "Skip back %1 seconds"
msgstr "Vai indietro di %1 secondi"

#: wp-includes/script-loader.php:373
msgid "Use Left/Right Arrow keys to advance one second, Up/Down arrows to advance ten seconds."
msgstr "Usa i tasti freccia sinistra/destra per avanzare di un secondo, su/giù per avanzare di 10 secondi."

#: wp-includes/script-loader.php:379
msgid "Use Up/Down Arrow keys to increase or decrease volume."
msgstr "Usa i tasti freccia su/giù per aumentare o diminuire il volume."

#: wp-includes/script-loader.php:369 wp-includes/theme.php:1396
msgid "Play"
msgstr "Play"

#: wp-includes/script-loader.php:372
msgid "Time Slider"
msgstr "Time Slider"

#: wp-includes/script-loader.php:383
msgid "Video Player"
msgstr "Video Player"

#: wp-includes/script-loader.php:384
msgid "Audio Player"
msgstr "Audio Player"

#: wp-includes/script-loader.php:382
msgid "Volume Slider"
msgstr "Cursore del volume"

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:129
#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:179
msgid "Invalid status."
msgstr "Lo stato non è valido."

#. translators: %s: revision date
#. translators: %s: revision date with author avatar
#: wp-includes/post-template.php:1711 wp-includes/post-template.php:1763
msgid "%s [Autosave]"
msgstr "%s [Salvataggio automatico]"

#. translators: %s: revision date
#. translators: %s: revision date with author avatar
#: wp-includes/post-template.php:1713 wp-includes/post-template.php:1765
msgid "%s [Current Revision]"
msgstr "%s [Revisione attuale]"

#. translators: 1: parameter, 2: minimum number, 3: maximum number
#: wp-includes/rest-api.php:1205
msgid "%1$s must be between %2$d (inclusive) and %3$d (inclusive)"
msgstr "%1$s deve essere tra %2$d (incluso) e %3$d (incluso)"

#. translators: 1: parameter, 2: minimum number, 3: maximum number
#: wp-includes/rest-api.php:1200
msgid "%1$s must be between %2$d (exclusive) and %3$d (inclusive)"
msgstr "%1$s deve essere tra %2$d (escluso) e %3$d (incluso)"

#. translators: 1: parameter, 2: minimum number, 3: maximum number
#: wp-includes/rest-api.php:1195
msgid "%1$s must be between %2$d (inclusive) and %3$d (exclusive)"
msgstr "%1$s deve essere tra %2$d (incluso) e %3$d (escluso)"

#. translators: 1: parameter, 2: minimum number, 3: maximum number
#: wp-includes/rest-api.php:1190
msgid "%1$s must be between %2$d (exclusive) and %3$d (exclusive)"
msgstr "%1$s deve essere tra %2$d (escluso) e %3$d (escluso)"

#. translators: %s: IP address
#: wp-includes/rest-api.php:1163
msgid "%s is not a valid IP address."
msgstr "%s non è un indirizzo IP valido."

#. translators: 1: parameter, 2: type name
#: wp-includes/rest-api.php:1089 wp-includes/rest-api.php:1105
#: wp-includes/rest-api.php:1129 wp-includes/rest-api.php:1134
#: wp-includes/rest-api.php:1139 wp-includes/rest-api.php:1144
msgid "%1$s is not of type %2$s."
msgstr "%1$s non è del tipo %2$s."

#. translators: 1: parameter, 2: list of valid values
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1189
#: wp-includes/rest-api.php:1123
msgid "%1$s is not one of %2$s."
msgstr "%1$s non è uno dei %2$s."

#: wp-includes/rest-api/fields/class-wp-rest-meta-fields.php:398
msgid "Meta fields."
msgstr "Campi dei meta."

#: wp-includes/rest-api/fields/class-wp-rest-meta-fields.php:257
#: wp-includes/rest-api/fields/class-wp-rest-meta-fields.php:267
#: wp-includes/rest-api/fields/class-wp-rest-meta-fields.php:313
msgid "Could not update meta value in database."
msgstr "Impossibile aggiornare il valore del meta nel database."

#: wp-includes/rest-api/fields/class-wp-rest-meta-fields.php:195
msgid "Could not delete meta value from database."
msgstr "Impossibile eliminare il valore del meta dal database."

#. translators: %s: role key
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1054
msgid "The role %s does not exist."
msgstr "Il ruolo %s non esiste."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:627
msgid "Username isn't editable."
msgstr "Il nome utente non si può modificare."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:428
msgid "You are not currently logged in."
msgstr "Al momento non sei connesso."

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:173
msgid "Invalid revision ID."
msgstr "L'ID della revisione non è valido."

#: wp-includes/script-loader.php:247
msgid "Item selected."
msgstr "Elemento selezionato."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2087
msgid "The order of the object in relation to other object of its type."
msgstr "L'ordine dell'oggetto in relazione agli altri oggetti dello stesso tipo"

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2078
msgid "Whether or not the object can be pinged."
msgstr "Se l'oggetto sia pingabile o meno"

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2072
msgid "Whether or not comments are open on the object."
msgstr "Se i commenti siano aperti o meno per l'oggetto"

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:477
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1837
msgid "GUID for the object, as it exists in the database."
msgstr "GUID dell'oggetto, come presente nel database."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1843
msgid "GUID for the object, transformed for display."
msgstr "GUID dell'oggetto, adatto alla visualizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:487
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1864
msgid "The date the object was last modified, in the site's timezone."
msgstr "La data di ultima modifica dell'oggetto, nel fuso orario del sito."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1831
msgid "The globally unique identifier for the object."
msgstr "L'identificatore univoco globale (GUID) dell'oggetto"

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1270
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:471
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1825
msgid "The date the object was published, as GMT."
msgstr "La data di pubblicazione dell'oggetto, nel fuso orario GMT"

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1264
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:465
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1819
msgid "The date the object was published, in the site's timezone."
msgstr "La data di pubblicazione dell'oggetto, nel fuso orario del sito"

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1042
msgid "A password protected post can not be set to sticky."
msgstr "Un articolo protetto da password non può essere posto in evidenza"

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:311
msgid "Whether or not the term cloud should be displayed."
msgstr "Se dovrà essere visualizzata o meno la nuvola di termini."

#: wp-includes/rest-api/endpoints/class-wp-rest-settings-controller.php:188
msgid "The %s property has an invalid stored value, and cannot be updated to null."
msgstr "La proprietà %s è stata salvata con un valore non valido, e non può essere aggiornata a null."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2487
msgid "Status is forbidden."
msgstr "Stato non consentito."

#. translators: %s: taxonomy name
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2412
msgid "Limit result set to all items that have the specified term assigned in the %s taxonomy."
msgstr "Limita il set di risultati a tutti gli elementi che hanno il termine specifico assegnato nella tassonomia %s."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:814
msgid "The post has already been deleted."
msgstr "L'articolo è già stato eliminato."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:485
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:519
msgid "Cannot create existing post."
msgstr "Non è possibile creare un articolo già esistente."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:403
msgid "Incorrect post password."
msgstr "Password dell'articolo non corretta."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:152
msgid "You need to define a search term to order by relevance."
msgstr "Devi definire un terminre di ricerca per ordinare per rilevanza."

#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:347
msgid "Scope under which the request is made; determines fields present in response."
msgstr "Scopo della richiesta; determina i campi che saranno presenti nella risposta."

#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:310
msgid "Current page of the collection."
msgstr "Pagina corrente dell'elenco."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:827
msgid "The comment cannot be deleted."
msgstr "Il commento non può essere eliminato."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1420
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2343
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1351
msgid "Sort collection by object attribute."
msgstr "Ordina la collezione in base ad un attributo dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:65
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1193
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:91
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:482
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:89
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1851
msgid "Unique identifier for the object."
msgstr "Identificatore univoco per l'oggetto."

#: wp-includes/option.php:1826
msgid "Site title."
msgstr "Titolo del sito."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1295
msgid "State of the object."
msgstr "Stato dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:93
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:128
msgid "Required to be true, as users do not support trashing."
msgstr "È necessario che sia impostato su vero, se la risorsa non supporta l'eliminazione."

#. translators: %s: method name
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:53
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:66
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:79
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:92
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:105
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:118
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:131
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:144
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:157
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:170
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:183
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:197
msgid "Method '%s' not implemented. Must be overridden in subclass."
msgstr "Il metodo '%s' non è implementato. Deve essere sovrascritto in una sottoclasse."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:818
msgid "The comment has already been trashed."
msgstr "Il commento è già stato spostato nel cestino."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2433
msgid "Limit result set to items that are sticky."
msgstr "Limita la serie di risultati agli elementi in evidenza."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1035
msgid "A sticky post can not be password protected."
msgstr "Un articolo in evidenza non può essere protetto da password."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1031
msgid "A post can not be sticky and have a password."
msgstr "Un articolo non può essere in evidenza e avere una password."

#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:327
msgid "Limit results to those matching a string."
msgstr "Limita la serie di risultati a quelli contententi una stringa."

#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:318
msgid "Maximum number of items to be returned in result set."
msgstr "Numero massimo dei risultati da restituire nella serie dei risultati."

#: wp-includes/post.php:167
msgid "No changesets found in Trash."
msgstr "Nessun changeset è stato trovato nel cestino."

#: wp-includes/post.php:166
msgid "No changesets found."
msgstr "Non è stato trovato alcun changeset."

#: wp-includes/post.php:165
msgid "Search Changesets"
msgstr "Cerca i changeset"

#: wp-includes/post.php:164
msgid "All Changesets"
msgstr "Tutti i changeset"

#: wp-includes/post.php:163
msgid "View Changeset"
msgstr "Visualizza il changeset"

#: wp-includes/post.php:162
msgid "Edit Changeset"
msgstr "Modifica il changeset"

#: wp-includes/post.php:161
msgid "New Changeset"
msgstr "Nuovo changeset"

#: wp-includes/post.php:160
msgid "Add New Changeset"
msgstr "Aggiungi un nuovo changeset"

#: wp-includes/post.php:159
msgctxt "Customize Changeset"
msgid "Add New"
msgstr "Aggiungi nuovo"

#: wp-includes/post.php:158
msgctxt "add new on admin bar"
msgid "Changeset"
msgstr "Changeset"

#: wp-includes/post.php:157
msgctxt "admin menu"
msgid "Changesets"
msgstr "Changeset"

#: wp-includes/post.php:156
msgctxt "post type singular name"
msgid "Changeset"
msgstr "Changeset"

#: wp-includes/post.php:155
msgctxt "post type general name"
msgid "Changesets"
msgstr "Changeset"

#: wp-includes/post.php:69
msgid "Attachment Attributes"
msgstr "Attributi dell'allegato"

#. translators: post revision title: 1: author avatar, 2: author name, 3: time
#. ago, 4: date
#: wp-includes/post-template.php:1755
msgid "%1$s %2$s, %3$s ago (%4$s)"
msgstr "%1$s %2$s, %3$s fa (%4$s)"

#: wp-includes/option.php:1914
msgid "Default post format."
msgstr "Formato dell'articolo predefinito."

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:460
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2025
msgid "The ID for the author of the object."
msgstr "L'ID dell'autore dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2033
msgid "The excerpt for the object."
msgstr "Il riassunto dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2042
msgid "Excerpt for the object, as it exists in the database."
msgstr "Il riassunto dell'oggetto, come riportato nel database."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1978
msgid "Title for the object, as it exists in the database."
msgstr "Il titolo dell'oggetto, come riportato nel database."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1969
msgid "The title for the object."
msgstr "Il titolo dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:493
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1871
msgid "The date the object was last modified, as GMT."
msgstr "La data in cui l'oggetto è stato modificato per l'ultima volta, in GMT."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1276
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1857
msgid "URL to the object."
msgstr "L'URL dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1251
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2003
msgid "Content for the object, as it exists in the database."
msgstr "Il contenuto dell'oggetto, come riportato nel database."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1242
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1994
msgid "The content for the object."
msgstr "Il contenuto dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1214
msgid "IP address for the object author."
msgstr "Indirizzo IP dell'autore dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1204
msgid "Email address for the object author."
msgstr "Indirizzo email dell'autore dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1122
msgid "Invalid comment author ID."
msgstr "L'ID dell'autore del commento non è valido."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:719
msgid "Updating comment failed."
msgstr "Aggiornamento del commento fallito."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:152
msgid "Query parameter not permitted: %s"
msgstr "Parametro della query non permesso: %s"

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:73
msgid "Sorry, you are not allowed to upload media on this site."
msgstr "Non hai i permessi per caricare media su questo sito."

#: wp-includes/post.php:1458
msgid "Page Attributes"
msgstr "Attributi della pagina"

#: wp-includes/post.php:1458
msgid "Post Attributes"
msgstr "Attributi dell'articolo"

#: wp-includes/post.php:1451
msgid "View Pages"
msgstr "Visualizza le pagine"

#: wp-includes/post.php:1451
msgid "View Posts"
msgstr "Visualizza gli articoli"

#: wp-includes/option.php:1941
msgid "Allow people to post comments on new articles."
msgstr "Permetti di pubblicare commenti per i nuovi articoli."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:587
msgid "Creating comment failed."
msgstr "Impossibile creare il commento."

#: wp-includes/option.php:1868
msgid "A city in the same timezone as you."
msgstr "Una città nel tuo stesso fuso orario."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:403
msgid "Alternative text to display when attachment is not displayed."
msgstr "Testo alternativo da mostrare quando la risorsa non è visualizzata."

#: wp-includes/media.php:3542
msgid "Search media items..."
msgstr "Ricerca elementi media..."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1326
msgid "Avatar URLs for the object author."
msgstr "URL dell'avatar dell'autore dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2047
msgid "HTML excerpt for the object, transformed for display."
msgstr "Riassunto HTML per l'oggetto, trasformato per essere visualizzato."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2053
msgid "Whether the excerpt is protected with a password."
msgstr "Indica se il riassunto sia protetto da password."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:527
msgid "Creating a comment requires valid author name and email values."
msgstr "Creare un commento richiede valori corretti per il nome e l'email dell'autore."

#: wp-includes/option.php:1931
msgid "Allow link notifications from other blogs (pingbacks and trackbacks) on new articles."
msgstr "Consenti notifiche di link da altri blog (pingback e trackback) sui nuovi articoli."

#. translators: %d: avatar image size in pixels
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1318
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1286
msgid "Avatar URL with image size of %d pixels."
msgstr "URL dell'avatar con immagine di grandezza %d pixel."

#. translators: %s: misc
#. translators: %s: privacy
#: wp-includes/option.php:2007 wp-includes/option.php:2017
#: wp-includes/option.php:2053 wp-includes/option.php:2063
msgid "The \"%s\" options group has been removed. Use another settings group."
msgstr "Il gruppo di opzioni \"%s\" è stato cancellato. Usa un altro gruppo di impostazioni."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1983
msgid "HTML title for the object, transformed for display."
msgstr "Titolo HTML per l'oggetto, trasformato per la visualizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:95
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:113
msgid "Whether to bypass trash and force deletion."
msgstr "Se evitare il cestino e cancellare definitivamente."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:517
msgid "No Content-Disposition supplied."
msgstr "Non è stata fornita alcuna Content-Disposition."

#: wp-includes/option.php:1874
msgid "A date format for all date strings."
msgstr "Un formato per tutte le stringhe che rappresentano una data."

#: wp-includes/option.php:1880
msgid "A time format for all time strings."
msgstr "Un formato per tutte le stringhe che rappresentano un orario."

#: wp-includes/option.php:1894
msgid "WordPress locale code."
msgstr "Codice locale di WordPress."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1471
msgid "Limit result set to comments assigned a specific type. Requires authorization."
msgstr "Limita l'insieme dei risultati a commenti assegnati ad un tipo specifico. Richiede autorizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:509
#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:710
msgid "No data supplied."
msgstr "Non è stato fornito alcun dato."

#: wp-includes/option.php:1901
msgid "Convert emoticons like :-) and :-P to graphics on display."
msgstr "Convertire in visualizzazione gli emoticon tipo :-) e :-P in simboli grafici."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2386
msgid "Limit result set to posts with one or more specific slugs."
msgstr "Limita l'insieme dei risultati agli articoli con uno o più slug specifici."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:546
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:713
msgid "Comment field exceeds maximum length allowed."
msgstr "Il campo del commento supera la lunghezza massima consentita."

#. translators: %s: taxonomy name
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2422
msgid "Limit result set to all items except those that have the specified term assigned in the %s taxonomy."
msgstr "Limita l'insieme dei risultati a tutti gli elementi eccetto quelli che hanno il termine specificato assegnato nella tassonomia %s."

#: wp-includes/rest-api/class-wp-rest-request.php:657
msgid "Invalid JSON body passed."
msgstr "&Egrave; stato passato un JSON con il body non valido."

#: wp-includes/script-loader.php:83
msgid "Expand Main menu"
msgstr "Espandi il menu principale"

#: wp-includes/script-loader.php:82
msgid "Collapse Main menu"
msgstr "Collassa il menu principale"

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:481
msgid "Cannot create existing comment."
msgstr "Impossibile creare un commento esistente."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1892
msgid "Type of Post for the object."
msgstr "Tipo di post per l'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:504
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1878
msgid "An alphanumeric identifier for the object unique to its type."
msgstr "Un identificatore alfanumerico per l'oggetto unico di questo tipo."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1303
msgid "Type of Comment for the object."
msgstr "Tipo di commento per l'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2014
msgid "Whether the content is protected with a password."
msgstr "Se il contenuto è protetto da password oppure no."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1289
msgid "The ID of the associated post object."
msgstr "L'id dell'oggetto articolo associato."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1283
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:71
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:87
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:499
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1909
msgid "The ID for the parent of the object."
msgstr "L'id del genitore dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1256
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2008
msgid "HTML content for the object, transformed for display."
msgstr "Contenuto HTML dell'oggetto, adattato alla visualizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1234
msgid "User agent for the object author."
msgstr "User agent dell'autore dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1220
msgid "Display name for the object author."
msgstr "Nome pubblico visualizzato per l'autore dell'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1199
msgid "The ID of the user object, if author was a user."
msgstr "L'id dell'oggetto user, se l'autore è stato un utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:697
msgid "Updating comment status failed."
msgstr "Aggiornamento dello stato del commento fallito."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1410
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2336
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1344
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:930
msgid "Order sort attribute ascending or descending."
msgstr "Attributo per l'ordinamento ascendente o discendente."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:548
msgid "Could not open file handle."
msgstr "Non riesco ad aprire l'handle del file."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:532
#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:720
msgid "Content hash did not match expected."
msgstr "L'hash del contenuto non corrisponde a quanto atteso."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:523
msgid "Invalid Content-Disposition supplied. Content-Disposition needs to be formatted as `attachment; filename=\"image.png\"` or similar."
msgstr "Content-Disposition non valido. Content-Disposition deve essere formattato come `attachment;filename=\"image.png\"` o simile."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:513
msgid "No Content-Type supplied."
msgstr "Nessun Content-Type fornito."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:100
#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:206
msgid "Invalid parent type."
msgstr "Tipo genitore non valido."

#: wp-includes/option.php:1886
msgid "A day number of the week that the week should start on."
msgstr "Il numero del giorno della settimana in cui la settimana dovrebbe cominciare."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:667
msgid "Limit result set to attachments of a particular media type."
msgstr "Limita l'insieme dei risultati agli allegati di un tipo media specifico."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2275
msgid "Limit response to posts published after a given ISO8601 compliant date."
msgstr "Limita l'insieme dei risultati a risorse pubblicate dopo una data fornita nello standard ISO8601. "

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1405
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2331
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1338
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:924
msgid "Offset the result set by a specific number of items."
msgstr "Sposta di uno specifico numero di elementi l'inizio dell'insieme dei risultati."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2396
msgid "Limit result set to posts assigned one or more statuses."
msgstr "Limita l'insieme dei risultati agli articoli con uno o più stati assegnati."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2300
msgid "Limit response to posts published before a given ISO8601 compliant date."
msgstr "Limita l'insieme dei risultati a risorse pubblicate prima di una data fornita nello standard ISO8601."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1375
msgid "Limit result set to that from a specific author email. Requires authorization."
msgstr "Limita l'insieme dei risultati a quelli provenienti da uno specifico indirizzo email di un autore. Richiede una autorizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1366
msgid "Ensure result set excludes comments assigned to specific user IDs. Requires authorization."
msgstr "Esclude dall'insieme dei risultati i commenti provenienti da specifici id di autori. Richiede una autorizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1358
msgid "Limit result set to comments assigned to specific user IDs. Requires authorization."
msgstr "Limita l'insieme dei risultati ai commenti assegnati a specifici id utenti. Richiede una autorizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1463
msgid "Limit result set to comments assigned a specific status. Requires authorization."
msgstr "Limita l'insieme dei risultati ai commenti con uno specifico stato assegnato. Richiede una autorizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:674
msgid "Limit result set to attachments of a particular MIME type."
msgstr "Limita l'insieme dei risultati agli allegati con uno specifico tipo MIME."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1886
msgid "A named status for the object."
msgstr "Un stato noto per l'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:449
msgid "HTML description for the object, transformed for display."
msgstr "Descrizione HTML per l'oggetto, trasformata per la visualizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:444
msgid "Description for the object, as it exists in the database."
msgstr "Descrizione dell'oggetto, così come presente nel database."

#: wp-includes/media.php:3558
msgid "&#8592; Cancel Gallery"
msgstr "&#8592; Elimina galleria"

#: wp-includes/script-loader.php:108
msgid "Close deleted text tag"
msgstr "Chiudi il tag di testo eliminato"

#: wp-includes/script-loader.php:107
msgid "Deleted text (strikethrough)"
msgstr "Testo eliminato (barrato)"

#: wp-includes/option.php:1920
msgid "Blog pages show at most."
msgstr "Pagine del blog da mostrare al massimo."

#: wp-includes/media.php:3531
msgid ""
"You are about to trash these items.\n"
"  'Cancel' to stop, 'OK' to delete."
msgstr ""
"Stai per spostare nel cestino questi elementi.\n"
"  'Annulla' per interrompere, 'OK' per eliminare."

#. translators: Comment moderation. 1: Comment action URL
#: wp-includes/pluggable.php:1511 wp-includes/pluggable.php:1672
msgid "Trash it: %s"
msgstr "Sposta nel cestino: %s"

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1134
msgid "Passwords cannot contain the \"\\\" character."
msgstr "Le password non possono contenere il carattere \"\\\"."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1130
msgid "Passwords cannot be empty."
msgstr "Le password non possono essere vuote."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1101
msgid "Username contains invalid characters."
msgstr "Il nome utente contiene caratteri non validi."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:164
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:485
msgid "Invalid user parameter(s)."
msgstr "Parametro/i utente non valido/i."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:97
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:132
msgid "Reassign the deleted user's posts and links to this user ID."
msgstr "Assegna gli articoli e i link dell'utente eliminato all'utente con questo ID. "

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:676
msgid "Sorry, you are not allowed to change the comment type."
msgstr "Non hai i permessi per cambiare il tipo di commento. "

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:158
msgid "Sorry, you are not allowed to view revisions of this post."
msgstr "Non hai i permessi per vedere le revisioni di questo articolo. "

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:453
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:457
msgid "Sorry, you are not allowed to create a comment on this post."
msgstr "Non hai i permessi per creare un commento per questo articolo."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:444
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:449
msgid "Sorry, you are not allowed to create this comment without a post."
msgstr "Non hai i permessi per per creare questo commento senza un articolo."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:122
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:358
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:461
msgid "Sorry, you are not allowed to read the post for this comment."
msgstr "Non hai i permessi per leggere l'articolo associato a questo commento."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:354
msgid "Sorry, you are not allowed to read this comment."
msgstr "Non hai i permessi per leggere questo commento."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:124
msgid "Sorry, you are not allowed to read comments without a post."
msgstr "Non hai i permessi per leggere i commenti senza un articolo."

#. translators: %s: custom field key
#: wp-includes/rest-api/fields/class-wp-rest-meta-fields.php:187
#: wp-includes/rest-api/fields/class-wp-rest-meta-fields.php:222
#: wp-includes/rest-api/fields/class-wp-rest-meta-fields.php:293
msgid "Sorry, you are not allowed to edit the %s custom field."
msgstr "Non hai i permessi per modificare il campo personalizzato %s."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:503
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:641
msgid "Sorry, you are not allowed to assign the provided terms."
msgstr "Non hai i permessi per per assegnare i termini forniti."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:633
msgid "Sorry, you are not allowed to update posts as this user."
msgstr "Non hai i permessi per aggiornare gli articoli con questo utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2064
msgid "The ID of the featured media for the object."
msgstr "L'id del media in evidenza per l'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:631
msgid "Invalid slug."
msgstr "Slug non valido. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:623
#: wp-includes/rest-api.php:1157 wp-includes/user.php:3204
msgid "Invalid email address."
msgstr "Indirizzo email non valido. "

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:501
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:705
msgid "Invalid comment content."
msgstr "Contenuto del commento non valido. "

#: wp-includes/rest-api/class-wp-rest-server.php:286
msgid "Invalid JSONP callback function."
msgstr "Funzione callback JSONP non valida. "

#: wp-includes/post.php:3579
msgid "Invalid page template."
msgstr "Template di pagina non valido. "

#: wp-includes/script-loader.php:606 wp-includes/rest-api.php:1151
#: wp-includes/post.php:3299
msgid "Invalid date."
msgstr "Data non valida. "

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:486
msgid "Cannot create a comment with that type."
msgstr "Impossibile creare un commento con questo tipo."

#. translators: %s: request parameter
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:420
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:429
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:438
msgid "Sorry, you are not allowed to edit '%s' for comments."
msgstr "Non hai i permessi per modificare '%s' nei commenti."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:130
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:348
msgid "Sorry, you are not allowed to edit comments."
msgstr "Non hai i permessi per modificare i commenti."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:87
#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:145
msgid "Sorry, you are not allowed to manage terms in this taxonomy."
msgstr "Non hai i permessi per gestire i termini di questa tassonomia"

#. translators: New user notification email subject. 1: Network name, 2: New
#. user login
#: wp-includes/ms-functions.php:1012
msgctxt "New user notification email subject"
msgid "[%1$s] Activate %2$s"
msgstr "[%1$s] %2$s è stato attivato"

#. translators: New site notification email subject. 1: Network name, 2: New
#. site URL
#: wp-includes/ms-functions.php:917
msgctxt "New site notification email subject"
msgid "[%1$s] Activate %2$s"
msgstr "[%1$s] %2$s è stato attivato"

#: wp-includes/option.php:1834
msgid "Site tagline."
msgstr "Motto del sito."

#: wp-includes/option.php:1908
msgid "Default post category."
msgstr "Categoria predefinita per gli articoli."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:95
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:826
msgid "Unique identifier for the term."
msgstr "Identificativo unico per il termine. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1294
msgid "Avatar URLs for the user."
msgstr "L'URL dell'avatar per l'utente. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1206
msgid "Description of the user."
msgstr "La descrizione dell'utente. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1200
msgid "URL of the user."
msgstr "L'URL dell'utente. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1068
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1078
msgid "Sorry, you are not allowed to give users that role."
msgstr "Non hai i permessi per attribuire agli utenti questo ruolo. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1185
msgid "Last name for the user."
msgstr "Il cognome dell'utente. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:186
msgid "Sorry, you are not allowed to order users by this parameter."
msgstr "Non hai i permessi per ordinare gli utenti in base e questo parametro."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1177
msgid "First name for the user."
msgstr "Il nome dell'utente "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:178
msgid "Sorry, you are not allowed to filter users by role."
msgstr "Non hai i permessi per filtrare gli utenti in base al ruolo"

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:383
msgid "Sorry, you are not allowed to create new terms."
msgstr "Non hai i permessi per creare un nuovo termine. "

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:426
msgid "HTML caption for the attachment, transformed for display."
msgstr "Didascalia HTML per l'allegato, trasformata per la visualizzazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1160
msgid "Login name for the user."
msgstr "Il nome per l'accesso dell'utente. "

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:421
msgid "Caption for the attachment, as it exists in the database."
msgstr "Didascalia per l'allegato, così come presente nel database."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:747
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:780
msgid "The user cannot be deleted."
msgstr "L'utente non può essere eliminato. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1270
msgid "Any extra capabilities assigned to the user."
msgstr "Qualsiasi capacità extra assegnata all'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:502
msgid "Error creating new user."
msgstr "Si è verificato un errore durante la creazione del nuovo utente. "

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:880
msgid "The parent term ID."
msgstr "L'ID del termine genitore. "

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:850
msgid "HTML title for the term."
msgstr "Il titolo HTML del termine. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1374
msgid "Limit result set to users matching at least one specific role provided. Accepts csv list or single role."
msgstr "Limita la serie di risultati agli utenti che corrispondono ad almeno uno dei ruoli indicati. Può accettare un elenco csv o un singolo ruolo."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:843
msgid "URL of the term."
msgstr "L'URL del termine. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1264
msgid "All capabilities assigned to the user."
msgstr "Tutte le capacità assegnate all'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1255
msgid "Password for the user (never included)."
msgstr "Password per l'utente (mai inclusa)."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:603
msgid "The term cannot be deleted."
msgstr "Il termine non può essere eliminato. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1193
msgid "The email address for the user."
msgstr "L'indirizzo email per l'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:766
msgid "Invalid user ID for reassignment."
msgstr "ID dell'utente non valido per la riassegnazione."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:730
msgid "Sorry, you are not allowed to delete this user."
msgstr "Non hai i permessi per eliminare questo utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:466
msgid "Cannot create existing user."
msgstr "Impossibile creare un utente già esistente."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:583
msgid "Sorry, you are not allowed to edit roles of this user."
msgstr "Non hai i permessi per modificare i ruoli di questo utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:940
msgid "Sort collection by term attribute."
msgstr "Ordina la raccolta per l'per attributo del termine"

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:956
msgid "Whether to hide terms not assigned to any posts."
msgstr "Se vuoi nascondere i termini non assegnati agli articoli. "

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:307
msgid "Term does not exist."
msgstr "Il termine non esiste. "

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:867
msgid "Type attribution for the term."
msgstr "Attribuzione del tipo per il termine."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:299
msgid "The title for the taxonomy."
msgstr "Il titolo della tassonomia. "

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:317
msgid "Types associated with the taxonomy."
msgstr "I tipi associati con la tassonomia. "

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:269
msgid "The title for the status."
msgstr "Il titolo dello stato. "

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:859
msgid "An alphanumeric identifier for the term unique to its type."
msgstr "Un identificatore alfanumerico per il termine, unico nella sua tipologia."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:135
msgid "Cannot view status."
msgstr "Non puoi vedere lo stato. "

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:969
msgid "Limit result set to terms assigned to a specific post."
msgstr "Limita la serie di risultati ai termini assegnati ad uno specifico articolo."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:963
msgid "Limit result set to terms assigned to a specific parent."
msgstr "Limita la serie di risultati ai termini assegnati ad uno specifico genitore."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:486
msgid "URL to the original attachment file."
msgstr "URL del file allegato originario. "

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:838
msgid "HTML description of the term."
msgstr "Descrizione HTML del termine."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:67
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1154
msgid "Unique identifier for the user."
msgstr "Identificatore univoco per l'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:435
msgid "The attachment description."
msgstr "La descrizione dell'allegato. "

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:412
msgid "The attachment caption."
msgstr "La didascalia dell'allegato. "

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:466
msgid "The attachment MIME type."
msgstr "Il tipo MIME per l'allegato."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:458
msgid "Attachment type."
msgstr "Tipo dell'allegato."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:832
msgid "Number of published posts for the term."
msgstr "Numero di articoli pubblicati per il termine."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:326
msgid "REST base route for the taxonomy."
msgstr "Rotta base REST per la tassonomia."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1352
msgid "Limit response to comments published after a given ISO8601 compliant date."
msgstr "Limita la risposta ai commenti pubblicati dopo una specifica data conforme al formato ISO8601."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1381
msgid "Limit response to comments published before a given ISO8601 compliant date."
msgstr "Limita la risposta ai commenti pubblicati prima di una specifica data conforme al formato ISO8601."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1247
msgid "Roles assigned to the user."
msgstr "Ruoli assegnati all'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1211
msgid "Author URL of the user."
msgstr "URL autore dell'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1169
msgid "Display name for the user."
msgstr "Visualizza il nome dell'utente. "

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1436
msgid "Limit result set to comments of specific parent IDs."
msgstr "Limita l'insieme dei risultati alle risorse appartenenti a specifici ID genitore."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1454
msgid "Limit result set to comments assigned to specific post IDs."
msgstr "Limita l'insieme dei risultati ai commenti assegnati a specifici ID di articoli."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1396
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2315
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1329
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:914
msgid "Limit result set to specific IDs."
msgstr "Limita l'insieme dei risultati a specifici ID."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1387
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2306
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1320
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:905
msgid "Ensure result set excludes specific IDs."
msgstr "Esclude specifici ID dall'insieme dei risultati."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:480
msgid "The ID for the associated post of the attachment."
msgstr "L'ID dell'articolo associato all'allegato."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:82
msgid "Sorry, you are not allowed to upload media to this post."
msgstr "Non hai i permessi per caricare media in questo articolo."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:473
msgid "Details about the media file, specific to its type."
msgstr "Dettagli circa il file media, specifici a seconda della sua tipologia."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:275
msgid "All capabilities used by the taxonomy."
msgstr "Tutte le capacità utilizzate dalla tassonomia."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1232
msgid "An alphanumeric identifier for the user."
msgstr "Un identificatore alfanumerico per l'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:281
msgid "A human-readable description of the taxonomy."
msgstr "Una descrizione leggibile per la descrizione della tassonomia."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:293
msgid "Human-readable labels for the taxonomy for various contexts."
msgstr "Etichette leggibili per la tassonomia in vari contesti."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:287
msgid "Whether or not the taxonomy should have children."
msgstr "Se questa tassonomia debba avere figli oppure no."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:293
msgid "Whether posts with this status should be publicly-queryable."
msgstr "Se gli articoli con questo status debbano essere richiedibili pubblicamente oppure no."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:287
msgid "Whether posts of this status should be shown in the front end of the site."
msgstr "Se gli articoli con questo status debbano essere mostrati nel front end del sito oppure no."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:281
msgid "Whether posts with this status should be protected."
msgstr "Se gli articoli con questo status debbano essere protetti oppure no."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:275
msgid "Whether posts with this status should be private."
msgstr "Se gli articoli con questo status debbano essere privati oppure no."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:241
msgid "All capabilities used by the post type."
msgstr "Tutte le capacità utilizzate dal tipo di contenuto (post type)."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:247
msgid "A human-readable description of the post type."
msgstr "Una descrizione del tipo di contenuto (post type) in formato leggibile."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:265
msgid "Human-readable labels for the post type for various contexts."
msgstr "Etichette del tipo di contenuto (post type) per vari contesti, in formato leggibile."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:51
#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:277
msgid "An alphanumeric identifier for the post type."
msgstr "Un identificativo alfanumerico per il tipo di contenuto (post type)."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:271
msgid "The title for the post type."
msgstr "Il titolo del tipo di contenuto (post type)."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:127
msgid "Cannot view post type."
msgstr "Non puoi visualizzare il tipo di contenuto (post type)."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:298
msgid "REST base route for the post type."
msgstr "Rotta base REST per il tipo di contenuto (post type)."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:289
msgid "Taxonomies associated with post type."
msgstr "Tassonomie associate con questo tipo di contenuto (post type)."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:347
msgid "Limit results to taxonomies associated with a specific post type."
msgstr "Limita i risultati alle tassonomie associate ad uno specifico tipo di contenuto (post type)."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:299
msgid "Whether to include posts in the edit listing for their post type."
msgstr "Se includere gli articoli nell'elenco dei modificabili del corrispondente tipo di contenuto (post type)."

#: wp-includes/post.php:1241
msgid "Unregistering a built-in post type is not allowed"
msgstr "Non è permesso de-registrare un tipo di contenuto (post type) incorporato"

#: wp-includes/post.php:1190 wp-includes/post.php:1191
msgid "Post type names must be between 1 and 20 characters in length."
msgstr "I nomi dei tipi di contenuto (post type) devono essere compresi fra 1 e 20 caratteri di lunghezza."

#: wp-includes/option.php:1859
msgid "This address is used for admin purposes, like new user notification."
msgstr "Questo indirizzo è utilizzato per scopi amministrativi, come ad esempio la notifica dell'iscrizione di nuovi utenti."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:450
msgid "Sorry, you are not allowed to create new users."
msgstr "Non hai i permessi per creare nuovi utenti."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1445
msgid "Ensure result set excludes specific parent IDs."
msgstr "Esclude dall'insieme dei risultati gli elementi con l'ID genitore specificato."

#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:51
#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:305
msgid "An alphanumeric identifier for the taxonomy."
msgstr "Un identificativo alfanumerico per la tassonomia."

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:111
msgid "Required to be true, as revisions do not support trashing."
msgstr "Deve essere true, poiché le revisioni non si possono eliminare."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:121
msgid "Required to be true, as terms do not support trashing."
msgstr "Deve essere true, poiché i termini non si possono eliminare."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:51
#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:305
msgid "An alphanumeric identifier for the status."
msgstr "Un identificativo alfanumerico per lo stato. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1224
msgid "The nickname for the user."
msgstr "Il nickname per l'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1218
msgid "Locale for the user."
msgstr "La lingua per l'utente. "

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1240
msgid "Registration date for the user."
msgstr "Data di registrazione per l'utente."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2098
msgid "The format for the object."
msgstr "Il formato per l’oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2376
msgid "Limit result set to all items except those of a particular parent ID."
msgstr "Limita il risultato a tutti gli elementi eccetto quelli di un particolare ID genitore."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2368
msgid "Limit result set to items with particular parent IDs."
msgstr "Limita la serie di risultati dei particolari parent ID."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2325
msgid "Limit result set to posts with a specific menu_order value."
msgstr "Limita la serie di risultati agli articoli con uno specifico valore menu_order."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2282
msgid "Limit result set to posts assigned to specific authors."
msgstr "Limita la serie di risultati agli articoli assegnati ad autori specifici."

#. translators: %s: taxonomy name
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2134
msgid "The terms assigned to the object in the %s taxonomy."
msgstr "I termini assegnati all'oggetto nella tassonomia %s."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2121
msgid "The theme file to use to display the object."
msgstr "Il file del tema da utilizzare per visualizzare l'oggetto."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1898
msgid "A password to protect access to the content and excerpt."
msgstr "Una password per proteggere l'accesso al contenuto e al riassunto."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2114
msgid "Whether or not the object should be treated as sticky."
msgstr "Se l'oggetto sia da trattare come in evidenza o meno."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1147
msgid "Invalid featured media ID."
msgstr "ID media in evidenza non valido."

#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:129
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1053
msgid "Invalid post parent ID."
msgstr "ID dell'articolo genitore non valido."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1478
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:82
msgid "The password for the post if it is password protected."
msgstr "La password per l’articolo se fosse protetto da password."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:1228
msgid "URL for the object author."
msgstr "L’URL per l’autore dell'oggetto."

#: wp-includes/script-loader.php:370 wp-includes/theme.php:1395
msgid "Pause"
msgstr "Pausa"

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2290
msgid "Ensure result set excludes posts assigned to specific authors."
msgstr "Garantisce che la serie di risultati escluda gli articoli assegnati ad autori specifici."

#: wp-includes/pluggable.php:1640
msgid "Trackback excerpt: "
msgstr "Riassunto del trackback:"

#: wp-includes/script-loader.php:295
msgid "An error occurred in the upload. Please try again later."
msgstr "Si è verificato un errore durante il caricamento. Riprova più tardi."

#: wp-includes/script-loader.php:296
msgid "There was a configuration error. Please contact the server administrator."
msgstr "Si è verificato un errore di configurazione. Contatta l'amministratore del server."

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:253
msgid "Whether or not the post type should have children."
msgstr "Se il tipo di contenuto (post type) deve avere figli o meno. "

#: wp-includes/post.php:1461
msgctxt "post"
msgid "Featured Image"
msgstr "Immagine in evidenza"

#: wp-includes/post.php:1461
msgctxt "page"
msgid "Featured Image"
msgstr "Immagine in evidenza"

#: wp-includes/post.php:1462
msgctxt "post"
msgid "Set featured image"
msgstr "Imposta immagine in evidenza"

#: wp-includes/post.php:1462
msgctxt "page"
msgid "Set featured image"
msgstr "Imposta immagine in evidenza"

#: wp-includes/post.php:1463
msgctxt "post"
msgid "Remove featured image"
msgstr "Rimuovi immagine in evidenza"

#: wp-includes/post.php:1463
msgctxt "page"
msgid "Remove featured image"
msgstr "Rimuovi immagine in evidenza"

#: wp-includes/post.php:1464
msgctxt "post"
msgid "Use as featured image"
msgstr "Usa come immagine in evidenza"

#: wp-includes/post.php:1464
msgctxt "page"
msgid "Use as featured image"
msgstr "Usa come immagine in evidenza"

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:76
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:98
msgid "The password for the parent post of the comment (if the post is password protected)."
msgstr "La password per l'articolo genitore del commento(se l'articolo è protetto con password)"

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:283
msgid "All features, supported by the post type."
msgstr "Tutte le funzionalità, supportate dal tipo di contenuto (post type)."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:157
msgid "You need to define an include parameter to order by include."
msgstr "Devi definire un parametro include per ordinare per include."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:329
msgid "The page number requested is larger than the number of pages available."
msgstr "Il numero di pagina chiesto è più grande del numero di pagine disponibili."

#. translators: %s: minimum site name length
#: wp-includes/ms-functions.php:616
msgid "Site name must be at least %s character."
msgid_plural "Site name must be at least %s characters."
msgstr[0] "Il nome del sito deve essere di almeno %s carattere."
msgstr[1] "Il nome del sito deve essere di almeno %s caratteri."

#: wp-includes/rest-api/class-wp-rest-server.php:264
msgid "The REST API can no longer be completely disabled, the rest_authentication_errors filter can be used to restrict access to the API, instead."
msgstr "La REST API non può più essere completamente disabilitata, In alternativa si possono usare i filtri rest_authentication_errors per limitare l'accesso alla API."

#. translators: 1: parameter, 2: maximum number
#: wp-includes/rest-api.php:1184
msgid "%1$s must be less than or equal to %2$d"
msgstr "%1$s deve essere minore o uguale di %2$d"

#. translators: 1: parameter, 2: maximum number
#: wp-includes/rest-api.php:1181
msgid "%1$s must be less than %2$d"
msgstr "%1$s deve essere minore di %2$d"

#. translators: 1: parameter, 2: minimum number
#: wp-includes/rest-api.php:1176
msgid "%1$s must be greater than or equal to %2$d"
msgstr "%1$s deve essere maggiore o uguale di %2$d"

#. translators: 1: parameter, 2: minimum number
#: wp-includes/rest-api.php:1173
msgid "%1$s must be greater than %2$d"
msgstr "%1$s deve essere maggiore di %2$d"

#: wp-includes/script-loader.php:80 wp-includes/media.php:3530
msgid ""
"You are about to permanently delete these items from your site.\n"
"This action cannot be undone.\n"
" 'Cancel' to stop, 'OK' to delete."
msgstr ""
"Stai per eliminare permanentemente questi elementi dal tuo sito.\n"
"Questa azione non può essere annullata.\n"
" 'Annulla' per fermarti, 'OK' per eliminare."

#: wp-includes/media.php:3529
msgid ""
"You are about to permanently delete this item from your site.\n"
"This action cannot be undone.\n"
" 'Cancel' to stop, 'OK' to delete."
msgstr "Stai per eliminare permanentemente questo elemento dal tuo sito. Questa azione non può essere annullata. 'Annulla' per fermarti, 'OK' per eliminare."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1366
msgid "Limit result set to users with one or more specific slugs."
msgstr "Limita l'insieme dei risultati agli utenti con uno o più specifici slug."

#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:975
msgid "Limit result set to terms with one or more specific slugs."
msgstr "Limita l'insieme dei risultati ai termini con uno o più specifici slug."

#. translators: 1: $sanitize_callback, 2: register_setting()
#: wp-includes/option.php:2077
msgid "%1$s is deprecated. The callback from %2$s is used instead."
msgstr "%1$s è deprecata. Viene utilizzata invece la callback da %2$s."

#: wp-includes/script-loader.php:116
msgid "List item"
msgstr "Voce in elenco"

#: wp-includes/script-loader.php:117
msgid "Close list item tag"
msgstr "Chiudi il tag di voce in elenco"

#: wp-includes/script-loader.php:367
msgid "Jump forward 1 second"
msgstr "Vai avanti di 1 secondo"

#: wp-includes/script-loader.php:436
msgid "Spanish"
msgstr "Spagnolo"

#: wp-includes/script-loader.php:432
msgid "Russian"
msgstr "Russo"

#: wp-includes/script-loader.php:430
msgid "Portuguese"
msgstr "Portoghese"

#: wp-includes/script-loader.php:420
msgid "Japanese"
msgstr "Giapponese"

#: wp-includes/script-loader.php:411
msgid "Greek"
msgstr "Greco"

#: wp-includes/script-loader.php:410
msgid "German"
msgstr "Tedesco"

#: wp-includes/script-loader.php:408
msgid "French"
msgstr "Francese"

#: wp-includes/script-loader.php:404
msgid "English"
msgstr "Inglese"

#: wp-includes/script-loader.php:388
msgid "Stop"
msgstr "Stop"

#: wp-includes/script-loader.php:376
msgid "Chapters"
msgstr "Capitoli"

#: wp-includes/script-loader.php:426
msgid "Maltese"
msgstr "Maltese"

#: wp-includes/script-loader.php:424
msgid "Macedonian"
msgstr "Macedone"

#: wp-includes/script-loader.php:397
msgid "Chinese"
msgstr "Cinese"

#: wp-includes/script-loader.php:400
msgid "Croatian"
msgstr "Croato"

#. translators: %s: force=true
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:593
msgid "Terms do not support trashing. Set '%s' to delete."
msgstr "Impossibile spostare nel cestino i termini. Imposta %s per eliminarli."

#. translators: %s: force=true
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:761
msgid "Users do not support trashing. Set '%s' to delete."
msgstr "Impossibile spostare nel cestino gli utenti. Imposta %s per eliminarli."

#. translators: %s: force=true
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:809
msgid "The post does not support trashing. Set '%s' to delete."
msgstr "Impossibile spostare nel cestino l'articolo. Imposta %s per eliminarlo."

#. translators: %s: force=true
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:814
msgid "The comment does not support trashing. Set '%s' to delete."
msgstr "Impossibile spostare nel cestino il commento. Imposta %s per eliminarlo."

#. translators: %s: force=true
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:292
msgid "Revisions do not support trashing. Set '%s' to delete."
msgstr "Impossibile spostare nel cestino le revisioni. Imposta %s per eliminarle."

#. translators: %d: error count
#: wp-includes/script-loader.php:481 wp-includes/script-loader.php:483
msgid "There is %d error which must be fixed before you can update this file."
msgid_plural "There are %d errors which must be fixed before you can update this file."
msgstr[0] "C'è %d errore che deve essere risolto prima di aggiornare questo file."
msgstr[1] "Ci sono %d errori che devono essere risolti prima di aggiornare questo file."

#: wp-includes/script-loader.php:362
msgid "You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https://get.adobe.com/flashplayer/"
msgstr "Stai usando un browser che non ha Flash player abilitato o installato. Attiva il tuo plugin Flash player o scarica l'ultima versione da https://get.adobe.com/flashplayer/"

#. translators: %s: register_routes()
#: wp-includes/rest-api/endpoints/class-wp-rest-controller.php:40
msgid "Method '%s' must be overridden."
msgstr "Il metodo '%s' deve essere ignorato."

#. translators: 1: Trackback/pingback website name, 2: website IP address, 3:
#. website hostname
#: wp-includes/pluggable.php:1476 wp-includes/pluggable.php:1487
#: wp-includes/pluggable.php:1637 wp-includes/pluggable.php:1647
msgid "Website: %1$s (IP address: %2$s, %3$s)"
msgstr "Sito web: %1$s (Indirizzo IP: %2$s, %3$s)"

#: wp-includes/script-loader.php:392
msgid "Albanian"
msgstr "Albanese"

#: wp-includes/script-loader.php:393
msgid "Arabic"
msgstr "Arabo"

#: wp-includes/script-loader.php:401
msgid "Czech"
msgstr "Ceco"

#: wp-includes/script-loader.php:403
msgid "Dutch"
msgstr "Olandese"

#: wp-includes/script-loader.php:405
msgid "Estonian"
msgstr "Estone"

#: wp-includes/script-loader.php:402
msgid "Danish"
msgstr "Danese"

#: wp-includes/script-loader.php:427
msgid "Norwegian"
msgstr "Norvegese"

#: wp-includes/script-loader.php:425
msgid "Malay"
msgstr "Malese"

#: wp-includes/script-loader.php:423
msgid "Lithuanian"
msgstr "Lituano"

#: wp-includes/script-loader.php:419
msgid "Italian"
msgstr "Italiano"

#: wp-includes/script-loader.php:422
msgid "Latvian"
msgstr "Lettone"

#: wp-includes/script-loader.php:421
msgid "Korean"
msgstr "Coreano"

#: wp-includes/script-loader.php:415
msgid "Hungarian"
msgstr "Ungherese"

#: wp-includes/script-loader.php:414
msgid "Hindi"
msgstr "Hindi"

#: wp-includes/script-loader.php:413
msgid "Hebrew"
msgstr "Ebraico"

#: wp-includes/script-loader.php:445
msgid "Yiddish"
msgstr "Yiddish"

#: wp-includes/script-loader.php:444
msgid "Welsh"
msgstr "Gallese"

#: wp-includes/script-loader.php:443
msgid "Vietnamese"
msgstr "Vietnamita"

#: wp-includes/script-loader.php:442
msgid "Ukrainian"
msgstr "Ucraino"

#: wp-includes/script-loader.php:441
msgid "Turkish"
msgstr "Turco"

#: wp-includes/script-loader.php:440
msgid "Thai"
msgstr "Thailandese"

#: wp-includes/script-loader.php:438
msgid "Swedish"
msgstr "Svedese"

#: wp-includes/script-loader.php:435
msgid "Slovenian"
msgstr "Sloveno"

#: wp-includes/script-loader.php:433
msgid "Serbian"
msgstr "Serbo"

#: wp-includes/script-loader.php:429
msgid "Polish"
msgstr "Polacco"

#: wp-includes/script-loader.php:407
msgid "Finnish"
msgstr "Finlandese"

#: wp-includes/script-loader.php:439
msgid "Tagalog"
msgstr "Tagalog"

#: wp-includes/script-loader.php:431
msgid "Romanian"
msgstr "Romeno"

#: wp-includes/script-loader.php:428
msgid "Persian"
msgstr "Persiano"

#: wp-includes/script-loader.php:417
msgid "Indonesian"
msgstr "Indonesiano"

#: wp-includes/script-loader.php:396
msgid "Catalan"
msgstr "Catalano"

#: wp-includes/ms-functions.php:184
msgid "User cannot be added to this site."
msgstr "L'utente non può essere aggiunto a questo sito"

#: wp-includes/script-loader.php:367
msgid "Jump forward %1 seconds"
msgstr "Salta in avanti di %1 secondi"

#: wp-includes/script-loader.php:437
msgid "Swahili"
msgstr "Swahili"

#: wp-includes/script-loader.php:412
msgid "Haitian Creole"
msgstr "Haitian Creole"

#: wp-includes/script-loader.php:409
msgid "Galician"
msgstr "Galician"

#: wp-includes/script-loader.php:391
msgid "Afrikaans"
msgstr "Afrikaans"

#: wp-includes/script-loader.php:386
msgid "Skip in %1 seconds"
msgstr "Salta in %1 secondi"

#: wp-includes/script-loader.php:386
msgid "Skip in 1 second"
msgstr "Salta in 1 secondo"

#: wp-includes/script-loader.php:385
msgid "Skip ad"
msgstr "Salta pubblicità"

#: wp-includes/script-loader.php:434
msgid "Slovak"
msgstr "Slovak"

#: wp-includes/script-loader.php:418
msgid "Irish"
msgstr "Irish"

#: wp-includes/script-loader.php:416
msgid "Icelandic"
msgstr "Icelandic"

#. translators: %s: Post custom field name
#: wp-includes/post-template.php:1031
msgctxt "Post custom field name"
msgid "%s:"
msgstr "%s:"

#: wp-includes/script-loader.php:374
msgid "Skip back 1 second"
msgstr "Torna indietro di 1 secondo"

#: wp-includes/script-loader.php:387
msgid "Source Chooser"
msgstr "Scelta sorgente"

#. translators: 1: comment author, 2: comment author's IP address, 3: comment
#. author's hostname
#. translators: 1: Comment author name, 2: comment author's IP address, 3:
#. comment author's hostname
#: wp-includes/pluggable.php:1497 wp-includes/pluggable.php:1657
msgid "Author: %1$s (IP address: %2$s, %3$s)"
msgstr "Autore: %1$s (Indirizzo IP: %2$s, %3$s)"

#: wp-includes/script-loader.php:406
msgid "Filipino"
msgstr "Filippino"

#: wp-includes/script-loader.php:390
msgid "Live Broadcast"
msgstr "Diretta streaming"

#: wp-includes/script-loader.php:478
msgid "Something went wrong. Your change may not have been saved. Please try again. There is also a chance that you may need to manually fix and upload the file over FTP."
msgstr "C'e stato un errore. I tuoi cambiamenti potrebbero non essere stati salvati. Riprova. C'e la possibilità che tu debba risolvere manualmente il problema e caricare il file tramite FTP."

#: wp-includes/post.php:201
msgid "oEmbed Response"
msgstr "Risposta oEmbed"

#: wp-includes/post.php:200
msgid "oEmbed Responses"
msgstr "Risposte oEmbed"

#. translators: Do not translate OLD_EMAIL, NEW_EMAIL, SITENAME, SITEURL: those
#. are placeholders.
#: wp-includes/ms-functions.php:2689
msgid ""
"Hi,\n"
"\n"
"This notice confirms that the network admin email address was changed on ###SITENAME###.\n"
"\n"
"The new network admin email address is ###NEW_EMAIL###.\n"
"\n"
"This email has been sent to ###OLD_EMAIL###\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao,\n"
"\n"
"l'indirizzo email dell'amministratore della rete ###SITENAME### è cambiato.\n"
"\n"
"Il nuovo indirizzo email è ###NEW_EMAIL###.\n"
"\n"
"Questa comunicazione è stata inviata a ###OLD_EMAIL###\n"
"\n"
"Ciao,\n"
"###SITENAME###\n"
"###SITEURL###"

#: wp-includes/script-loader.php:368
msgid "Toggle Loop"
msgstr "Attiva/disattiva la riproduzione automatica"

#. translators: Do not translate USERNAME, ADMIN_URL, EMAIL, SITENAME, SITEURL:
#. those are placeholders.
#: wp-includes/ms-functions.php:2601
msgid ""
"Howdy ###USERNAME###,\n"
"\n"
"You recently requested to have the network admin email address on\n"
"your network changed.\n"
"\n"
"If this is correct, please click on the following link to change it:\n"
"###ADMIN_URL###\n"
"\n"
"You can safely ignore and delete this email if you do not want to\n"
"take this action.\n"
"\n"
"This email has been sent to ###EMAIL###\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao ###USERNAME###,\n"
"\n"
"Recentemente hai richiesto il cambio dell'indirizzo mail dell'amministratore\n"
"della tua rete.\n"
"\n"
"Per confermare, fai click sul seguente link:\n"
"###ADMIN_URL###\n"
"\n"
"Puoi tranquillamente ignorare e cancellare questa mail\n"
"se non vuoi procedere con la richiesta.\n"
"\n"
"Questa mail è stata spedita a ###EMAIL###\n"
"\n"
"Saluti,\n"
"Tutto da ###SITENAME###\n"
"###SITEURL###"

#: wp-includes/rest-api.php:1115
msgid "%1$s is not a valid property of Object."
msgstr "%1$s non è una proprietà valida di Object."

#: wp-includes/script-loader.php:399
msgid "Chinese (Traditional)"
msgstr "Cinese (tradizionale)"

#: wp-includes/script-loader.php:398
msgid "Chinese (Simplified)"
msgstr "Cinese (semplificato)"

#: wp-includes/script-loader.php:395
msgid "Bulgarian"
msgstr "Bulgaro"

#: wp-includes/script-loader.php:394
msgid "Belarusian"
msgstr "Bielorusso"

#: wp-includes/script-loader.php:389
msgid "Speed Rate"
msgstr "Velocità di riproduzione"

#. translators: revision date format, see https://secure.php.net/date
#: wp-includes/post-template.php:1709 wp-includes/post-template.php:1745
msgctxt "revision date format"
msgid "F j, Y @ H:i:s"
msgstr "j F Y, G:i:s"

#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:259
msgid "Whether or not the post type can be viewed."
msgstr "Se il tipo di contenuto può essere visualizzato oppure no."

#: wp-includes/post.php:285
msgctxt "request status"
msgid "Pending"
msgstr "In attesa"

#: wp-includes/post.php:292
msgctxt "request status"
msgid "Confirmed"
msgstr "Confermato"

#: wp-includes/post.php:785
msgid "Confirmed"
msgstr "Confermato"

#: wp-includes/post.php:306
msgctxt "request status"
msgid "Completed"
msgstr "Completato"

#: wp-includes/post.php:787
msgid "Completed"
msgstr "Completato"

#: wp-includes/post.php:215
msgid "User Requests"
msgstr "Richieste utente"

#: wp-includes/post.php:216
msgid "User Request"
msgstr "Richiesta utente"

#: wp-includes/media.php:3969
msgid "WordPress Media"
msgstr "Media WordPress"

#: wp-includes/post.php:784
msgid "Pending"
msgstr "In attesa"

#: wp-includes/media.php:3526
msgctxt "media items"
msgid "Mine"
msgstr "I miei elementi"

#: wp-includes/post.php:299
msgctxt "request status"
msgid "Failed"
msgstr "Mancato"

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:1382
msgid "Limit result set to users who are considered authors."
msgstr "Limita il risultato impostato agli utenti che sono considerati autori."

#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:199
msgid "Sorry, you are not allowed to query users by this parameter."
msgstr "Non hai i permessi per fare delle query utenti con questo parametro."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2168
msgid "The current user can publish this post."
msgstr "L'utente corrente può pubblicare questo articolo."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2185
msgid "The current user can sticky this post."
msgstr "L'utente corrente può mettere in evidenza questo post."

#. translators: %s: taxonomy name
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2222
msgid "The current user can create terms in the %s taxonomy."
msgstr "L'utente corrente può creare termini nella tassonomia %s."

#. translators: %s: taxonomy name
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2220
msgid "The current user can assign terms in the %s taxonomy."
msgstr "L'utente corrente può assegnare termini nella tassonomia %s."

#. translators: %s: required disk space in kilobytes
#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:800
msgid "Not enough space to upload. %s KB needed."
msgstr "Non c'è abbastanza spazio per effettuare il caricamento. Sono necessari %s KB."

#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:2201
msgid "The current user can change the author on this post."
msgstr "L'utente corrente può modificare l'autore di questo post."

#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:809
msgid "You have used your space quota. Please delete files before uploading."
msgstr "Hai superato la tua quota di spazio. Elimina dei file prima di effettuare un caricamento."

#. translators: %s: maximum allowed file size in kilobytes
#: wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php:805
msgid "This file is too big. Files must be less than %s KB in size."
msgstr "Questo file è troppo grande. I file devono essere meno di  %s KB come dimensione."

#: wp-includes/post.php:66
msgctxt "add new media"
msgid "Add New"
msgstr "Aggiungi media"

#: wp-includes/post.php:1446
msgctxt "post"
msgid "Add New"
msgstr "Aggiungi articolo"

#: wp-includes/post.php:1446
msgctxt "page"
msgid "Add New"
msgstr "Aggiungi pagina"

#. translators: Network admin email change notification email subject. %s:
#. Network title
#: wp-includes/ms-functions.php:2704
msgid "[%s] Notice of Network Admin Email Change"
msgstr "[%s] Avviso del cambio di email dell'amministratore del network"

#: wp-includes/ms-functions.php:2647
msgid "[%s] New Network Admin Email Address"
msgstr "[%s] Nuovo indirizzo email dell'amministratore del network"

#: wp-includes/ms-functions.php:992
msgid ""
"To activate your user, please click the following link:\n"
"\n"
"%s\n"
"\n"
"After you activate, you will receive *another email* with your login."
msgstr ""
"Per attivare il tuo utente, fai click sul link seguente:\n"
"\n"
"%s\n"
"\n"
"Dopo l'attivazione, riceverai un'altra email con i dettagli per il login."

#: wp-includes/pluggable.php:537
msgid "<strong>ERROR</strong>: Invalid username, email address or incorrect password."
msgstr "<strong>ERRORE</strong>: nome utente non valido, indirizzo email o password non corretti."

#: wp-includes/post.php:126 wp-includes/post.php:127
msgid "Custom CSS"
msgstr "CSS personalizzato"

#: wp-includes/class-wp-theme.php:780
msgid "Blue"
msgstr "Blu"

#: wp-includes/class-wp-theme.php:781
msgid "Gray"
msgstr "Grigio"

#. translators: %s: search phrase
#: wp-includes/general-template.php:977
msgid "Search Results for &#8220;%s&#8221;"
msgstr "Risultati della ricerca per &#8220;%s&#8221;"

#: wp-includes/script-loader.php:377 wp-includes/media-template.php:629
#: wp-includes/media-template.php:656 wp-includes/media-template.php:734
#: wp-includes/media-template.php:860 wp-includes/media-template.php:875
#: wp-includes/media-template.php:926 wp-includes/media-template.php:988
#: wp-includes/media-template.php:1086 wp-includes/media-template.php:1175
#: wp-includes/deprecated.php:688
msgid "None"
msgstr "Nessuna"

#: wp-includes/comment-template.php:1082 wp-includes/comment-template.php:2231
msgctxt "noun"
msgid "Comment"
msgstr "Commento"

#: wp-includes/script-loader.php:656 wp-includes/comment.php:3260
msgid "Comments"
msgstr "Commenti"

#: wp-includes/comment-template.php:2200
msgid "Name"
msgstr "Nome"

#. translators: Name of a comment's author after being anonymized.
#: wp-includes/comment.php:3330 wp-includes/class-wp-theme.php:732
#: wp-includes/comment-template.php:31
msgid "Anonymous"
msgstr "Anonimo"

#: wp-includes/comment-template.php:1084
msgid "Pingback"
msgstr "Pingback"

#: wp-includes/comment-template.php:1083
msgid "Trackback"
msgstr "Trackback"

#: wp-includes/widgets/class-wp-widget-archives.php:30
#: wp-includes/widgets/class-wp-widget-archives.php:43
#: wp-includes/general-template.php:1512
#: wp-includes/theme-compat/sidebar.php:91
msgid "Archives"
msgstr "Archivi"

#: wp-login.php:1031 wp-includes/general-template.php:397
msgid "Password"
msgstr "Password"

#: wp-includes/media-template.php:694 wp-includes/media-template.php:769
#: wp-includes/media-template.php:943 wp-includes/media.php:3179
msgid "Thumbnail"
msgstr "Miniatura"

#. translators: %s: file name
#: wp-includes/deprecated.php:3182
msgid "File &#8220;%s&#8221; doesn&#8217;t exist?"
msgstr "Il file &#8220;%s&#8221; non esiste?"

#: wp-includes/deprecated.php:3186
msgid "The GD image library is not installed."
msgstr "La GD image library non è installata."

#: wp-includes/media-template.php:340 wp-includes/media-template.php:504
msgid "Saved."
msgstr "Salvato."

#: wp-includes/media-template.php:695 wp-includes/media-template.php:770
#: wp-includes/media-template.php:944 wp-includes/media.php:3180
msgid "Medium"
msgstr "Media"

#: wp-includes/widgets/class-wp-widget-media-image.php:64
#: wp-includes/media-template.php:685 wp-includes/media-template.php:759
#: wp-includes/media-template.php:934
msgid "Size"
msgstr "Dimensione"

#: wp-includes/media-template.php:869 wp-includes/media-template.php:981
msgid "Image URL"
msgstr "URL immagine"

#: wp-includes/class-wp-theme.php:780
msgid "Black"
msgstr "Nero"

#: wp-includes/class-wp-theme.php:780
msgid "Brown"
msgstr "Marrone"

#: wp-includes/class-wp-theme.php:781
msgid "Green"
msgstr "Verde"

#: wp-includes/class-wp-theme.php:781
msgid "Orange"
msgstr "Arancione"

#: wp-includes/class-wp-theme.php:782
msgid "Pink"
msgstr "Rosa"

#: wp-includes/class-wp-theme.php:782
msgid "Purple"
msgstr "Viola"

#: wp-includes/class-wp-theme.php:782
msgid "Red"
msgstr "Rosso"

#: wp-includes/class-wp-theme.php:783
msgid "Silver"
msgstr "Argento"

#: wp-includes/class-wp-theme.php:783
msgid "White"
msgstr "Bianco"

#: wp-includes/class-wp-theme.php:784
msgid "Yellow"
msgstr "Giallo"

#: wp-includes/class-wp-theme.php:784
msgid "Light"
msgstr "Chiaro"

#: wp-includes/class-wp-widget.php:143
msgid "There are no options for this widget."
msgstr "Non vi sono opzioni per questo widget."

#: wp-includes/class-wp-xmlrpc-server.php:610
msgid "Allow people to post comments on new articles"
msgstr "Consenti la scrittura di commenti per i nuovi articoli"

#: wp-includes/class-wp-xmlrpc-server.php:555
msgid "Time Format"
msgstr "Formato ora"

#: wp-includes/comment-template.php:2204
msgid "Website"
msgstr "Sito web"

#: wp-includes/class-wp-xmlrpc-server.php:4805
msgid "For some strange yet very annoying reason, this post could not be edited."
msgstr "Per qualche strano motivo, non è possibile modificare questo articolo."

#: wp-includes/class-wp-xmlrpc-server.php:3634
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:465
#: wp-includes/comment.php:3039
msgid "Sorry, comments are closed for this item."
msgstr "I commenti sono chiusi per questo articolo."

#: wp-includes/link-template.php:2302 wp-includes/link-template.php:2354
msgid "&laquo; Previous Page"
msgstr "&laquo; Pagina precedente"

#: wp-includes/link-template.php:2219 wp-includes/link-template.php:2355
msgid "Next Page &raquo;"
msgstr "Pagina successiva &raquo;"

#. translators: Comments feed title. 1: Post title
#: wp-includes/feed-atom-comments.php:33
msgid "Comments on %s"
msgstr "Commenti a %s"

#: wp-includes/comment-template.php:921
msgid "No Comments"
msgstr "Nessun commento"

#: wp-includes/comment-template.php:923
msgid "1 Comment"
msgstr "1 commento"

#: wp-includes/link-template.php:994 wp-includes/link-template.php:1329
#: wp-includes/link-template.php:1431 wp-includes/link-template.php:1492
msgid "Edit This"
msgstr "Modifica"

#: wp-includes/comment-template.php:1712
msgid "Leave a Comment"
msgstr "Lascia un commento"

#: wp-includes/comment.php:241
msgid "Unapproved"
msgstr "Non approvato"

#: wp-includes/comment.php:684 wp-includes/comment.php:687
#: wp-includes/comment.php:690
msgid "Duplicate comment detected; it looks as though you&#8217;ve already said that!"
msgstr "Identificato un commento duplicato; sembra che tu abbia già scritto questo commento!"

#: wp-includes/comment.php:2143
msgid "Could not update comment status"
msgstr "Impossibile aggiornare lo stato del commento"

#: wp-includes/cron.php:428
msgid "Once Hourly"
msgstr "Ogni ora"

#: wp-includes/cron.php:429
msgid "Twice Daily"
msgstr "Due volte al giorno"

#: wp-includes/cron.php:430
msgid "Once Daily"
msgstr "Ogni giorno"

#: wp-includes/deprecated.php:64
msgid "new WordPress Loop"
msgstr "nuovo WordPress Loop"

#: wp-includes/deprecated.php:963
msgid "Last updated"
msgstr "Ultimo aggiornamento"

#. translators: Comments feed title. 1: Post title
#: wp-includes/feed-rss2-comments.php:38
msgid "Comments on: %s"
msgstr "Commenti a: %s"

#. translators: Comments feed title. 1: Site name, 2: Search query
#: wp-includes/feed-rss2-comments.php:41 wp-includes/feed-atom-comments.php:36
msgid "Comments for %1$s searching on %2$s"
msgstr "Commenti per %1$s ricercati in %2$s"

#. translators: Comments feed title. 1: Site name
#: wp-includes/feed-rss2-comments.php:44 wp-includes/feed-atom-comments.php:39
msgid "Comments for %s"
msgstr "Commenti per %s"

#. translators: Individual comment title. 1: Post title, 2: Comment author name
#: wp-includes/feed-rss2-comments.php:80 wp-includes/feed-atom-comments.php:81
msgid "Comment on %1$s by %2$s"
msgstr "Commenti su %1$s di %2$s"

#. translators: Comment author title. 1: Comment author name
#: wp-includes/feed-rss2-comments.php:83 wp-includes/feed-atom-comments.php:84
msgid "By: %s"
msgstr "Di: %s"

#. translators: Time difference between two dates, in minutes (min=minute). 1:
#. Number of minutes
#: wp-includes/formatting.php:3246
msgid "%s min"
msgid_plural "%s mins"
msgstr[0] "%s minuto"
msgstr[1] "%s minuti"

#. translators: Time difference between two dates, in hours. 1: Number of hours
#: wp-includes/formatting.php:3252
msgid "%s hour"
msgid_plural "%s hours"
msgstr[0] "%s ora"
msgstr[1] "%s ore"

#. translators: Time difference between two dates, in days. 1: Number of days
#: wp-includes/formatting.php:3258
msgid "%s day"
msgid_plural "%s days"
msgstr[0] "%s giorno"
msgstr[1] "%s giorni"

#. translators: used between list items, there is a space after the comma
#: wp-includes/class-wp-theme.php:739
msgid ", "
msgstr ", "

#. translators: %s: directory path
#: wp-includes/functions.php:1918 wp-includes/functions.php:2187
msgid "Unable to create directory %s. Is its parent directory writable by the server?"
msgstr "Impossibile creare la directory %s. Verifica che la directory madre sia scrivibile dal server!"

#: wp-includes/functions.php:2149
msgid "Empty filename"
msgstr "Nome file vuoto"

#: wp-includes/functions.php:2195
msgid "Could not write file %s"
msgstr "Impossibile scrivere il file %s"

#: wp-includes/functions.php:2786
msgid "&laquo; Back"
msgstr "&laquo; Indietro"

#: wp-load.php:92 wp-includes/functions.php:2798
msgid "WordPress &rsaquo; Error"
msgstr "WordPress &rsaquo; Errore"

#. translators: 1: PHP function name, 2: version number, 3: alternative
#. function name
#. translators: 1: PHP file name, 2: version number, 3: alternative file name
#. translators: 1: WordPress hook name, 2: version number, 3: alternative hook
#. name
#: wp-includes/functions.php:3888 wp-includes/functions.php:4016
#: wp-includes/functions.php:4139
msgid "%1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr "%1$s è <strong>deprecata</strong> dalla versione %2$s! Utilizzare al suo posto %3$s."

#. translators: 1: PHP function name, 2: version number
#. translators: 1: PHP file name, 2: version number
#. translators: 1: WordPress hook name, 2: version number
#: wp-includes/functions.php:3891 wp-includes/functions.php:4019
#: wp-includes/functions.php:4142
msgid "%1$s is <strong>deprecated</strong> since version %2$s with no alternative available."
msgstr "%1$s è <strong>deprecata</strong> dalla versione %2$s senza alcuna alternativa disponibile."

#: wp-includes/functions.php:4719
msgid "Select a city"
msgstr "Seleziona una città"

#: wp-login.php:608 wp-login.php:737 wp-login.php:825 wp-login.php:1061
#: wp-includes/general-template.php:527
msgid "Register"
msgstr "Registrati"

#: wp-includes/general-template.php:531
msgid "Site Admin"
msgstr "Amministra sito"

#. translators: 1: separator, 2: search phrase
#: wp-includes/general-template.php:1182
msgid "Search Results %1$s %2$s"
msgstr "Risultati della ricerca %1$s %2$s"

#. translators: 1: month name, 2: 4-digit year
#: wp-includes/media.php:3435 wp-includes/general-template.php:1783
msgid "%1$s %2$d"
msgstr "%1$s %2$d"

#. translators: Calendar caption: 1: month name, 2: 4-digit year
#: wp-includes/general-template.php:2008
msgctxt "calendar caption"
msgid "%1$s %2$s"
msgstr "%1$s: %2$s"

#. translators: Separator between blog name and feed type in feed links
#: wp-includes/general-template.php:2646 wp-includes/general-template.php:2688
msgctxt "feed link"
msgid "&raquo;"
msgstr "&raquo;"

#. translators: 1: blog title, 2: separator (raquo)
#: wp-includes/general-template.php:2648
msgid "%1$s %2$s Feed"
msgstr "%1$s %2$s Feed"

#. translators: 1: blog title, 2: separator (raquo)
#: wp-includes/general-template.php:2650
msgid "%1$s %2$s Comments Feed"
msgstr "%1$s %2$s Feed dei commenti"

#. translators: 1: blog name, 2: separator(raquo), 3: post title
#: wp-includes/general-template.php:2690
msgid "%1$s %2$s %3$s Comments Feed"
msgstr "%1$s %2$s %3$s Feed dei commenti"

#. translators: 1: blog name, 2: separator(raquo), 3: category name
#: wp-includes/general-template.php:2692
msgid "%1$s %2$s %3$s Category Feed"
msgstr "%1$s %2$s %3$s Feed della categoria"

#. translators: 1: blog name, 2: separator(raquo), 3: tag name
#: wp-includes/general-template.php:2694
msgid "%1$s %2$s %3$s Tag Feed"
msgstr "%1$s %2$s %3$s Feed del tag"

#. translators: 1: blog name, 2: separator(raquo), 3: author name
#: wp-includes/general-template.php:2698
msgid "%1$s %2$s Posts by %3$s Feed"
msgstr "%1$s %2$s Articoli per il feed %3$s"

#. translators: 1: blog name, 2: separator(raquo), 3: search phrase
#: wp-includes/general-template.php:2700
msgid "%1$s %2$s Search Results for &#8220;%3$s&#8221; Feed"
msgstr "%1$s %2$s Risultati della ricerca per i feed di &#8220;%3$s&#8221;"

#: wp-includes/general-template.php:3734
msgid "&laquo; Previous"
msgstr "&laquo; Precedente"

#: wp-includes/general-template.php:3735
msgid "Next &raquo;"
msgstr "Successivo &raquo;"

#. translators: month name
#: wp-includes/class-wp-locale.php:156 wp-includes/class-wp-locale.php:184
msgid "January"
msgstr "gennaio"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:157 wp-includes/class-wp-locale.php:185
msgid "February"
msgstr "febbraio"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:158 wp-includes/class-wp-locale.php:186
msgid "March"
msgstr "marzo"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:159 wp-includes/class-wp-locale.php:187
msgid "April"
msgstr "aprile"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:160 wp-includes/class-wp-locale.php:188
msgid "May"
msgstr "maggio"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:161 wp-includes/class-wp-locale.php:189
msgid "June"
msgstr "giugno"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:162 wp-includes/class-wp-locale.php:190
msgid "July"
msgstr "luglio"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:163 wp-includes/class-wp-locale.php:191
msgid "August"
msgstr "agosto"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:164 wp-includes/class-wp-locale.php:192
msgid "September"
msgstr "settembre"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:165 wp-includes/class-wp-locale.php:193
msgid "October"
msgstr "ottobre"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:166 wp-includes/class-wp-locale.php:194
msgid "November"
msgstr "novembre"

#. translators: month name
#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:167 wp-includes/class-wp-locale.php:195
msgid "December"
msgstr "dicembre"

#. translators: weekday
#: wp-includes/class-wp-locale.php:129 wp-includes/class-wp-locale.php:138
#: wp-includes/class-wp-locale.php:147
msgid "Sunday"
msgstr "domenica"

#. translators: weekday
#. translators: one-letter abbreviation of the weekday
#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:130 wp-includes/class-wp-locale.php:139
#: wp-includes/class-wp-locale.php:148
msgid "Monday"
msgstr "lunedì"

#. translators: weekday
#. translators: one-letter abbreviation of the weekday
#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:131 wp-includes/class-wp-locale.php:140
#: wp-includes/class-wp-locale.php:149
msgid "Tuesday"
msgstr "martedì"

#. translators: weekday
#. translators: one-letter abbreviation of the weekday
#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:132 wp-includes/class-wp-locale.php:141
#: wp-includes/class-wp-locale.php:150
msgid "Wednesday"
msgstr "mercoledì"

#. translators: weekday
#. translators: one-letter abbreviation of the weekday
#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:133 wp-includes/class-wp-locale.php:142
#: wp-includes/class-wp-locale.php:151
msgid "Thursday"
msgstr "giovedì"

#. translators: weekday
#. translators: one-letter abbreviation of the weekday
#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:134 wp-includes/class-wp-locale.php:143
#: wp-includes/class-wp-locale.php:152
msgid "Friday"
msgstr "venerdì"

#. translators: weekday
#. translators: one-letter abbreviation of the weekday
#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:135 wp-includes/class-wp-locale.php:144
#: wp-includes/class-wp-locale.php:153
msgid "Saturday"
msgstr "sabato"

#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:147
msgid "Sun"
msgstr "dom"

#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:148
msgid "Mon"
msgstr "lun"

#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:149
msgid "Tue"
msgstr "mar"

#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:150
msgid "Wed"
msgstr "mer"

#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:151
msgid "Thu"
msgstr "gio"

#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:152
msgid "Fri"
msgstr "ven"

#. translators: three-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:153
msgid "Sat"
msgstr "sab"

#: wp-includes/widgets/class-wp-widget-media-audio.php:70
#: wp-includes/widgets/class-wp-widget-media-video.php:72
#: wp-includes/media-template.php:1097 wp-includes/media-template.php:1186
msgid "Loop"
msgstr "Ciclo"

#: wp-includes/media-template.php:848 wp-includes/media-template.php:914
msgid "Align"
msgstr "Allinea"

#: wp-includes/customize/class-wp-customize-background-position-control.php:52
msgid "Bottom Left"
msgstr "In basso a sinistra"

#: wp-includes/customize/class-wp-customize-background-position-control.php:54
msgid "Bottom Right"
msgstr "In basso a destra"

#: wp-includes/media-template.php:330 wp-includes/media-template.php:528
#: wp-includes/media.php:3569
msgid "Edit Image"
msgstr "Modifica immagine"

#: wp-includes/media-template.php:697 wp-includes/media-template.php:772
#: wp-includes/media-template.php:946 wp-includes/media.php:3182
msgid "Full Size"
msgstr "Dimensione reale"

#: wp-includes/widgets/class-wp-widget-media-image.php:129
#: wp-includes/media-template.php:1011
msgid "Link Rel"
msgstr "Link Rel"

#: wp-includes/link-template.php:699
msgid "Comments Feed"
msgstr "Feed dei commenti"

#: wp-includes/link-template.php:1781 wp-includes/link-template.php:2023
msgid "Previous Post"
msgstr "Articolo precedente"

#: wp-includes/link-template.php:1781 wp-includes/link-template.php:2023
msgid "Next Post"
msgstr "Articolo successivo"

#: wp-includes/deprecated.php:2679
msgid "First Post"
msgstr "Primo articolo"

#: wp-includes/deprecated.php:2679
msgid "Last Post"
msgstr "Ultimo articolo"

#: wp-includes/link-template.php:2693
msgid "Newer Comments &raquo;"
msgstr "Commenti seguenti &raquo;"

#: wp-includes/link-template.php:2737
msgid "&laquo; Older Comments"
msgstr "&laquo; Commenti precedenti"

#: wp-includes/class-wp-locale.php:198
msgid "am"
msgstr "am"

#: wp-includes/class-wp-locale.php:199
msgid "pm"
msgstr "pm"

#: wp-includes/class-wp-locale.php:200
#: wp-includes/customize/class-wp-customize-date-time-control.php:172
msgid "AM"
msgstr "AM"

#: wp-includes/class-wp-locale.php:201
#: wp-includes/customize/class-wp-customize-date-time-control.php:173
msgid "PM"
msgstr "PM"

#. translators: $thousands_sep argument for
#. https://secure.php.net/number_format, default is ,
#: wp-includes/class-wp-locale.php:207
msgid "number_format_thousands_sep"
msgstr "."

#: wp-includes/post-template.php:1508 wp-includes/deprecated.php:1848
msgid "Missing Attachment"
msgstr "Allegato mancante"

#: wp-includes/class-wp-theme.php:231
msgid "Stylesheet is missing."
msgstr "Foglio di stile mancante."

#: wp-login.php:1042 wp-includes/general-template.php:398
msgid "Remember Me"
msgstr "Ricordami"

#: wp-includes/class-wp-xmlrpc-server.php:473
msgid "Software Name"
msgstr "Nome software"

#: wp-includes/class-wp-xmlrpc-server.php:478
msgid "Software Version"
msgstr "Versione software"

#: wp-includes/class-wp-xmlrpc-server.php:535
msgid "Time Zone"
msgstr "Fuso orario"

#: wp-includes/class-wp-xmlrpc-server.php:560
msgid "Allow new users to sign up"
msgstr "Permettere ai nuovi utenti di iscriversi"

#: wp-includes/class-wp-xmlrpc-server.php:2758
#: wp-includes/class-wp-xmlrpc-server.php:2878
#: wp-includes/class-wp-xmlrpc-server.php:2940
msgid "Sorry, no such page."
msgstr "Questa pagina non esiste."

#: wp-includes/class-wp-xmlrpc-server.php:3312
#: wp-includes/class-wp-xmlrpc-server.php:3444
#: wp-includes/class-wp-xmlrpc-server.php:3511
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:312
msgid "Invalid comment ID."
msgstr "ID commento non valido."

#: wp-includes/class-wp-xmlrpc-server.php:3369
#: wp-includes/class-wp-xmlrpc-server.php:3526
msgid "Invalid comment status."
msgstr "Stato del commento non valido."

#: wp-includes/class-wp-xmlrpc-server.php:3612
msgid "You must be registered to comment."
msgstr "Occorre essere registrati per poter inviare commenti"

#: wp-includes/class-wp-xmlrpc-server.php:4621
#: wp-includes/class-wp-xmlrpc-server.php:6003
msgid "Either there are no posts, or something went wrong."
msgstr "O non vi sono articoli o qualcosa è andato storto."

#: wp-includes/class-wp-xmlrpc-server.php:4781
#: wp-includes/class-wp-xmlrpc-server.php:4853
#: wp-includes/class-wp-xmlrpc-server.php:5685
#: wp-includes/class-wp-xmlrpc-server.php:6219
msgid "Sorry, no such post."
msgstr "Questo articolo non esiste."

#: wp-includes/class-wp-xmlrpc-server.php:6366
#: wp-includes/class-wp-xmlrpc-server.php:6373
#: wp-includes/class-wp-xmlrpc-server.php:6380
#: wp-includes/class-wp-xmlrpc-server.php:6530
msgid "The specified target URL cannot be used as a target. It either doesn&#8217;t exist, or it is not a pingback-enabled resource."
msgstr "L'URL indicata non può essere usata come destinazione. O non esiste o non è una risorsa abilitata al pingback."

#: wp-includes/class-wp-xmlrpc-server.php:6376
msgid "The source URL and the target URL cannot both point to the same resource."
msgstr "L'URL di partenza e l'URL d'arrivo non possono puntare alla stessa risorsa."

#: wp-includes/class-wp-xmlrpc-server.php:6409
msgid "The source URL does not exist."
msgstr "L'URL sorgente non esiste."

#: wp-includes/class-wp-xmlrpc-server.php:6430
msgid "We cannot find a title on that page."
msgstr "Non possiamo trovare un titolo in quella pagina."

#: wp-includes/class-wp-xmlrpc-server.php:6467
msgid "The source URL does not contain a link to the target URL, and so cannot be used as a source."
msgstr "L'URL d'origine non contiene un link all'URL di destinazione e quindi non può essere utilizzato come origine."

#. translators: 1: URL of the page linked from, 2: URL of the page linked to
#: wp-includes/class-wp-xmlrpc-server.php:6504
msgid "Pingback from %1$s to %2$s registered. Keep the web talking! :-)"
msgstr "Pingback da %1$s a %2$s registrato. Facciamo parlare il web! :-)"

#: wp-login.php:811 wp-includes/comment-template.php:2202
msgid "Email"
msgstr "Email"

#: wp-includes/functions.php:4764 wp-includes/functions.php:4768
msgid "UTC"
msgstr "UTC"

#: wp-includes/functions.php:4772
msgid "Manual Offsets"
msgstr "Offset manuale"

#. translators: $dec_point argument for https://secure.php.net/number_format,
#. default is .
#: wp-includes/class-wp-locale.php:220
msgid "number_format_decimal_point"
msgstr ","

#: wp-includes/class-wp-image-editor-imagick.php:258
#: wp-includes/class-wp-image-editor-gd.php:178
msgid "Could not calculate resized image dimensions"
msgstr "Impossibile calcolare la dimensioni dell'immagine ridimensionata"

#: wp-includes/class-wp-theme.php:291
msgid "The parent theme is missing. Please install the \"%s\" parent theme."
msgstr "Il tema padre è mancante. Installare il tema padre \"%s\"."

#: wp-includes/load.php:219
msgid "Maintenance"
msgstr "In manutenzione."

#: wp-includes/class-wp-xmlrpc-server.php:6331
msgid "Is there no link to us?"
msgstr "Non ci sono link a noi?"

#: wp-includes/load.php:223
msgid "Briefly unavailable for scheduled maintenance. Check back in a minute."
msgstr "Momentaneamente non disponibile per manutenzione. Riprovare fra un minuto."

#: wp-includes/class-wp-xmlrpc-server.php:3265
#: wp-includes/class-wp-xmlrpc-server.php:5831
#: wp-includes/class-wp-xmlrpc-server.php:6053
msgid "Sorry, you must be able to edit posts on this site in order to view categories."
msgstr "Occorre avere la possibilità di modificare gli articoli di questo sito per poter visualizzare le categorie."

#: wp-includes/class-wp-xmlrpc-server.php:3087
msgid "Sorry, you must be able to edit posts on this site in order to view tags."
msgstr "Occorre avere la possibilità di modificare gli articoli di questo sito per poter visualizzare i tag."

#: wp-includes/class-wp-xmlrpc-server.php:545
msgid "Site Tagline"
msgstr "Motto del sito"

#: wp-includes/link-template.php:3705
msgid "This is the short link."
msgstr "Questo è il link breve."

#: wp-includes/load.php:538
msgid "The site you have requested is not installed properly. Please contact the system administrator."
msgstr "Il sito richiesto non è installato correttamente. Contattare l'amministratore di sistema."

#. translators: 1: PHP function name, 2: version number, 3: optional message
#. regarding the change
#: wp-includes/functions.php:4080
msgid "%1$s was called with an argument that is <strong>deprecated</strong> since version %2$s! %3$s"
msgstr "%1$s è stato chiamato con un argomento <strong>deprecato</strong> dalla versione %2$s! %3$s."

#: wp-includes/cron.php:205
msgid "This argument has changed to an array to match the behavior of the other cron functions."
msgstr "Questo argomento è stato trasformato in un array per adattarsi al funzionamento di altre funzioni cron."

#: wp-includes/comment-template.php:2219
msgid "Required fields are marked %s"
msgstr "I campi obbligatori sono contrassegnati %s"

#. translators: Developer debugging message. 1: PHP function name, 2:
#. Explanatory message, 3: Version information message
#: wp-includes/functions.php:4196
msgid "%1$s was called <strong>incorrectly</strong>. %2$s %3$s"
msgstr "%1$s è stato richiamato <strong>in maniera scorretta</strong>. %2$s %3$s"

#: wp-includes/class-wp-theme.php:786
msgid "Blavatar"
msgstr "Blavatar"

#: wp-includes/class-wp-xmlrpc-server.php:565
msgid "Thumbnail Width"
msgstr "Larghezza miniatura"

#: wp-includes/class-wp-xmlrpc-server.php:570
msgid "Thumbnail Height"
msgstr "Altezza miniatura"

#: wp-includes/class-wp-xmlrpc-server.php:575
msgid "Crop thumbnail to exact dimensions"
msgstr "Ritaglia la miniatura alle dimensioni esatte"

#: wp-includes/class-wp-xmlrpc-server.php:580
msgid "Medium size image width"
msgstr "Larghezza dell'immagine di dimensione media"

#: wp-includes/class-wp-xmlrpc-server.php:585
msgid "Medium size image height"
msgstr "Altezza dell'immagine di dimensione media"

#: wp-includes/class-wp-xmlrpc-server.php:600
msgid "Large size image width"
msgstr "Larghezza dell'immagine di dimensione grande"

#: wp-includes/class-wp-xmlrpc-server.php:605
msgid "Large size image height"
msgstr "Altezza dell'immagine di dimensione grande"

#: wp-includes/class-wp-xmlrpc-server.php:1469
#: wp-includes/class-wp-xmlrpc-server.php:4027
#: wp-includes/class-wp-xmlrpc-server.php:5165
#: wp-includes/class-wp-xmlrpc-server.php:5530
msgid "Invalid attachment ID."
msgstr "ID allegato non valido."

#: wp-includes/class-wp-xmlrpc-server.php:4981
#: wp-includes/class-wp-xmlrpc-server.php:5305
msgid "Invalid post format."
msgstr "Formato articolo non valido"

#. translators: 1: Current PHP version number, 2: WordPress version number, 3:
#. Minimum required PHP version number
#: wp-includes/load.php:132
msgid "Your server is running PHP version %1$s but WordPress %2$s requires at least %3$s."
msgstr "Il server sta utilizzando PHP versione %1$s ma WordPress %2$s richiede almeno la versione %3$s."

#: wp-includes/script-loader.php:653 wp-includes/comment-template.php:1597
msgid "Reply"
msgstr "Rispondi"

#: wp-includes/comment-template.php:1874 wp-includes/comment-template.php:2257
msgid "Leave a Reply to %s"
msgstr "Rispondi a %s"

#: wp-includes/comment-template.php:2262
msgid "Cancel reply"
msgstr "Annulla risposta"

#: wp-includes/class-wp-xmlrpc-server.php:3170
#: wp-includes/class-wp-xmlrpc-server.php:3172
msgid "Sorry, the new category failed."
msgstr "L'inserimento di una nuova categoria non è riuscito."

#: wp-includes/widgets/class-wp-widget-media-audio.php:65
#: wp-includes/widgets/class-wp-widget-media-video.php:66
#: wp-includes/media-template.php:1082 wp-includes/media-template.php:1171
msgid "Preload"
msgstr "Precaricamento"

#: wp-includes/general-template.php:3820 wp-includes/formatting.php:3363
msgid "&hellip;"
msgstr "&hellip;"

#: wp-includes/functions.php:3823
msgid "Database Error"
msgstr "Errore del database"

#: wp-includes/wp-db.php:1577 wp-includes/functions.php:3827
#: wp-includes/ms-load.php:455
msgid "Error establishing a database connection"
msgstr "Errore nello stabilire una connessione al database"

#. translators: en dash
#: wp-includes/formatting.php:116
msgctxt "en dash"
msgid "&#8211;"
msgstr "&#8211;"

#. translators: em dash
#: wp-includes/formatting.php:118
msgctxt "em dash"
msgid "&#8212;"
msgstr "&#8212;"

#: wp-includes/general-template.php:3918
msgctxt "admin color scheme"
msgid "Blue"
msgstr "Blu"

#: wp-includes/comment.php:740 wp-includes/comment.php:886
#: wp-includes/comment.php:889
msgid "You are posting comments too quickly. Slow down."
msgstr "Stai scrivendo commenti troppo in fretta. Rallenta."

#. translators: Yearly archive title. 1: Year
#: wp-includes/general-template.php:1008 wp-includes/general-template.php:1477
msgctxt "yearly archives date format"
msgid "Y"
msgstr "Y"

#. translators: Monthly archive title. 1: Month name and year
#: wp-includes/general-template.php:1011 wp-includes/general-template.php:1480
msgctxt "monthly archives date format"
msgid "F Y"
msgstr "F Y"

#. translators: opening curly double quote
#: wp-includes/formatting.php:98 wp-includes/formatting.php:4815
msgctxt "opening curly double quote"
msgid "&#8220;"
msgstr "&#8220;"

#. translators: closing curly double quote
#: wp-includes/formatting.php:100
msgctxt "closing curly double quote"
msgid "&#8221;"
msgstr "&#8221;"

#. translators: apostrophe, for example in 'cause or can't
#: wp-includes/formatting.php:103
msgctxt "apostrophe"
msgid "&#8217;"
msgstr "&#8217;"

#. translators: prime, for example in 9' (nine feet)
#: wp-includes/formatting.php:106
msgctxt "prime"
msgid "&#8242;"
msgstr "&#8242;"

#. translators: double prime, for example in 9" (nine inches)
#: wp-includes/formatting.php:108
msgctxt "double prime"
msgid "&#8243;"
msgstr "&#8243;"

#. translators: opening curly single quote
#: wp-includes/formatting.php:111
msgctxt "opening curly single quote"
msgid "&#8216;"
msgstr "&#8216;"

#. translators: closing curly single quote
#: wp-includes/formatting.php:113
msgctxt "closing curly single quote"
msgid "&#8217;"
msgstr "&#8217;"

#: wp-includes/class-wp-xmlrpc-server.php:508
msgid "Image default size"
msgstr "Dimensioni predefinite immagine"

#: wp-includes/class-wp-xmlrpc-server.php:513
msgid "Image default align"
msgstr "Allineamento predefinito immagine"

#: wp-includes/class-wp-xmlrpc-server.php:1423
#: wp-includes/class-wp-xmlrpc-server.php:5021
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1018
msgid "Invalid author ID."
msgstr "ID autore non valido"

#: wp-includes/class-wp-xmlrpc-server.php:503
msgid "Image default link type"
msgstr "Tipo di link predefinito per le immagini"

#: wp-includes/class-wp-theme.php:303 wp-includes/class-wp-theme.php:307
msgid "The \"%s\" theme is not a valid parent theme."
msgstr "Il tema \"%s\" non è un tema genitore valido."

#: wp-includes/class-wp-xmlrpc-server.php:1973
#: wp-includes/class-wp-xmlrpc-server.php:2077
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:406
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:511
#: wp-includes/taxonomy.php:2061 wp-includes/taxonomy.php:2685
msgid "Parent term does not exist."
msgstr "Il termine genitore non esiste."

#: wp-includes/class-wp-xmlrpc-server.php:1501
#: wp-includes/class-wp-xmlrpc-server.php:2053
#: wp-includes/class-wp-xmlrpc-server.php:2149
#: wp-includes/class-wp-xmlrpc-server.php:2221 wp-includes/taxonomy.php:2052
msgid "Invalid term ID."
msgstr "ID del termine non valido."

#: wp-includes/class-wp-xmlrpc-server.php:1964
msgid "This taxonomy is not hierarchical."
msgstr "Questa tassonomia non è gerarchica."

#: wp-includes/class-wp-term.php:173
#: wp-includes/class-wp-xmlrpc-server.php:1945
#: wp-includes/class-wp-xmlrpc-server.php:2038
#: wp-includes/class-wp-xmlrpc-server.php:2140
#: wp-includes/class-wp-xmlrpc-server.php:2211
#: wp-includes/class-wp-xmlrpc-server.php:2270
#: wp-includes/class-wp-xmlrpc-server.php:2365
#: wp-includes/rest-api/endpoints/class-wp-rest-taxonomies-controller.php:163
#: wp-includes/class-wp-tax-query.php:548
#: wp-includes/class-wp-tax-query.php:555 wp-includes/taxonomy.php:420
#: wp-includes/taxonomy.php:652 wp-includes/taxonomy.php:752
#: wp-includes/taxonomy.php:931 wp-includes/taxonomy.php:1090
#: wp-includes/taxonomy.php:1901 wp-includes/taxonomy.php:2037
#: wp-includes/taxonomy.php:2300 wp-includes/taxonomy.php:2443
#: wp-includes/taxonomy.php:2644
msgid "Invalid taxonomy."
msgstr "Tassonomia non valida."

#: wp-includes/class-wp-xmlrpc-server.php:528
msgid "Post Thumbnail"
msgstr "Miniatura articolo"

#: wp-includes/class-wp-xmlrpc-server.php:728
msgid "Insufficient arguments passed to this XML-RPC method."
msgstr "Gli argomenti passati a questo metodo XML-RPC sono insufficienti"

#: wp-includes/class-wp-xmlrpc-server.php:1317
msgid "Sorry, you cannot stick a private post."
msgstr "Impossibile mettere in evidenza un articolo privato."

#. translators: %s: version number
#: wp-includes/functions.php:4189
msgid "(This message was added in version %s.)"
msgstr "(Questo messaggio è stato aggiunto nella versione %s.)"

#: wp-includes/customize/class-wp-customize-media-control.php:253
#: wp-includes/customize/class-wp-customize-media-control.php:258
msgid "Select file"
msgstr "Seleziona file"

#: wp-includes/customize/class-wp-customize-header-image-control.php:113
msgid "Remove image"
msgstr "Rimuovi immagine"

#: wp-includes/class-wp-xmlrpc-server.php:523
msgid "Stylesheet"
msgstr "Foglio di stile"

#: wp-includes/class-wp-theme.php:239
msgid "Stylesheet is not readable."
msgstr "Il foglio di stile non è leggibile."

#: wp-includes/class-wp-xmlrpc-server.php:1715
#: wp-includes/class-wp-xmlrpc-server.php:4863
#: wp-includes/rest-api/endpoints/class-wp-rest-revisions-controller.php:312
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:825
msgid "The post cannot be deleted."
msgstr "L'articolo non può essere eliminato."

#. translators: %s: database repair URL
#: wp-includes/functions.php:1413
msgid "One or more database tables are unavailable. The database may need to be <a href=\"%s\">repaired</a>."
msgstr "Una o più tabelle del database non sono disponibili. Il database potrebbe avere bisogno di essere <a href=\"%s\">riparato</a>."

#. translators: 1: wp_enqueue_scripts, 2: admin_enqueue_scripts, 3:
#. login_enqueue_scripts
#: wp-includes/functions.wp-scripts.php:43
msgid "Scripts and styles should not be registered or enqueued until the %1$s, %2$s, or %3$s hooks."
msgstr "Gli script e gli stili non dovrebbero essere registrati o accodati prima degli hooks %1$s,%2$s, o %3$s."

#: wp-includes/feed-rss2-comments.php:91
msgid "Protected Comments: Please enter your password to view comments."
msgstr "Commenti protetti: inserisci la tua password per visualizzare i commenti."

#: wp-includes/comment-template.php:1525
msgid "Enter your password to view comments."
msgstr "Inserisci la tua password per visualizzare i commenti."

#: wp-includes/class-wp-image-editor-imagick.php:156
#: wp-includes/class-wp-image-editor-gd.php:100
msgid "File is not an image."
msgstr "Il file non è una immagine"

#: wp-includes/widgets/class-wp-widget-media-image.php:90
#: wp-includes/media-template.php:908
msgid "Alternative Text"
msgstr "Testo alternativo"

#: wp-includes/media-template.php:174 wp-includes/media-template.php:180
msgid "Drop files to upload"
msgstr "Trascina file per caricare"

#: wp-includes/widgets/class-wp-widget-media-image.php:97
#: wp-includes/media-template.php:640 wp-includes/media-template.php:718
#: wp-includes/media-template.php:866 wp-includes/media-template.php:970
msgid "Link To"
msgstr "Link a"

#. translators: 1: blog name, 2: separator(raquo), 3: post type name
#: wp-includes/general-template.php:2702
msgid "%1$s %2$s %3$s Feed"
msgstr "%1$s %2$s %3$s Feed"

#: wp-includes/class-wp-xmlrpc-server.php:268
msgid "XML-RPC services are disabled on this site."
msgstr "Il servizio XML-RPC è disattivato su questo sito."

#: wp-includes/class-wp-xmlrpc-server.php:1644
msgid "There is a revision of this post that is more recent."
msgstr "C'è una revisione di questo articolo che è più recente."

#: wp-includes/script-loader.php:863 wp-includes/media-template.php:601
msgid "Clear"
msgstr "Pulisci"

#: wp-includes/media-template.php:489
msgid "Describe this video&hellip;"
msgstr "Descrivi questo video&hellip;"

#: wp-includes/media-template.php:491
msgid "Describe this audio file&hellip;"
msgstr "Descrivi questo file audio&hellip;"

#: wp-includes/media-template.php:493
msgid "Describe this media file&hellip;"
msgstr "Descrivi questo file multimediale&hellip;"

#: wp-includes/media-template.php:663 wp-includes/media-template.php:731
#: wp-includes/media-template.php:974
msgid "Media File"
msgstr "File media"

#: wp-includes/class-wp-image-editor-imagick.php:225
#: wp-includes/class-wp-image-editor-gd.php:104
msgid "Could not read image size."
msgstr "Impossibile leggere le dimensioni dell'immagine."

#: wp-includes/media-template.php:1236 wp-includes/media.php:3523
msgid "No items found."
msgstr "Nessun elemento trovato"

#: wp-includes/media-template.php:478
msgid "Deselect"
msgstr "Deseleziona"

#: wp-includes/class-wp-xmlrpc-server.php:4658
#: wp-includes/class-wp-xmlrpc-server.php:4671
msgid "Sorry, that file cannot be edited."
msgstr "Questo file non può essere modificato."

#: wp-includes/media-template.php:196
msgid "Upload Limit Exceeded"
msgstr "Limite di caricamento superato"

#: wp-includes/media-template.php:264
msgid "Dismiss Errors"
msgstr "Nascondi l'errore"

#: wp-includes/media.php:2924
msgid "No editor could be selected."
msgstr "Impossibile selezionare un editor"

#: wp-includes/media-template.php:263
msgid "Uploading"
msgstr "Caricamento"

#: wp-includes/media-template.php:675 wp-includes/media-template.php:872
#: wp-includes/media-template.php:985
msgid "Custom URL"
msgstr "URL personalizzata"

#: wp-includes/media-template.php:501 wp-includes/media.php:3546
msgid "Attachment Details"
msgstr "Dettagli allegato"

#: wp-includes/media-template.php:740
msgid "Columns"
msgstr "Colonne"

#: wp-includes/media-template.php:696 wp-includes/media-template.php:771
#: wp-includes/media-template.php:945 wp-includes/media.php:3181
msgid "Large"
msgstr "Grande"

#: wp-includes/media-template.php:608
msgid "Attachment Display Settings"
msgstr "Impostazioni visualizzazione allegati"

#: wp-includes/media-template.php:715
msgid "Gallery Settings"
msgstr "Impostazioni galleria"

#: wp-includes/media-template.php:754
msgid "Random Order"
msgstr "Ordine casuale"

#: wp-includes/class-wp-xmlrpc-server.php:2505
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:350
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:619
#: wp-includes/user.php:1451 wp-includes/user.php:1804
#: wp-includes/user.php:1810
msgid "Invalid user ID."
msgstr "ID utente non valido."

#: wp-includes/customize/class-wp-customize-nav-menu-auto-add-control.php:46
msgid "Automatically add new top-level pages to this menu"
msgstr "Aggiungi automaticamente le nuove pagine di primo livello a questo menu"

#: wp-login.php:605 wp-login.php:675 wp-login.php:734 wp-login.php:829
#: wp-includes/general-template.php:260
msgid "Log in"
msgstr "Accedi"

#: wp-includes/general-template.php:213
msgctxt "placeholder"
msgid "Search &hellip;"
msgstr "Cerca &hellip;"

#: wp-includes/general-template.php:212 wp-includes/general-template.php:220
msgctxt "label"
msgid "Search for:"
msgstr "Ricerca per:"

#: wp-includes/general-template.php:215 wp-includes/general-template.php:222
msgctxt "submit button"
msgid "Search"
msgstr "Cerca"

#: wp-includes/script-loader.php:569 wp-includes/functions.php:5383
msgid "Session expired"
msgstr "Sessione scaduta"

#: wp-includes/class-wp-xmlrpc-server.php:483
msgid "WordPress Address (URL)"
msgstr "Indirizzo WordPress (URL)"

#: wp-includes/class-wp-xmlrpc-server.php:488
msgid "Site Address (URL)"
msgstr "Indirizzo sito (URL)"

#: wp-includes/functions.php:5385
msgid "The login page will open in a new window. After logging in you can close it and return to this page."
msgstr "La pagina di login sar&agrave; aperta in una nuova finestra. Dopo esserti loggato potrai chiuderla e ritornare a questa pagina."

#: wp-includes/user.php:285 wp-includes/functions.php:5384
msgid "Please log in again."
msgstr "Accedi nuovamente"

#: wp-login.php:1012 wp-login.php:1044 wp-includes/general-template.php:399
msgid "Log In"
msgstr "Login"

#: wp-includes/media-template.php:355 wp-includes/media-template.php:533
msgid "Length:"
msgstr "Lunghezza:"

#: wp-includes/media-template.php:638
msgid "Embed or Link"
msgstr "Incorpora o crea un link"

#: wp-includes/media-template.php:661
msgid "Link to Media File"
msgstr "Link al file multimediale"

#: wp-includes/media-template.php:668
msgid "Link to Attachment Page"
msgstr "Link alla pagina dell'allegato"

#. translators: Time difference between two dates, in weeks. 1: Number of weeks
#: wp-includes/formatting.php:3264
msgid "%s week"
msgid_plural "%s weeks"
msgstr[0] "%s settimana"
msgstr[1] "%s settimane"

#. translators: Time difference between two dates, in months. 1: Number of
#. months
#: wp-includes/formatting.php:3270
msgid "%s month"
msgid_plural "%s months"
msgstr[0] "%s mese"
msgstr[1] "%s mesi"

#. translators: Time difference between two dates, in years. 1: Number of years
#: wp-includes/formatting.php:3276
msgid "%s year"
msgid_plural "%s years"
msgstr[0] "%s anno"
msgstr[1] "%s anni"

#. translators: localized date format, see https://secure.php.net/date
#: wp-includes/media.php:3112 wp-includes/class-wp-locale.php:388
msgid "F j, Y"
msgstr "j F Y"

#. translators: localized time format, see https://secure.php.net/date
#: wp-includes/class-wp-locale.php:390
msgid "g:i a"
msgstr "G:i"

#. translators: localized date and time format, see https://secure.php.net/date
#: wp-includes/class-wp-locale.php:392
msgid "F j, Y g:i a"
msgstr "j F Y G:i"

#: wp-includes/class-wp-xmlrpc-server.php:493
msgid "Login Address (URL)"
msgstr "Indirizzo di login (URL)"

#: wp-includes/class-wp-xmlrpc-server.php:498
msgid "The URL to the admin area"
msgstr "L'URL dell'area di amministrazione"

#: wp-includes/class-wp-xmlrpc-server.php:2887
msgid "Failed to delete the page."
msgstr "Impossibile eliminare la pagina."

#. translators: This is a comma-separated list of very common words that should
#. be excluded from a search, * like a, an, and the. These are usually called
#. "stopwords". You should not simply translate these individual * words into
#. your language. Instead, look for and provide commonly accepted stopwords in
#. your language.
#: wp-includes/class-wp-query.php:1373
msgctxt "Comma-separated list of search stopwords in your language"
msgid "about,an,are,as,at,be,by,com,for,from,how,in,is,it,of,on,or,that,the,this,to,was,what,when,where,who,will,with,www"
msgstr "su,un,uno,una,sono,siete,come,alle,allo,alla,essere,di,da,per,in,con,per,tra,fra,come,è,egli,ella,essi,loro,il,al,le,lo,oppure,questo,quello,queste,questi,quelli,quelle,a,era,eri,erano,cosa,quando,dove,chi,www"

#: wp-includes/class-wp-theme.php:229
msgid "The theme directory \"%s\" does not exist."
msgstr "La directory del tema  \"%s\" non esiste."

#: wp-includes/link-template.php:1607
msgid "Use commas instead of %s to separate excluded terms."
msgstr "Usa le virgole invece di %s per separare i termini da escludere."

#: wp-includes/general-template.php:3912
msgctxt "admin color scheme"
msgid "Light"
msgstr "Light"

#: wp-includes/general-template.php:3924
msgctxt "admin color scheme"
msgid "Midnight"
msgstr "Mezzanotte"

#: wp-includes/class-wp-image-editor.php:274
msgid "Attempted to set image quality outside of the range [1,100]."
msgstr "Tentativo di impostare la qualità dell'immagine al di fuori dell'intervallo consentito [1,100]."

#: wp-includes/media-template.php:1084 wp-includes/media-template.php:1173
msgctxt "auto preload"
msgid "Auto"
msgstr "Automatico"

#. translators: used to join items in a list with more than 2 items
#: wp-includes/formatting.php:4506
msgid "%s, %s"
msgstr "%s, %s"

#. translators: used to join last two items in a list with more than 2 times
#: wp-includes/formatting.php:4508
msgid "%s, and %s"
msgstr "%s e %s"

#. translators: used to join items in a list with only 2 items
#: wp-includes/formatting.php:4510
msgid "%s and %s"
msgstr "%s e %s"

#: wp-includes/media-template.php:814
msgid "Show Images"
msgstr "Mostra le immagini"

#: wp-includes/media-template.php:1085 wp-includes/media-template.php:1174
msgid "Metadata"
msgstr "Metadati"

#: wp-includes/media-template.php:1092 wp-includes/media-template.php:1181
msgid "Autoplay"
msgstr "Autoplay"

#: wp-includes/widgets/class-wp-widget-media-video.php:79
#: wp-includes/media-template.php:1190
msgid "Tracks (subtitles, captions, descriptions, chapters, or metadata)"
msgstr "Tracce (sottotitoli, didascalia, capitoli o metadata)"

#: wp-includes/media-template.php:1072 wp-includes/media-template.php:1154
msgid "Add alternate sources for maximum HTML5 playback:"
msgstr "Aggiungi una sorgente per la massima riproduzione HTML5: "

#: wp-includes/media-template.php:1165
msgid "Poster Image"
msgstr "Immagine Poster"

#: wp-includes/media-template.php:1203
msgid "There are no associated subtitles."
msgstr "Non ci sono sottotitoli associati."

#: wp-includes/customize/class-wp-customize-header-image-control.php:210
msgctxt "custom headers"
msgid "Previously uploaded"
msgstr "Precedentemente caricata"

#: wp-includes/customize/class-wp-customize-header-image-control.php:217
msgctxt "custom headers"
msgid "Suggested"
msgstr "Suggerita"

#: wp-includes/customize/class-wp-customize-nav-menu-control.php:54
#: wp-includes/customize/class-wp-widget-area-customize-control.php:60
msgid "Reorder"
msgstr "Riordina"

#: wp-includes/media-template.php:892 wp-includes/media.php:3500
msgid "Replace"
msgstr "Sostituisci"

#: wp-includes/media-template.php:485
msgid "Caption this image&hellip;"
msgstr "Didascalia per questa immagine&hellip;"

#: wp-includes/customize/class-wp-customize-header-image-control.php:140
msgid "No image set"
msgstr "Nessuna immagine impostata"

#: wp-includes/media-template.php:891
msgid "Edit Original"
msgstr "Modifica originale"

#: wp-includes/customize/class-wp-customize-header-image-control.php:108
msgid "Set image"
msgstr "Imposta immagine"

#: wp-includes/media.php:2181
msgid "Genre"
msgstr "Genere"

#: wp-includes/media-template.php:388 wp-includes/media-template.php:569
#: wp-includes/media.php:2176
msgid "Artist"
msgstr "Artista"

#: wp-includes/media-template.php:389 wp-includes/media-template.php:570
#: wp-includes/media.php:2177
msgid "Album"
msgstr "Album"

#: wp-includes/media-template.php:959
msgid "Custom Size"
msgstr "Dimensioni personalizzate"

#: wp-includes/media.php:2182
#: wp-includes/customize/class-wp-customize-date-time-control.php:151
msgid "Year"
msgstr "Anno"

#: wp-includes/media.php:2183
msgctxt "video or audio"
msgid "Length"
msgstr "Durata"

#: wp-includes/widgets/class-wp-widget-media-image.php:113
#: wp-includes/media-template.php:1002
msgid "Image CSS Class"
msgstr "Classe CSS dell&#8217;immagine"

#: wp-includes/widgets/class-wp-widget-media-image.php:144
#: wp-includes/media-template.php:998
msgid "Image Title Attribute"
msgstr "Attributo title dell&#8217;immagine"

#: wp-includes/media-template.php:994
msgid "Advanced Options"
msgstr "Opzioni avanzate"

#: wp-includes/media-template.php:795
msgid "Show Video List"
msgstr "Mostra elenco video"

#: wp-includes/widgets/class-wp-widget-media-image.php:122
#: wp-includes/media-template.php:1015
msgid "Link CSS Class"
msgstr "Classe CSS del link"

#: wp-includes/media-template.php:912
msgid "Display Settings"
msgstr "Impostazioni di visualizzazione"

#: wp-includes/customize/class-wp-customize-media-control.php:244
msgid "Change image"
msgstr "Cambia immagine"

#: wp-includes/customize/class-wp-customize-header-image-control.php:97
msgid "Randomize uploaded headers"
msgstr "Rendi casuali le testate caricate"

#: wp-includes/customize/class-wp-customize-header-image-control.php:99
msgid "Randomize suggested headers"
msgstr "Rendi casuali le testate suggerite"

#: wp-includes/customize/class-wp-customize-header-image-control.php:205
msgid "Add new image"
msgstr "Aggiungi nuova immagine"

#: wp-includes/customize/class-wp-customize-header-image-control.php:204
msgid "Hide image"
msgstr "Nascondi immagine"

#. translators: 1: PHP class name, 2: version number, 3: __construct() method
#: wp-includes/functions.php:3954
msgid "The called constructor method for %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr "Il metodo di costruzione richiamato per %1$s è <strong>deprecato</strong> dalla versione %2$s! Utilizzare invece %3$s."

#. translators: %s: Name of the directory (build)
#: wp-includes/class-wp-locale.php:244
msgid "The %s directory of the develop repository must be used for RTL."
msgstr "La directory %s del repository di sviluppo deve essere utilizzata per l'RTL."

#: wp-includes/customize/class-wp-customize-nav-menu-control.php:58
msgid "When in reorder mode, additional controls to reorder menu items will be available in the items list above."
msgstr "Quando si è in modalità di riordino. Saranno disponibili, nella lista degli elementi qua sopra, nuovi controlli per riordinare gli elementi del menu."

#. translators: 1: original menu name, 2: duplicate count
#: wp-includes/customize/class-wp-customize-nav-menu-setting.php:512
msgid "%1$s (%2$d)"
msgstr "%1$s (%2$d)"

#: wp-includes/customize/class-wp-customize-themes-panel.php:44
msgid "Previewing theme"
msgstr "Anteprima del tema"

#: wp-includes/customize/class-wp-customize-themes-panel.php:42
msgid "Active theme"
msgstr "Tema attivo"

#. translators: This is a comma-separated list of words that defy the syntax of
#. quotations in normal use, * for example...  'We do not have enough words
#. yet' ... is a typical quoted phrase.  But when we write * lines of code 'til
#. we have enough of 'em, then we need to insert apostrophes instead of quotes.
#: wp-includes/formatting.php:132
msgctxt "Comma-separated list of words to texturize in your language"
msgid "'tain't,'twere,'twas,'tis,'twill,'til,'bout,'nuff,'round,'cause,'em"
msgstr "da',di',fa',sta',va',be',po',mo',a',de',co'"

#: wp-includes/formatting.php:135
msgctxt "Comma-separated list of replacement words in your language"
msgid "&#8217;tain&#8217;t,&#8217;twere,&#8217;twas,&#8217;tis,&#8217;twill,&#8217;til,&#8217;bout,&#8217;nuff,&#8217;round,&#8217;cause,&#8217;em"
msgstr "da&#8217;,di&#8217;,fa&#8217;,sta&#8217;,va&#8217;,be&#8217;,po&#8217;,mo&#8217;,a&#8217;,de&#8217;,co&#8217;"

#: wp-includes/media-template.php:1167
msgid "Remove poster image"
msgstr "Elimina immagine poster"

#: wp-includes/media-template.php:1051 wp-includes/media-template.php:1065
msgid "Remove audio source"
msgstr "Rimuovi sorgente audio"

#: wp-includes/media-template.php:1199
msgctxt "media"
msgid "Remove video track"
msgstr "Rimuovi traccia video"

#. translators: 'rtl' or 'ltr'. This sets the text direction for WordPress.
#: wp-includes/class-wp-locale.php:228
msgctxt "text direction"
msgid "ltr"
msgstr "ltr"

#: wp-includes/customize/class-wp-customize-nav-menu-control.php:38
msgid "Add Items"
msgstr "Aggiungi voci"

#: wp-includes/media-template.php:1134 wp-includes/media-template.php:1146
msgid "Remove video source"
msgstr "Rimuovi sorgente video"

#: wp-includes/customize/class-wp-customize-new-menu-control.php:50
msgid "Create Menu"
msgstr "Crea menu"

#: wp-includes/customize/class-wp-customize-nav-menu-auto-add-control.php:42
#: wp-includes/customize/class-wp-customize-nav-menus-panel.php:85
msgid "Menu Options"
msgstr "Opzioni del menu"

#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:93
msgid "Navigation Label"
msgstr "Etichetta di navigazione"

#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:105
msgid "Title Attribute"
msgstr "Attributo titolo"

#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:111
msgid "CSS Classes"
msgstr "Classi CSS"

#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:117
msgid "Link Relationship (XFN)"
msgstr "Relazioni tra link (XFN)"

#. translators: Nav menu item original title. 1: Original title
#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:134
msgid "Original: %s"
msgstr "Originale: %s"

#. translators: 1: PHP function name, 2: version number
#: wp-includes/functions.php:4083
msgid "%1$s was called with an argument that is <strong>deprecated</strong> since version %2$s with no alternative available."
msgstr "%1$s è stato richiamato con un argomento che è <strong>deprecato</strong> dalla versione %2$s senza alcuna alternativa disponibile."

#: wp-includes/customize/class-wp-customize-themes-panel.php:49
msgctxt "theme"
msgid "Change"
msgstr "Cambia"

#. translators: 1: 'text_direction' argument, 2: bloginfo() function name, 3:
#. is_rtl() function name
#: wp-includes/general-template.php:713
msgid "The %1$s option is deprecated for the family of %2$s functions. Use the %3$s function instead."
msgstr "L'opzione %1$s è deprecata per la famiglia di funzioni di %2$s. Al suo posto utilizza la funzione %3$s."

#. translators: 1: 'siteurl'/'home' argument, 2: bloginfo() function name, 3:
#. 'url' argument
#: wp-includes/general-template.php:640
msgid "The %1$s option is deprecated for the family of %2$s functions. Use the %3$s option instead."
msgstr "L'opzione %1$s è deprecata per la famiglia di funzioni di %2$s. Al suo posto utilizza la funzione %3$s."

#. translators: %s: post title
#: wp-includes/comment-template.php:1500
msgid "No Comments<span class=\"screen-reader-text\"> on %s</span>"
msgstr "Nessun commento<span class=\"screen-reader-text\"> su %s</span>"

#. translators: %s: post title
#: wp-includes/comment-template.php:1516
msgid "Comments Off<span class=\"screen-reader-text\"> on %s</span>"
msgstr "Commenti disabilitati<span class=\"screen-reader-text\"> su %s</span>"

#. translators: %s: theme name
#: wp-includes/customize/class-wp-customize-theme-control.php:106
msgid "<span>Previewing:</span> %s"
msgstr "<span>Anteprima:</span> %s"

#: wp-includes/customize/class-wp-customize-theme-control.php:83
#: wp-includes/customize/class-wp-customize-themes-section.php:78
msgid "Theme Details"
msgstr "Dettagli del tema"

#: wp-includes/script-loader.php:840
#: wp-includes/customize/class-wp-customize-theme-control.php:118
msgid "Live Preview"
msgstr "Anteprima in tempo reale"

#: wp-includes/media-template.php:797
msgid "Show Tracklist"
msgstr "Mostra la scaletta delle tracce"

#: wp-includes/media-template.php:806
msgid "Show Artist Name in Tracklist"
msgstr "Mostra il nome dell'artista nella scaletta"

#: wp-includes/media-template.php:436 wp-includes/media-template.php:539
msgid "Untrash"
msgstr "Ripristina"

#: wp-includes/media-template.php:344
msgid "File type:"
msgstr "Tipo di file:"

#: wp-includes/media-template.php:345
msgid "Uploaded on:"
msgstr "Caricato il:"

#: wp-includes/media-template.php:350
msgid "Dimensions:"
msgstr "Dimensioni:"

#: wp-includes/media-template.php:360
msgid "Bitrate:"
msgstr "Bitrate:"

#: wp-includes/media-template.php:412
msgid "Uploaded By"
msgstr "Caricato da"

#: wp-includes/media-template.php:429
msgid "View attachment page"
msgstr "Vedi pagina allegato"

#: wp-includes/media-template.php:343
msgid "File name:"
msgstr "Nome file:"

#: wp-includes/media-template.php:417
msgid "Uploaded To"
msgstr "Caricato in"

#: wp-includes/media-template.php:284
msgid "Edit previous media item"
msgstr "Modifica l'elemento media precedente"

#: wp-includes/media-template.php:285
msgid "Edit next media item"
msgstr "Modifica l'elemento media successivo"

#: wp-includes/media-template.php:187
msgid "Close uploader"
msgstr "Chiudi l'uploader"

#: wp-includes/media-template.php:233
msgid "Maximum upload file size: %s."
msgstr "Dimensione massima di caricamento file: %s."

#: wp-includes/media.php:2185
msgid "Bitrate"
msgstr "Bitrate"

#: wp-includes/media-template.php:166
msgid "Close media panel"
msgstr "Chiudi il pannello media"

#: wp-includes/media-template.php:431
msgid "Edit more details"
msgstr "Modifica i dettagli aggiuntivi"

#: wp-includes/general-template.php:3901
msgctxt "admin color scheme"
msgid "Default"
msgstr "Predefinito"

#: wp-includes/widgets/class-wp-widget-media-image.php:31
#: wp-includes/customize/class-wp-customize-media-control.php:247
msgid "No image selected"
msgstr "Nessuna immagine selezionata"

#. translators: Date query invalid date message: 1: invalid value, 2: type of
#. value, 3: minimum valid value, 4: maximum valid value
#: wp-includes/date.php:403
msgid "Invalid value %1$s for %2$s. Expected value should be between %3$s and %4$s."
msgstr "Valore non valido %1$s per %2$s. Il valore previsto dovrebbe essere tra %3$s e %4$s."

#. translators: Comment reply button text. 1: Comment author name
#: wp-includes/comment-template.php:1599
msgid "Reply to %s"
msgstr "Rispondi a %s"

#: wp-includes/customize/class-wp-customize-media-control.php:257
msgid "No file selected"
msgstr "Nessun file selezionato"

#: wp-includes/l10n.php:1229
msgctxt "translations"
msgid "Available"
msgstr "Disponibile"

#. translators: Post type archive title. 1: Post type name
#: wp-includes/general-template.php:1506
msgid "Archives: %s"
msgstr "Archivi: %s"

#. translators: Taxonomy term archive title. 1: Taxonomy singular name, 2:
#. Current taxonomy term
#: wp-includes/general-template.php:1510
msgid "%1$s: %2$s"
msgstr "%1$s: %2$s"

#: wp-includes/general-template.php:1500
msgctxt "post format archive title"
msgid "Audio"
msgstr "Audio"

#. translators: Daily archive title. 1: Date
#: wp-includes/general-template.php:1483
msgid "Day: %s"
msgstr "Giorno: %s"

#. translators: Yearly archive title. 1: Year
#: wp-includes/general-template.php:1477
msgid "Year: %s"
msgstr "Anno: %s"

#. translators: Author archive title. 1: Author name
#: wp-includes/general-template.php:1474
msgid "Author: %s"
msgstr "Autore: %s"

#. translators: Tag archive title. 1: Tag name
#: wp-includes/general-template.php:1471
msgid "Tag: %s"
msgstr "Tag: %s"

#. translators: Category archive title. 1: Category name
#: wp-includes/general-template.php:1468
msgid "Category: %s"
msgstr "Categoria: %s"

#. translators: Monthly archive title. 1: Month name and year
#: wp-includes/general-template.php:1480
msgid "Month: %s"
msgstr "Mese: %s"

#: wp-includes/general-template.php:1019
msgid "Page %s"
msgstr "Pagina %s"

#: wp-includes/link-template.php:2483
msgid "Newer posts"
msgstr "Articoli seguenti"

#: wp-includes/link-template.php:2484 wp-includes/link-template.php:2538
#: wp-includes/link-template.php:2582
msgid "Posts navigation"
msgstr "Navigazione articoli"

#. translators: Daily archive title. 1: Date
#: wp-includes/general-template.php:1483 wp-includes/general-template.php:2100
msgctxt "daily archives date format"
msgid "F j, Y"
msgstr "j F Y"

#: wp-includes/general-template.php:1488
msgctxt "post format archive title"
msgid "Galleries"
msgstr "Gallerie"

#: wp-includes/general-template.php:1490
msgctxt "post format archive title"
msgid "Images"
msgstr "Immagini"

#: wp-includes/l10n.php:1195
msgctxt "translations"
msgid "Installed"
msgstr "Installata"

#: wp-includes/media-template.php:598
msgid "Edit Selection"
msgstr "Modifica la selezione"

#: wp-includes/general-template.php:1496
msgctxt "post format archive title"
msgid "Links"
msgstr "Link"

#: wp-includes/customize/class-wp-customize-media-control.php:259
msgid "Choose file"
msgstr "Scegli un file"

#: wp-includes/customize/class-wp-customize-media-control.php:243
#: wp-includes/customize/class-wp-customize-media-control.php:248
msgid "Select image"
msgstr "Seleziona una immagine"

#: wp-includes/customize/class-wp-customize-media-control.php:254
msgid "Change file"
msgstr "Cambia file"

#: wp-includes/link-template.php:2416
msgid "Post navigation"
msgstr "Navigazione articoli"

#: wp-includes/general-template.php:1502
msgctxt "post format archive title"
msgid "Chats"
msgstr "Chat"

#: wp-includes/general-template.php:1498
msgctxt "post format archive title"
msgid "Statuses"
msgstr "Stati"

#: wp-includes/general-template.php:1492
msgctxt "post format archive title"
msgid "Videos"
msgstr "Video"

#: wp-includes/general-template.php:1494
msgctxt "post format archive title"
msgid "Quotes"
msgstr "Citazioni"

#: wp-includes/general-template.php:1486
msgctxt "post format archive title"
msgid "Asides"
msgstr "Digressioni"

#: wp-includes/date.php:433
msgid "The following values do not describe a valid date: year %1$s, month %2$s, day %3$s."
msgstr "I seguenti valori non descrivono una data valida: anno %1$s, mese %2$s, giorno %3$s."

#: wp-includes/date.php:450
msgid "The following values do not describe a valid date: month %1$s, day %2$s."
msgstr "I seguenti valori non descrivono una data valida: giorno %2$s, mese %1$s."

#: wp-includes/media-template.php:1248
msgid "As a browser icon"
msgstr "Come icona del browser"

#: wp-includes/media-template.php:1253
#: wp-includes/customize/class-wp-customize-site-icon-control.php:67
msgid "Preview as a browser icon"
msgstr "Anteprima come icona del browser"

#: wp-includes/media-template.php:1260
#: wp-includes/customize/class-wp-customize-site-icon-control.php:71
msgid "Preview as an app icon"
msgstr "Anteprima come icona app"

#. translators: 1: Title of a menu item, 2: Type of a menu item
#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:72
msgid "Edit menu item: %1$s (%2$s)"
msgstr "Modifica elemento del menu: %1$s (%2$s)"

#: wp-includes/script-loader.php:1100 wp-includes/formatting.php:3374
msgctxt "Word count type. Do not translate!"
msgid "words"
msgstr "words"

#: wp-includes/customize/class-wp-customize-header-image-control.php:196
msgid "Current header"
msgstr "Intestazione attuale"

#: wp-includes/media-template.php:1258
msgid "As an app icon"
msgstr "Come icona per la app"

#. translators: playlist item title
#: wp-includes/media.php:1832 wp-includes/media.php:1847
msgctxt "playlist item title"
msgid "&#8220;%s&#8221;"
msgstr "&#8220;%s&#8221;"

#. translators: one-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:144
msgctxt "Saturday initial"
msgid "S"
msgstr "S"

#. translators: one-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:143
msgctxt "Friday initial"
msgid "F"
msgstr "V"

#. translators: one-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:142
msgctxt "Thursday initial"
msgid "T"
msgstr "G"

#. translators: one-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:141
msgctxt "Wednesday initial"
msgid "W"
msgstr "M"

#. translators: one-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:140
msgctxt "Tuesday initial"
msgid "T"
msgstr "M"

#. translators: one-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:139
msgctxt "Monday initial"
msgid "M"
msgstr "L"

#. translators: one-letter abbreviation of the weekday
#: wp-includes/class-wp-locale.php:138
msgctxt "Sunday initial"
msgid "S"
msgstr "D"

#. translators: Post calendar label. 1: Date
#: wp-includes/general-template.php:2102
msgid "Posts published on %s"
msgstr "Articoli pubblicati in %s"

#: wp-includes/link-template.php:2825 wp-includes/link-template.php:2873
msgid "Comments navigation"
msgstr "Navigazione commenti"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:195
msgctxt "December abbreviation"
msgid "Dec"
msgstr "Dic"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:194
msgctxt "November abbreviation"
msgid "Nov"
msgstr "Nov"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:193
msgctxt "October abbreviation"
msgid "Oct"
msgstr "Ott"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:192
msgctxt "September abbreviation"
msgid "Sep"
msgstr "Set"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:191
msgctxt "August abbreviation"
msgid "Aug"
msgstr "Ago"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:190
msgctxt "July abbreviation"
msgid "Jul"
msgstr "Lug"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:189
msgctxt "June abbreviation"
msgid "Jun"
msgstr "Giu"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:187
msgctxt "April abbreviation"
msgid "Apr"
msgstr "Apr"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:188
msgctxt "May abbreviation"
msgid "May"
msgstr "Mag"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:186
msgctxt "March abbreviation"
msgid "Mar"
msgstr "Mar"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:185
msgctxt "February abbreviation"
msgid "Feb"
msgstr "Feb"

#. translators: three-letter abbreviation of the month
#: wp-includes/class-wp-locale.php:184
msgctxt "January abbreviation"
msgid "Jan"
msgstr "Gen"

#. translators: %s: Name of current post
#: wp-includes/embed.php:803 wp-includes/post-template.php:270
msgid "Continue reading %s"
msgstr "Leggi tutto %s"

#: wp-includes/embed.php:957
msgid "%s <span class=\"screen-reader-text\">Comment</span>"
msgid_plural "%s <span class=\"screen-reader-text\">Comments</span>"
msgstr[0] "%s <span class=\"screen-reader-text\">Commento</span>"
msgstr[1] "%s <span class=\"screen-reader-text\">Commenti</span>"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:175
msgctxt "genitive"
msgid "June"
msgstr "Giugno"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:176
msgctxt "genitive"
msgid "July"
msgstr "Luglio"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:177
msgctxt "genitive"
msgid "August"
msgstr "Agosto"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:178
msgctxt "genitive"
msgid "September"
msgstr "Settembre"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:179
msgctxt "genitive"
msgid "October"
msgstr "Ottobre"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:180
msgctxt "genitive"
msgid "November"
msgstr "Novembre"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:181
msgctxt "genitive"
msgid "December"
msgstr "Dicembre"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:170
msgctxt "genitive"
msgid "January"
msgstr "Gennaio"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:171
msgctxt "genitive"
msgid "February"
msgstr "Febbraio"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:172
msgctxt "genitive"
msgid "March"
msgstr "Marzo"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:173
msgctxt "genitive"
msgid "April"
msgstr "Aprile"

#. translators: month name, genitive
#: wp-includes/class-wp-locale.php:174
msgctxt "genitive"
msgid "May"
msgstr "Maggio"

#: wp-includes/embed.php:998
msgid "Sharing options"
msgstr "Opzioni di condivisione"

#: wp-includes/class-wp-xmlrpc-server.php:5900
msgid "Sorry, you have used your space allocation."
msgstr "Hai utilizzato tutto lo spazio a tua disposizione."

#: wp-includes/embed.php:1013
msgid "Copy and paste this URL into your WordPress site to embed"
msgstr "Copia e incolla questo URL nel tuo sito WordPress per farne l'embed"

#: wp-includes/embed.php:1020
msgid "Copy and paste this code into your site to embed"
msgstr "Copia e incolla questo codice nel tuo sito per farne l'embed"

#. translators: %s: 'document_title_separator' filter name
#: wp-includes/feed.php:103 wp-includes/feed.php:129
msgid "Use the %s filter instead."
msgstr "Utilizza il filtro %s in alternativa."

#: wp-includes/embed.php:981
msgid "Open sharing dialog"
msgstr "Apri la maschera di condivisione"

#: wp-includes/class-wp-xmlrpc-server.php:595
msgid "Medium-Large size image height"
msgstr "Altezza immagine media-larga"

#: wp-includes/embed.php:1006
msgid "HTML Embed"
msgstr "Embed HTML"

#. translators: %s: WP_User->ID
#: wp-includes/class-wp-user.php:264 wp-includes/class-wp-user.php:293
#: wp-includes/class-wp-user.php:331 wp-includes/class-wp-user.php:354
msgid "Use %s instead."
msgstr "Utilizza invece %s."

#: wp-includes/embed.php:1025
msgid "Close sharing dialog"
msgstr "Chiudi la finestra di condivisione"

#: wp-includes/media-template.php:258
msgid "Grid View"
msgstr "Visualizzazione a griglia"

#: wp-includes/class-wp-xmlrpc-server.php:279
msgid "Incorrect username or password."
msgstr "Nome utente o password non corrette."

#: wp-includes/media-template.php:194
msgid "The web browser on your device cannot be used to upload files. You may be able to use the <a href=\"%s\">native app for your device</a> instead."
msgstr "Il browser web del tuo dispositivo non può essere utilizzato per caricare file. Dovresti poter utilizzare al suo posto <a href=\"%s\">la app nativa per lil tuo dispositivo</a>."

#: wp-includes/class-wp-theme.php:235
msgid "ERROR: The themes directory is either empty or doesn&#8217;t exist. Please check your installation."
msgstr "ERRORE: La directory dei temi è vuota o non esiste. Verifica la tua installazione."

#: wp-includes/customize/class-wp-customize-header-image-control.php:128
msgid "Randomizing suggested headers"
msgstr "Rendi casuali le testate suggerite"

#: wp-includes/customize/class-wp-customize-header-image-control.php:126
msgid "Randomizing uploaded headers"
msgstr "Ordinamento casuale delle testate caricate"

#: wp-includes/class-wp-xmlrpc-server.php:615
msgid "Allow link notifications from other blogs (pingbacks and trackbacks) on new articles"
msgstr "Permetti le notifiche dei link da altri blog (pingback e trackback) sui nuovi articoli"

#: wp-includes/link-template.php:2482
msgid "Older posts"
msgstr "Articoli meno recenti"

#: wp-includes/customize/class-wp-customize-nav-menu-control.php:50
msgid "Add or remove menu items"
msgstr "Aggiungere o rimuovere voci dal menu"

#: wp-includes/customize/class-wp-widget-area-customize-control.php:63
msgid "When in reorder mode, additional controls to reorder widgets will be available in the widgets list above."
msgstr "Quando si è in modalità di ordinamento nella lista dei widget sottostanti sono disponibili controlli supplementari per riordinare i widget."

#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:125
msgid "The description will be displayed in the menu if the current theme supports it."
msgstr "La descrizione sarà visualizzata nel menu se il tema supporta questa funzionalità."

#. translators: 1: $table_prefix 2: wp-config.php
#: wp-includes/load.php:438
msgid "<strong>ERROR</strong>: %1$s in %2$s can only contain numbers, letters, and underscores."
msgstr "<strong>ERRORE</strong>: %1$s in %2$s può contenere solo numeri, lettere e trattini bassi."

#: wp-includes/functions.php:1230
msgid "ERROR: This is not a valid feed template."
msgstr "ERRORE: questo non è un modello valido per i feed."

#: wp-includes/class-wp-xmlrpc-server.php:1960
#: wp-includes/class-wp-xmlrpc-server.php:2063
msgid "The term name cannot be empty."
msgstr "Il nome del termine non può essere vuoto."

#. translators: %s: Codex URL
#: wp-includes/functions.php:4192
msgid "Please see <a href=\"%s\">Debugging in WordPress</a> for more information."
msgstr "Leggi <a href=\"%s\">Debugging in WordPress</a> per maggiori informazioni."

#: wp-includes/functions.php:4193
msgid "https://codex.wordpress.org/Debugging_in_WordPress"
msgstr "https://codex.wordpress.org/Debugging_in_WordPress"

#. translators: %s: menu name
#: wp-includes/customize/class-wp-customize-nav-menu-locations-control.php:77
msgctxt "menu location"
msgid "(Current: %s)"
msgstr "(Attuale: %s)"

#: wp-includes/class-wp-xmlrpc-server.php:1539
msgid "Ambiguous term name used in a hierarchical taxonomy. Please use term ID instead."
msgstr "Termine ambiguo utilizzato in una tassonomia gerarchica. Utilizza la ID dei termine."

#: wp-includes/comment.php:3134
msgid "<strong>ERROR</strong>: please type a comment."
msgstr "<strong>ERRORE</strong>: inserisci un commento."

#: wp-includes/comment.php:3129
msgid "<strong>ERROR</strong>: please enter a valid email address."
msgstr "<strong>ERRORE</strong>: inserisci un indirizzo email valido."

#: wp-includes/comment.php:3127
msgid "<strong>ERROR</strong>: please fill the required fields (name, email)."
msgstr "<strong>ERRORE</strong>: inserisci i campi obbligatori (nome, email)."

#: wp-includes/formatting.php:4256
msgid "The timezone you have entered is not valid. Please select a valid timezone."
msgstr "Il fuso orario inserito non è valido. Seleziona un fuso orario valido."

#: wp-includes/formatting.php:4082
msgid "The email address entered did not appear to be a valid email address. Please enter a valid email address."
msgstr "L'indirizzo email inserito non sembra essere un indirizzo email valido. Inserisci un indirizzo email valido."

#: wp-includes/formatting.php:4188
msgid "The WordPress address you entered did not appear to be a valid URL. Please enter a valid URL."
msgstr "L'indirizzo di WordPress inserito non sembra essere una URL valida. Inserisci una URL valida."

#: wp-includes/formatting.php:4201
msgid "The Site address you entered did not appear to be a valid URL. Please enter a valid URL."
msgstr "L'indirizzo del sito immesso non sembra essere una URL valida. Inserisci una URL valida."

#: wp-includes/comment-template.php:1873 wp-includes/comment-template.php:2256
msgid "Leave a Reply"
msgstr "Lascia un commento"

#: wp-includes/comment-template.php:2248
msgid "Your email address will not be published."
msgstr "Il tuo indirizzo email non sarà pubblicato."

#. translators: 1: Title of a menu item, 2: Type of a menu item
#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:76
msgid "Remove Menu Item: %1$s (%2$s)"
msgstr "Rimuovi elemento del menu: %1$s (%2$s)"

#: wp-includes/class-wp-xmlrpc-server.php:590
msgid "Medium-Large size image width"
msgstr "Larghezza immagine medio-larga"

#: wp-includes/customize/class-wp-customize-header-image-control.php:204
msgid "Hide header image"
msgstr "Nascondi l'immagine dell'intestazione"

#: wp-includes/media-template.php:785
msgid "Playlist Settings"
msgstr "Impostazioni playlist"

#: wp-includes/general-template.php:3930
msgctxt "admin color scheme"
msgid "Sunrise"
msgstr "Alba"

#: wp-includes/general-template.php:3936
msgctxt "admin color scheme"
msgid "Ectoplasm"
msgstr "Ectoplasma"

#: wp-includes/general-template.php:3942
msgctxt "admin color scheme"
msgid "Ocean"
msgstr "Oceano"

#: wp-includes/general-template.php:3948
msgctxt "admin color scheme"
msgid "Coffee"
msgstr "Caffè"

#: wp-includes/media-template.php:651
msgid "Embed Media Player"
msgstr "Incorpora il media player"

#: wp-includes/media-template.php:403 wp-includes/media-template.php:584
#: wp-includes/media-template.php:843
msgid "Alt Text"
msgstr "Testo alternativo"

#: wp-includes/general-template.php:972 wp-includes/general-template.php:1187
msgid "Page not found"
msgstr "Pagina non trovata"

#. translators: 1: script name, 2: wp_enqueue_scripts
#: wp-includes/functions.wp-scripts.php:230
msgid "Do not deregister the %1$s script in the administration area. To target the front-end theme, use the %2$s hook."
msgstr "Non de-registrare lo script %1$s nell'area amministrativa. Per farlo per il tema utilizzato ricorri all'hook %2$s."

#: wp-includes/deprecated.php:3130
msgid "Are you sure you want to do this?"
msgstr "Sei sicuro di voler fare questo?"

#: wp-includes/functions.php:5368
msgid "Close dialog"
msgstr "Chiudi la finestra di dialogo"

#. translators: %s: document.write()
#: wp-includes/customize/class-wp-customize-selective-refresh.php:198
msgid "%s is forbidden"
msgstr "%s è proibito"

#: wp-includes/comment.php:243
msgctxt "comment status"
msgid "Spam"
msgstr "Spam"

#. translators: Translate this to the correct language tag for your locale, *
#. see https://www.w3.org/International/articles/language-tags/ for reference.
#. * Do not translate into your own language.
#: wp-includes/general-template.php:704
msgid "html_lang_attribute"
msgstr "it-IT"

#: wp-includes/class-wp-metadata-lazyloader.php:77
#: wp-includes/class-wp-metadata-lazyloader.php:117
msgid "Invalid object type"
msgstr "Tipo oggetto non valido"

#: wp-includes/class-wp-theme.php:269
msgid "https://codex.wordpress.org/Child_Themes"
msgstr "https://codex.wordpress.org/Child_Themes"

#: wp-includes/comment.php:242
msgctxt "comment status"
msgid "Approved"
msgstr "Approvato"

#: wp-includes/comment.php:1160
msgid "<strong>ERROR</strong>: your name is too long."
msgstr "<strong>ERRORE</strong>: il nome è troppo lungo."

#: wp-includes/comment.php:1164
msgid "<strong>ERROR</strong>: your email address is too long."
msgstr "<strong>ERRORE</strong>: l'indirizzo email è troppo lungo."

#. translators: %s: user name
#: wp-includes/comment-template.php:2244
msgid "Logged in as %s. Edit your profile."
msgstr "Autenticato come %s. Modifica il tuo profilo."

#. translators: 1: edit user link, 2: accessibility text, 3: user name, 4:
#. logout URL
#: wp-includes/comment-template.php:2241
msgid "<a href=\"%1$s\" aria-label=\"%2$s\">Logged in as %3$s</a>. <a href=\"%4$s\">Log out?</a>"
msgstr "<a href=\"%1$s\" aria-label=\"%2$s\">Autenticato come %3$s</a>. <a href=\"%4$s\">Uscire?</a>"

#: wp-includes/comment.php:1168
msgid "<strong>ERROR</strong>: your url is too long."
msgstr "<strong>ERRORE</strong>: l'indirizzo è troppo lungo."

#: wp-includes/comment.php:1172
msgid "<strong>ERROR</strong>: your comment is too long."
msgstr "<strong>ERRORE</strong>: il commento è troppo lungo."

#. translators: 1: PHP class name, 2: PHP parent class name, 3: version number,
#. 4: __construct() method
#: wp-includes/functions.php:3950
msgid "The called constructor method for %1$s in %2$s is <strong>deprecated</strong> since version %3$s! Use %4$s instead."
msgstr "Il metodo del costruttore richiamato per %1$s in %2$s è <strong>deprecato</strong> dalla versione %3$s! Utilizzare invece %4$s."

#. translators: 1: blog name, 2: separator(raquo), 3: term name, 4: taxonomy
#. singular name
#: wp-includes/general-template.php:2696
msgid "%1$s %2$s %3$s %4$s Feed"
msgstr "Feed %1$s %2$s %3$s %4$s"

#: wp-includes/customize/class-wp-customize-partial.php:207
msgid "Partial render must echo the content or return the content string (or array), but not both."
msgstr "La visualizzazione parziale deve mostrare il contenuto o restituire la stringa del contenuto (o un array) ma non può fare entrambe le cose."

#. translators: 1: post title, 2: site name
#: wp-includes/embed.php:467
msgid "&#8220;%1$s&#8221; &#8212; %2$s"
msgstr "&#8220;%1$s&#8221; &#8212; %2$s"

#. translators: 1: <script>, 2: wp_add_inline_script()
#. translators: 1: <style>, 2: wp_add_inline_style()
#: wp-includes/functions.wp-scripts.php:113
#: wp-includes/functions.wp-styles.php:89
msgid "Do not pass %1$s tags to %2$s."
msgstr "Non passare i %1$s tag a %2$s."

#. translators: %s: ImageMagick method name
#: wp-includes/class-wp-image-editor-imagick.php:690
#: wp-includes/class-wp-image-editor-imagick.php:695
msgid "%s is required to strip image meta."
msgstr "È richiesto %s per poter eliminare i meta dell'immagine."

#: wp-includes/class-wp-xmlrpc-server.php:2711
msgid "Sorry, the user cannot be updated."
msgstr "L'utente non può essere modificato."

#: wp-includes/class-wp-xmlrpc-server.php:4321
#: wp-includes/class-wp-xmlrpc-server.php:4391
msgid "Sorry, revisions are disabled."
msgstr "Le revisioni sono disabilitate."

#. translators: %s: post title
#: wp-includes/comment-template.php:1505
msgid "1 Comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr "1 commento<span class=\"screen-reader-text\"> su %s</span>"

#. translators: 1: Number of comments 2: post title
#: wp-includes/comment-template.php:1510
msgid "%1$s Comment<span class=\"screen-reader-text\"> on %2$s</span>"
msgid_plural "%1$s Comments<span class=\"screen-reader-text\"> on %2$s</span>"
msgstr[0] "%1$s commento<span class=\"screen-reader-text\"> su %2$s</span>"
msgstr[1] "%1$s commenti<span class=\"screen-reader-text\"> su %2$s</span>"

#. translators: 1: index.php, 2: Codex URL, 3: style.css
#: wp-includes/class-wp-theme.php:267
msgid "Template is missing. Standalone themes need to have a %1$s template file. <a href=\"%2$s\">Child themes</a> need to have a Template header in the %3$s stylesheet."
msgstr "Modello mancante. I temi devono avere un file di modello %1$s . <a href=\"%2$s\">I temi Child</a> devono avere una intestazione di modello nel foglio di stile %3$s."

#: wp-includes/class-wp-xmlrpc-server.php:518
msgid "Template"
msgstr "Modello"

#. translators: %s: number of comments
#: wp-includes/comment-template.php:894 wp-includes/comment-template.php:908
msgid "%s Comment"
msgid_plural "%s Comments"
msgstr[0] "%s commento"
msgstr[1] "%s commenti"

#: wp-includes/embed.php:1003
msgid "WordPress Embed"
msgstr "Embed di WordPress"

#: wp-includes/media.php:2186
msgid "Bitrate Mode"
msgstr "Modalità bitrate"

#: wp-includes/class-wp-image-editor-gd.php:296
msgid "Image crop failed."
msgstr "Ritaglio immagine non riuscito."

#: wp-includes/class-wp-image-editor-gd.php:322
msgid "Image rotate failed."
msgstr "Rotazione immagine non riuscita."

#: wp-includes/class-wp-image-editor-gd.php:388
#: wp-includes/class-wp-image-editor-gd.php:396
#: wp-includes/class-wp-image-editor-gd.php:400
#: wp-includes/class-wp-image-editor-gd.php:403
msgid "Image Editor Save Failed"
msgstr "Salvataggio immagine non riuscito"

#: wp-includes/class-wp-image-editor-gd.php:165
#: wp-includes/class-wp-image-editor-gd.php:190
msgid "Image resize failed."
msgstr "Ridimensionamento immagine non riuscito."

#: wp-includes/class-wp-xmlrpc-server.php:6384
msgid "The pingback has already been registered."
msgstr "Il pingback è già stato registrato."

#: wp-includes/class-wp-xmlrpc-server.php:6537
msgid "The specified target URL does not exist."
msgstr "L’URL di destinazione specifica non esiste"

#: wp-includes/load.php:141
msgid "Your PHP installation appears to be missing the MySQL extension which is required by WordPress."
msgstr "L’installazione di PHP non ha l’estensione MySQL necessaria per utilizzare WordPress."

#: wp-includes/comment-template.php:1781
msgid "Click here to cancel reply."
msgstr "Fai clic qui per annullare la risposta."

#. translators: %s: file name
#: wp-includes/deprecated.php:3195
msgid "File &#8220;%s&#8221; is not an image."
msgstr "Il file &#8220;%s&#8221; non è un’immagine."

#: wp-includes/customize/class-wp-customize-themes-section.php:84
msgid "An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href=\"https://wordpress.org/support/\">support forums</a>."
msgstr "Si è verificato un errore inaspettato. Deve esserci un errore con WordPress.org o con la configurazione di questo server. Se continui ad avere problemi prova a consultare il <a href=\"https://it.wordpress.org/forums/\">forum di supporto</a>."

#: wp-includes/class-wp-term.php:160
msgid "Term ID is shared between multiple taxonomies"
msgstr "L'ID del termine è condiviso tra diverse tassonomie"

#: wp-includes/class-wp-image-editor-gd.php:351
msgid "Image flip failed."
msgstr "Il ribaltamento dell'immagine non è riuscito."

#. translators: %s: login URL
#: wp-includes/comment-template.php:2235
msgid "You must be <a href=\"%s\">logged in</a> to post a comment."
msgstr "Devi essere <a href=\"%s\">connesso</a> per inviare un commento."

#: wp-includes/class-wp-xmlrpc-server.php:2094
msgid "Sorry, editing the term failed."
msgstr "La modifica del termine è fallita."

#: wp-includes/class-wp-xmlrpc-server.php:2161
msgid "Sorry, deleting the term failed."
msgstr "L'eliminazione del termine è fallita."

#: wp-includes/class-wp-xmlrpc-server.php:550
msgid "Date Format"
msgstr "Formato data"

#: wp-includes/widgets/class-wp-nav-menu-widget.php:153
#: wp-includes/customize/class-wp-customize-nav-menu-location-control.php:78
msgid "Edit Menu"
msgstr "Modifica il menu"

#. translators: %s: Codex URL
#: wp-includes/formatting.php:4274
msgid "A structure tag is required when using custom permalinks. <a href=\"%s\">Learn more</a>"
msgstr "Quando usi permalink personalizzati devi avere una struttura di tag. <a href=\"%s\">Per saperne di più</a>"

#: wp-includes/media-template.php:1242
msgid "Image crop area preview. Requires mouse interaction."
msgstr "Anteprima dell'area di ritaglio. Interagisci con il mouse."

#: wp-includes/formatting.php:4275
msgid "https://codex.wordpress.org/Using_Permalinks#Choosing_your_permalink_structure"
msgstr "https://codex.wordpress.org/Using_Permalinks#Choosing_your_permalink_structure"

#: wp-includes/class-wp-xmlrpc-server.php:1709
#: wp-includes/class-wp-xmlrpc-server.php:4857
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:749
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:793
msgid "Sorry, you are not allowed to delete this post."
msgstr "Non hai i permessi per eliminare questo articolo."

#: wp-includes/class-wp-xmlrpc-server.php:2882
msgid "Sorry, you are not allowed to delete this page."
msgstr "Non hai i permessi per eliminare questa pagina."

#: wp-includes/class-wp-xmlrpc-server.php:2747
#: wp-includes/class-wp-xmlrpc-server.php:2944
msgid "Sorry, you are not allowed to edit this page."
msgstr "Non hai i permessi per modificare questa pagina."

#: wp-includes/class-wp-xmlrpc-server.php:4021
#: wp-includes/class-wp-xmlrpc-server.php:4069
#: wp-includes/class-wp-xmlrpc-server.php:5895
msgid "Sorry, you are not allowed to upload files."
msgstr "Non hai i permessi per caricare i file."

#: wp-includes/class-wp-xmlrpc-server.php:3141
msgid "Sorry, you are not allowed to add a category."
msgstr "Non hai i permessi per aggiungere una categoria."

#: wp-includes/class-wp-xmlrpc-server.php:5355
msgid "Sorry, you are not allowed to change the page author as this user."
msgstr "Non hai i permessi per cambiare l'autore della pagina con questo nome utente."

#: wp-includes/class-wp-xmlrpc-server.php:5350
msgid "Sorry, you are not allowed to change the post author as this user."
msgstr "Non hai i permessi per cambiare l'autore dell'articolo con questo utente."

#: wp-includes/class-wp-xmlrpc-server.php:4790
#: wp-includes/class-wp-xmlrpc-server.php:5464
#: wp-includes/class-wp-xmlrpc-server.php:6274
msgid "Sorry, you are not allowed to publish this post."
msgstr "Non hai i permessi per pubblicare questo articolo."

#: wp-includes/class-wp-xmlrpc-server.php:3969
msgid "Sorry, you are not allowed to update options."
msgstr "Non hai i permessi per aggiornare le opzioni."

#: wp-includes/class-wp-xmlrpc-server.php:3771
msgid "Sorry, you are not allowed access to details of this post."
msgstr "Non hai i permessi per accedere ai dettagli di questo articoli."

#: wp-includes/class-wp-xmlrpc-server.php:3730
#: wp-includes/class-wp-xmlrpc-server.php:3811
#: wp-includes/class-wp-xmlrpc-server.php:3843
#: wp-includes/class-wp-xmlrpc-server.php:3875
#: wp-includes/class-wp-xmlrpc-server.php:4113
msgid "Sorry, you are not allowed access to details about this site."
msgstr "Non hai i permessi per accedere ai dettagli riguardanti questo sito."

#: wp-includes/class-wp-xmlrpc-server.php:4944
msgid "Sorry, you are not allowed to publish pages on this site."
msgstr "Non hai i permessi per pubblicare pagine su questo sito."

#: wp-includes/class-wp-xmlrpc-server.php:4955
#: wp-includes/class-wp-xmlrpc-server.php:4968
#: wp-includes/class-wp-xmlrpc-server.php:4973
msgid "Sorry, you are not allowed to publish posts on this site."
msgstr "Non hai i permessi per pubblicare articoli su questo sito."

#: wp-includes/class-wp-xmlrpc-server.php:1390
#: wp-includes/class-wp-xmlrpc-server.php:4707
msgid "Sorry, you are not allowed to post on this site."
msgstr "Non hai i permessi per pubblicare articoli su questo sito."

#: wp-includes/class-wp-xmlrpc-server.php:1493
#: wp-includes/class-wp-xmlrpc-server.php:1517
msgid "Sorry, you are not allowed to assign a term to one of the given taxonomies."
msgstr "Non hai i permessi per assegnare un termine a una delle tassonomie."

#: wp-includes/class-wp-xmlrpc-server.php:1418
#: wp-includes/class-wp-xmlrpc-server.php:5010
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:491
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:499
msgid "Sorry, you are not allowed to create posts as this user."
msgstr "Non hai i permessi per  creare articoli con questo utente."

#: wp-includes/class-wp-xmlrpc-server.php:1546
msgid "Sorry, you are not allowed to add a term to one of the given taxonomies."
msgstr "Non hai i permessi per aggiungere un termine a una delle tassonomie."

#: wp-includes/class-wp-xmlrpc-server.php:1385
#: wp-includes/class-wp-xmlrpc-server.php:1806
#: wp-includes/class-wp-xmlrpc-server.php:4387
#: wp-includes/class-wp-xmlrpc-server.php:4560
#: wp-includes/class-wp-xmlrpc-server.php:4787
#: wp-includes/class-wp-xmlrpc-server.php:5291
#: wp-includes/class-wp-xmlrpc-server.php:5588
#: wp-includes/class-wp-xmlrpc-server.php:5930
#: wp-includes/class-wp-xmlrpc-server.php:6101
#: wp-includes/class-wp-xmlrpc-server.php:6155
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:397
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:629
msgid "Sorry, you are not allowed to edit this post."
msgstr "Non hai i permessi per modificare questo articolo."

#: wp-includes/class-wp-xmlrpc-server.php:2275
#: wp-includes/class-wp-xmlrpc-server.php:2370
msgid "Sorry, you are not allowed to assign terms in this taxonomy."
msgstr "Non hai i permessi per assegnare termini a questa tassonomia."

#: wp-includes/class-wp-xmlrpc-server.php:1950
msgid "Sorry, you are not allowed to create terms in this taxonomy."
msgstr "Non hai i permessi per creare termini in questa tassonomia."

#: wp-includes/class-wp-xmlrpc-server.php:3316
#: wp-includes/class-wp-xmlrpc-server.php:3515
msgid "Sorry, you are not allowed to moderate or edit this comment."
msgstr "Non hai i permessi per moderare o modificare questo commento."

#: wp-includes/class-wp-xmlrpc-server.php:5014
msgid "Sorry, you are not allowed to create pages as this user."
msgstr "Non hai i permessi per creare pagine con questo utente."

#: wp-includes/class-wp-xmlrpc-server.php:3046
#: wp-includes/class-wp-xmlrpc-server.php:4317
#: wp-includes/class-wp-xmlrpc-server.php:4613
#: wp-includes/class-wp-xmlrpc-server.php:5718
msgid "Sorry, you are not allowed to edit posts."
msgstr "Non hai i permessi per modificare gli articoli."

#: wp-includes/class-wp-xmlrpc-server.php:5462
msgid "Sorry, you are not allowed to publish this page."
msgstr "Non hai i permessi per pubblicare questa pagina."

#: wp-includes/class-wp-theme.php:783
msgid "Tan"
msgstr "Marrone chiaro"

#: wp-includes/class-wp-theme.php:784
msgid "Dark"
msgstr "Scuro"

#: wp-includes/class-wp-theme.php:785
msgid "Fixed Layout"
msgstr "Layout fisso"

#: wp-includes/class-wp-theme.php:786
msgid "Responsive Layout"
msgstr "Layout adattabile (responsive)"

#: wp-includes/class-wp-theme.php:787
msgid "Photoblogging"
msgstr "Photoblogging"

#: wp-includes/class-wp-theme.php:785
msgid "Fluid Layout"
msgstr "Layout fluido"

#: wp-includes/class-wp-theme.php:787
msgid "Seasonal"
msgstr "Stagionale"

#. translators: If comment number in your language requires declension, *
#. translate this to 'on'. Do not translate into your own language.
#: wp-includes/comment-template.php:900
msgctxt "Comment number declension: on or off"
msgid "off"
msgstr "off"

#: wp-includes/class-wp-xmlrpc-server.php:2500
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:596
msgid "Sorry, you are not allowed to edit this user."
msgstr "Non hai i permessi per modificare questo utente."

#: wp-includes/class-wp-xmlrpc-server.php:2631
#: wp-includes/class-wp-xmlrpc-server.php:2677
msgid "Sorry, you are not allowed to edit your profile."
msgstr "Non hai i permessi per modificare il tuo profilo."

#: wp-includes/class-wp-xmlrpc-server.php:2788
#: wp-includes/class-wp-xmlrpc-server.php:2990
msgid "Sorry, you are not allowed to edit pages."
msgstr "Non hai i permessi per modificare le pagine."

#: wp-includes/class-wp-xmlrpc-server.php:4514
msgid "Sorry, you are not allowed to access user data on this site."
msgstr "Non hai i permessi per accedere ai dati degli  utenti su questo sito. "

#: wp-includes/class-wp-tax-query.php:642
msgid "Inexistent terms."
msgstr "Termine inesistente."

#: wp-includes/media-template.php:255
msgid "List View"
msgstr "Elenco"

#: wp-includes/customize/class-wp-customize-nav-menu-location-control.php:78
msgid "Edit selected menu"
msgstr "Modifica il menu selezionato"

#: wp-includes/customize/class-wp-customize-header-image-control.php:205
msgid "Add new header image"
msgstr "Aggiungi una nuova immagine per l'header"

#: wp-includes/media-template.php:203
msgid "Drop files anywhere to upload"
msgstr "Rilascia i file qui dentro per caricarli"

#: wp-includes/media-template.php:204
msgctxt "Uploader: Drop files here - or - Select Files"
msgid "or"
msgstr "oppure"

#: wp-includes/media-template.php:205
msgid "Select Files"
msgstr "Seleziona i file"

#: wp-includes/customize/class-wp-customize-background-position-control.php:42
msgid "Top Left"
msgstr "In alto a sinistra"

#: wp-includes/customize/class-wp-customize-background-position-control.php:44
msgid "Top Right"
msgstr "In alto a destra"

#: wp-includes/class-wp-xmlrpc-server.php:3674
msgid "A valid email address is required."
msgstr "&Egrave; obbligatorio un indirizzo email valido."

#. translators: 1: file name, 2: error message
#: wp-includes/class-wp-xmlrpc-server.php:5921
msgid "Could not write file %1$s (%2$s)."
msgstr "Impossibile scrivere il file %1$s (%2$s)."

#: wp-includes/class-wp-xmlrpc-server.php:3638
msgid "Comment is required."
msgstr "Il commento è obbligatorio."

#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:395
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:412
#: wp-includes/comment.php:3119
msgid "Sorry, you must be logged in to comment."
msgstr "Devi essere connesso per commentare."

#: wp-includes/customize/class-wp-customize-themes-section.php:116
#: wp-includes/customize/class-wp-customize-themes-section.php:117
#: wp-includes/customize/class-wp-customize-themes-section.php:131
#: wp-includes/customize/class-wp-customize-themes-section.php:132
msgid "Search themes&hellip;"
msgstr "Cerca temi&hellip;"

#. translators: %s: "Update now" button
#: wp-includes/customize/class-wp-customize-theme-control.php:95
msgid "New version available. %s"
msgstr "Nuova versione disponibile. %s"

#: wp-includes/customize/class-wp-customize-selective-refresh.php:194
msgid "Click to edit this widget."
msgstr "Fai clic per modificare questo widget."

#: wp-includes/link-template.php:2536
msgctxt "previous set of posts"
msgid "Previous"
msgstr "Precedenti"

#: wp-login.php:590 wp-login.php:1027 wp-includes/general-template.php:396
msgid "Username or Email Address"
msgstr "Nome utente o indirizzo email"

#. translators: %s: theme name
#: wp-includes/customize/class-wp-customize-theme-control.php:67
msgid "Install and preview theme: %s"
msgstr "Installa e vedi in anteprima il tema: %s"

#. translators: %s: theme name
#: wp-includes/customize/class-wp-customize-theme-control.php:65
msgid "Live preview theme: %s"
msgstr "Visualizza anteprima del tema: %s"

#: wp-includes/class-wp-xmlrpc-server.php:1990
msgid "Sorry, your term could not be created."
msgstr "Il tuo termine non può essere creato."

#: wp-includes/class-wp-xmlrpc-server.php:3558
msgid "Sorry, the comment could not be edited."
msgstr "Il commento non può essere modificato."

#: wp-includes/customize/class-wp-customize-selective-refresh.php:196
msgid "Click to edit this element."
msgstr "Fai clic per modificare questo elemento."

#: wp-includes/customize/class-wp-customize-selective-refresh.php:193
msgid "Click to edit this menu."
msgstr "Fai clic per modificare questo menu."

#. translators: %s: themes panel title in the Customizer
#: wp-includes/customize/class-wp-customize-themes-panel.php:75
msgid "You are browsing %s"
msgstr "Stai sfogliando %s"

#: wp-includes/class-wp-xmlrpc-server.php:1595
#: wp-includes/class-wp-xmlrpc-server.php:4727
#: wp-includes/class-wp-xmlrpc-server.php:5186
msgid "Sorry, your entry could not be posted."
msgstr "Il tuo contributo non può essere pubblicato."

#: wp-includes/class-wp-xmlrpc-server.php:2574
msgid "Invalid role."
msgstr "Ruolo non valido."

#: wp-includes/customize/class-wp-customize-themes-section.php:122
msgid "Filter themes"
msgstr "Filtro temi"

#. translators: %s: theme name
#: wp-includes/customize/class-wp-customize-theme-control.php:61
msgid "Details for theme: %s"
msgstr "Dettagli per il tema: %s"

#. translators: %s: theme name
#: wp-includes/customize/class-wp-customize-theme-control.php:63
msgid "Customize theme: %s"
msgstr "Personalizza il tema: %s"

#: wp-includes/customize/class-wp-customize-selective-refresh.php:195
msgid "Click to edit the site title."
msgstr "Fai clic per modificare il titolo del sito."

#: wp-includes/class-wp-xmlrpc-server.php:5507
msgid "Sorry, your entry could not be edited."
msgstr "Il tuo inserimento non può essere modificato."

#: wp-includes/media-template.php:441 wp-includes/media-template.php:544
#: wp-includes/media.php:3537
msgid "Delete Permanently"
msgstr "Elimina definitivamente"

#: wp-includes/media-template.php:332
msgid "Document Preview"
msgstr "Anteprima documento"

#: wp-includes/customize/class-wp-customize-media-control.php:223
#: wp-includes/customize/class-wp-customize-media-control.php:228
msgid "Select video"
msgstr "Seleziona un video"

#: wp-includes/customize/class-wp-customize-media-control.php:224
msgid "Change video"
msgstr "Cambia il video"

#: wp-includes/widgets/class-wp-widget-media-video.php:31
#: wp-includes/customize/class-wp-customize-media-control.php:227
msgid "No video selected"
msgstr "Nessun video selezionato"

#: wp-includes/customize/class-wp-customize-media-control.php:229
msgid "Choose video"
msgstr "Scegli un video"

#: wp-includes/class-wp-xmlrpc-server.php:3448
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:770
msgid "Sorry, you are not allowed to delete this comment."
msgstr "Non hai i permessi per eliminare la risorsa. "

#: wp-includes/class-wp-xmlrpc-server.php:1325
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:495
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:637
msgid "Sorry, you are not allowed to make posts sticky."
msgstr "Non hai i permessi per rendere i post in evidenza."

#: wp-includes/class-wp-xmlrpc-server.php:2152
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:568
msgid "Sorry, you are not allowed to delete this term."
msgstr "Non hai i permessi per eliminare questo termine."

#: wp-includes/class-wp-xmlrpc-server.php:2056
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:340
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:483
msgid "Sorry, you are not allowed to edit this term."
msgstr "Non hai i permessi per modificare questo termine."

#: wp-includes/class-wp-xmlrpc-server.php:2224
msgid "Sorry, you are not allowed to assign this term."
msgstr "Non hai i permessi per assegnare questo termine."

#: wp-includes/class-wp-xmlrpc-server.php:2558
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:182
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:388
#: wp-includes/rest-api/endpoints/class-wp-rest-users-controller.php:390
msgid "Sorry, you are not allowed to list users."
msgstr "Non hai i permessi per l'elenco degli utenti."

#. translators: Theme author name
#: wp-includes/customize/class-wp-customize-theme-control.php:87
msgctxt "theme author"
msgid "By %s"
msgstr "Di %s"

#: wp-includes/class-wp-xmlrpc-server.php:2068
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:400
#: wp-includes/rest-api/endpoints/class-wp-rest-terms-controller.php:505
msgid "Cannot set parent term, taxonomy is not hierarchical."
msgstr "Non puoi impostare il termine genitore, la tassonomia non è gerarchica. "

#: wp-includes/class-wp-xmlrpc-server.php:1387
#: wp-includes/class-wp-xmlrpc-server.php:5299
msgid "The post type may not be changed."
msgstr "Il tipo di contenuto (post type) non può essere modificato."

#: wp-includes/class-wp-xmlrpc-server.php:1399
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1111
msgid "Sorry, you are not allowed to create private posts in this post type."
msgstr "Non hai i permessi per creare articoli privati per questo tipo di contenuto (post type)."

#: wp-includes/class-wp-xmlrpc-server.php:1413
msgid "Sorry, you are not allowed to create password protected posts in this post type."
msgstr "Non hai i permessi per creare articoli protetti da password per questo tipo di contenuto (post type)."

#: wp-includes/class-wp-xmlrpc-server.php:1867
#: wp-includes/class-wp-xmlrpc-server.php:4201
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:134
#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:82
#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:131
#: wp-includes/rest-api/endpoints/class-wp-rest-post-statuses-controller.php:84
msgid "Sorry, you are not allowed to edit posts in this post type."
msgstr "Non hai i permessi per modificare gli articoli di questo tipo di contenuto (post type)."

#: wp-includes/class-wp-xmlrpc-server.php:1404
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:1117
msgid "Sorry, you are not allowed to publish posts in this post type."
msgstr "Non hai i permessi per pubblicare articoli per questo tipo di contenuto (post type)."

#: wp-includes/l10n.php:1203
msgctxt "default site language"
msgid "Site Default"
msgstr "Lingua predefinita del sito"

#: wp-includes/class-wp-xmlrpc-server.php:1490
#: wp-includes/class-wp-xmlrpc-server.php:1514
msgid "Sorry, one of the given taxonomies is not supported by the post type."
msgstr "Una delle tassonomie non è supportata da questo tipo di contenuto (post type)."

#: wp-includes/link-template.php:2537
msgctxt "next set of posts"
msgid "Next"
msgstr "Successivi"

#: wp-includes/customize/class-wp-customize-custom-css-setting.php:161
msgid "Markup is not allowed in CSS."
msgstr "Il markup non è ammesso nel CSS."

#: wp-includes/script-loader.php:891
#: wp-includes/customize/class-wp-customize-nav-menu-control.php:55
#: wp-includes/customize/class-wp-widget-area-customize-control.php:61
msgid "Done"
msgstr "Fatto"

#: wp-includes/comment-template.php:1713
msgid "Log in to leave a Comment"
msgstr "Accedi per lasciare un commento"

#: wp-includes/comment-template.php:1600
msgid "Log in to Reply"
msgstr "Accedi per rispondere"

#: wp-includes/general-template.php:262
msgid "Log out"
msgstr "Esci"

#. translators: %s: logout URL
#: wp-includes/functions.php:2652
msgid "Do you really want to <a href=\"%s\">log out</a>?"
msgstr "Vuoi veramente <a href=\"%s\">uscire</a>?"

#. translators: %s: site name
#: wp-includes/functions.php:2645
msgid "You are attempting to log out of %s"
msgstr "Stai tentando di uscire da %s"

#: wp-includes/class-wp-xmlrpc-server.php:1383
#: wp-includes/class-wp-xmlrpc-server.php:1639
#: wp-includes/class-wp-xmlrpc-server.php:1705
#: wp-includes/class-wp-xmlrpc-server.php:1803
#: wp-includes/class-wp-xmlrpc-server.php:2744
#: wp-includes/class-wp-xmlrpc-server.php:3626
#: wp-includes/class-wp-xmlrpc-server.php:3630
#: wp-includes/class-wp-xmlrpc-server.php:3767
#: wp-includes/class-wp-xmlrpc-server.php:4314
#: wp-includes/class-wp-xmlrpc-server.php:4378
#: wp-includes/class-wp-xmlrpc-server.php:4381
#: wp-includes/class-wp-xmlrpc-server.php:4384
#: wp-includes/class-wp-xmlrpc-server.php:4557
#: wp-includes/class-wp-xmlrpc-server.php:5288
#: wp-includes/class-wp-xmlrpc-server.php:5585
#: wp-includes/class-wp-xmlrpc-server.php:6098
#: wp-includes/class-wp-xmlrpc-server.php:6152
#: wp-includes/class-wp-xmlrpc-server.php:6271
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:326
#: wp-includes/rest-api/endpoints/class-wp-rest-comments-controller.php:688
#: wp-includes/rest-api/endpoints/class-wp-rest-posts-controller.php:369
#: wp-includes/post.php:3180 wp-includes/post.php:3714
#: wp-includes/revision.php:292
msgid "Invalid post ID."
msgstr "ID articolo non valido."

#: wp-includes/class-wp-xmlrpc-server.php:3672
msgid "Comment author name and email are required."
msgstr "Il nome e l'email dell'autore del commento sono obbligatori."

#: wp-includes/widgets/class-wp-widget-media-audio.php:31
#: wp-includes/customize/class-wp-customize-media-control.php:237
msgid "No audio selected"
msgstr "Nessun audio selezionato"

#. translators: 1: suggested width number, 2: suggested height number.
#: wp-includes/media-template.php:240 wp-includes/media.php:3579
msgid "Suggested image dimensions: %1$s by %2$s pixels."
msgstr "Dimensioni suggerite per l'immagine: %1$s per %2$s pixel."

#: wp-includes/script-loader.php:291 wp-includes/functions.php:2153
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "Questo tipo di file non è permesso per ragioni di sicurezza."

#: wp-includes/comment.php:3066
msgid "Sorry, comments are not allowed for this item."
msgstr "I commenti non sono permessi per questo elemento."

#: wp-includes/class-wp-oembed-controller.php:83
msgid "The maximum height of the embed frame in pixels."
msgstr "L'altezza massima in pixel del frame incorporato."

#: wp-includes/class-wp-oembed-controller.php:77
msgid "The maximum width of the embed frame in pixels."
msgstr "La larghezza massima in pixel del frame incorporato."

#: wp-includes/class-wp-oembed-controller.php:62
msgid "The URL of the resource for which to fetch oEmbed data."
msgstr "La URL della risorsa da cui recuperare i dati oEmbed."

#: wp-includes/class-wp-oembed-controller.php:138
msgid "Sorry, you are not allowed to make proxied oEmbed requests."
msgstr "Non hai i permessi per fare richieste oEmbed mediante proxy."

#: wp-includes/class-wp-oembed-controller.php:88
msgid "Whether to perform an oEmbed discovery request for non-whitelisted providers."
msgstr "Se eseguire una richiesta esplorativa di oEmbed per provider non presenti nella whitelist."

#: wp-includes/media.php:3126
msgid "(no author)"
msgstr "(nessun autore)"

#: wp-includes/class-wp-oembed-controller.php:68
msgid "The oEmbed format to use."
msgstr "Il formato oEmbed da utilizzare."

#: wp-includes/functions.php:2661 wp-includes/ms-functions.php:2047
msgid "Please try again."
msgstr "Prova di nuovo."

#. translators: %s: "Search WordPress.org themes" button
#: wp-includes/customize/class-wp-customize-themes-section.php:91
msgid "No themes found. Try a different search, or %s."
msgstr "Nessun tema trovato. Prova una ricerca diversa, o %s"

#: wp-includes/customize/class-wp-customize-themes-section.php:92
msgid "Search WordPress.org themes"
msgstr "Cerca temi su WordPress.org"

#: wp-includes/customize/class-wp-customize-nav-menu-item-setting.php:713
msgid "Invalid URL."
msgstr "URL non valida."

#: wp-includes/customize/class-wp-customize-media-control.php:249
msgid "Choose image"
msgstr "Scegli immagine"

#: wp-includes/customize/class-wp-customize-media-control.php:239
msgid "Choose audio"
msgstr "Scegli audio"

#: wp-includes/customize/class-wp-customize-media-control.php:234
msgid "Change audio"
msgstr "Cambia audio"

#: wp-includes/customize/class-wp-customize-media-control.php:233
#: wp-includes/customize/class-wp-customize-media-control.php:238
msgid "Select audio"
msgstr "Seleziona audio"

#: wp-includes/customize/class-wp-customize-date-time-control.php:167
msgid "Minute"
msgstr "Minuti"

#: wp-includes/customize/class-wp-customize-date-time-control.php:146
msgid "Day"
msgstr "Giorno"

#: wp-includes/customize/class-wp-customize-date-time-control.php:130
msgid "Month"
msgstr "Mese"

#: wp-includes/customize/class-wp-customize-date-time-control.php:127
msgid "Date"
msgstr "Data"

#: wp-includes/customize/class-wp-customize-date-time-control.php:160
msgid "Time"
msgstr "Ora"

#: wp-includes/customize/class-wp-customize-themes-section.php:87
msgid "No themes found. Try a different search."
msgstr "Nessun tema trovato. Prova una ricerca differente."

#: wp-includes/customize/class-wp-customize-date-time-control.php:162
msgid "Hour"
msgstr "Ora"

#. translators: %s: number of themes displayed.
#: wp-includes/customize/class-wp-customize-themes-section.php:141
msgid "%s themes"
msgstr "%s temi"

#. translators: %s: UTC offset
#: wp-includes/customize/class-wp-customize-date-time-control.php:243
msgid "Timezone is %s."
msgstr "Il fuso orario è %s."

#: wp-includes/customize/class-wp-customize-date-time-control.php:176
msgid "Timezone"
msgstr "Fuso orario"

#. translators: %s: Template
#: wp-includes/class-wp-theme.php:255
msgid "The theme defines itself as its parent theme. Please check the %s header."
msgstr "Questo tema definisce se stesso come tema genitore. Controlla l'intestazione di %s ."

#. translators: 1: month number (01, 02, etc.), 2: month abbreviation
#: wp-includes/customize/class-wp-customize-date-time-control.php:201
msgid "%1$s-%2$s"
msgstr "%1$s-%2$s"

#: wp-includes/customize/class-wp-customize-date-time-control.php:170
msgid "Meridian"
msgstr "Meridiano"

#. translators: %s: number of filters selected.
#: wp-includes/customize/class-wp-customize-themes-section.php:125
msgid "Filter themes (%s)"
msgstr "Filtra temi (%s)"

#: wp-includes/functions.php:5777
msgid "Only UUID V4 is supported at this time."
msgstr "Al momento è supportato solo UUID V4"

#. translators: 1: timezone name, 2: timezone abbreviation, 3: gmt offset
#: wp-includes/customize/class-wp-customize-date-time-control.php:234
msgid "Timezone is %1$s (%2$s), currently %3$s."
msgstr "Il fuso orario è %1$s (%2$s), attualmente %3$s."

#: wp-includes/deprecated.php:3927 wp-includes/deprecated.php:3944
msgid "The Press This plugin is required."
msgstr "Il plugin Press This è obbligatorio."

#: wp-includes/customize/class-wp-customize-theme-control.php:126
msgid "Install &amp; Preview"
msgstr "Installazione &amp; Anteprima"

#: wp-includes/customize/class-wp-customize-nav-menu-locations-control.php:46
msgctxt "menu locations"
msgid "Where do you want this menu to appear?"
msgstr "Dove vuoi che compaia questo menu?"

#. translators: Do not translate OLD_EMAIL, NEW_EMAIL, SITENAME, SITEURL: those
#. are placeholders.
#: wp-includes/functions.php:5841
msgid ""
"Hi,\n"
"\n"
"This notice confirms that the admin email address was changed on ###SITENAME###.\n"
"\n"
"The new admin email address is ###NEW_EMAIL###.\n"
"\n"
"This email has been sent to ###OLD_EMAIL###\n"
"\n"
"Regards,\n"
"All at ###SITENAME###\n"
"###SITEURL###"
msgstr ""
"Ciao,\n"
"\n"
"Questo avviso conferma il cambio del'indirizzo mail dell'amministratore del sito  ###SITENAME###.\n"
"\n"
"Il nuovo indirizzo mail è ###NEW_EMAIL###.\n"
"\n"
"Questa mail è stata inviata a ###OLD_EMAIL###\n"
"\n"
"SAluti,\n"
"A tutti ###SITENAME###\n"
"###SITEURL###"

#. translators: 1: Codex URL, 2: additional link attributes, 3: accessibility
#. text
#: wp-includes/customize/class-wp-customize-nav-menu-locations-control.php:51
msgctxt "menu locations"
msgid "(If you plan to use a menu <a href=\"%1$s\" %2$s>widget%3$s</a>, skip this step.)"
msgstr "(Se hai in programma di usare un menu <a href=\"%1$s\" %2$s>widget%3$s</a>, salta questo passaggio.)"

#: wp-includes/customize/class-wp-customize-nav-menu-locations-control.php:52
msgid "https://codex.wordpress.org/WordPress_Widgets"
msgstr "https://codex.wordpress.org/WordPress_Widgets (in inglese)"

#: wp-includes/customize/class-wp-customize-nav-menu-locations-control.php:63
msgctxt "menu locations"
msgid "Here&#8217;s where this menu appears. If you&#8217;d like to change that, pick another location."
msgstr "Qui è dove appare il menu. Se desideri modificarlo, seleziona una diversa posizione."

#: wp-includes/load.php:1134
msgid "Scrape nonce check failed. Please try again."
msgstr "Controllo del nonce fallito. Prova ancora"

#. translators: %s: header size in pixels
#: wp-includes/customize/class-wp-customize-header-image-control.php:177
msgid "Click &#8220;Add new image&#8221; to upload an image file from your computer. Your theme works best with an image with a header size of %s pixels &#8212; you&#8217;ll be able to crop your image once you upload it for a perfect fit."
msgstr "Fare clic su &#8220;Aggiungi nuova immagine&#8221; per caricare un file immagine dal tuo computer. Il tema funziona al meglio con una immagine di dimensione pari a %s pixel &#8212; potrai ritagliare l'immagine dopo il caricamento per adattarla al meglio."

#. translators: %s: header height in pixels
#: wp-includes/customize/class-wp-customize-header-image-control.php:187
msgid "Click &#8220;Add new image&#8221; to upload an image file from your computer. Your theme works best with an image with a header height of %s pixels &#8212; you&#8217;ll be able to crop your image once you upload it for a perfect fit."
msgstr "Fare clic su &#8220;Aggiungi nuova immagine&#8221; per caricare un file immagine dal tuo computer. Il tema funziona al meglio con una immagine con una testata di altezza pari a %s pixel &#8212; potrai ritagliare l'immagine dopo il caricamento per adattarla al meglio."

#. translators: %s: header width in pixels
#: wp-includes/customize/class-wp-customize-header-image-control.php:182
msgid "Click &#8220;Add new image&#8221; to upload an image file from your computer. Your theme works best with an image with a header width of %s pixels &#8212; you&#8217;ll be able to crop your image once you upload it for a perfect fit."
msgstr "Fare clic su &#8220;Aggiungi nuova immagine&#8221; per caricare un file immagine dal tuo computer. Il tema funziona al meglio con una immagine con una testata di larghezza pari a %s pixel &#8212; potrai ritagliare l'immagine dopo il caricamento per adattarla al meglio."

#: wp-includes/customize/class-wp-customize-header-image-control.php:174
msgid "Click &#8220;Add new image&#8221; to upload an image file from your computer. Your theme works best with an image that matches the size of your video &#8212; you&#8217;ll be able to crop your image once you upload it for a perfect fit."
msgstr "Fare clic su &#8220;Aggiungi nuova immagine&#8221; per caricare un file immagine dal tuo computer. Il tema funziona al meglio con una immagine che corrisponde alla dimensione del video &#8212; potrai ritagliare l'immagine dopo il caricamento per adattarla al meglio."

#: wp-includes/customize/class-wp-customize-nav-menu-location-control.php:77
msgid "+ Create New Menu"
msgstr "+ Crea un nuovo menu"

#: wp-includes/customize/class-wp-customize-nav-menu-location-control.php:77
msgid "Create a menu for this location"
msgstr "Crea un menu per questa posizione"

#: wp-includes/class-wp-xmlrpc-server.php:3217
msgid "Sorry, you are not allowed to delete this category."
msgstr "Non hai i permessi per per eliminare questa categoria."

#: wp-includes/customize/class-wp-customize-themes-section.php:113
msgid "Back to theme sources"
msgstr "Torna alla sorgente del tema"

#: wp-includes/class-wp-user.php:716
msgid "Usage of user levels is deprecated. Use capabilities instead."
msgstr "L'uso dei livelli utente è deprecato. Utilizza invece le capacità."

#. translators: %s: "Add Items" button text
#: wp-includes/customize/class-wp-customize-nav-menu-control.php:44
msgid "Time to add some links! Click &#8220;%s&#8221; to start putting pages, categories, and custom links in your menu. Add as many things as you&#8217;d like."
msgstr "È ora di aggiungere qualche link! Fai clic su  &#8220;%s&#8221; per iniziare ad inserire pagine, categorie e link personalizzati al tuo menu. Aggiungi quanti elementi vuoi."

#: wp-includes/media-template.php:347
msgid "File size:"
msgstr "Dimensione del file:"

#: wp-includes/customize/class-wp-customize-theme-control.php:113
#: wp-includes/customize/class-wp-customize-theme-control.php:121
msgctxt "theme"
msgid "Installed"
msgstr "Installato"

#: wp-includes/functions.php:2656
msgid "The link you followed has expired."
msgstr "Il link che hai seguito è scaduto."

#: wp-includes/comment.php:3175 wp-includes/comment.php:3284
msgid "WordPress Comments"
msgstr "Commenti WordPress"

#: wp-includes/comment.php:3217
msgid "Comment URL"
msgstr "URL commento"

#: wp-includes/comment.php:3216
msgid "Comment Content"
msgstr "Contenuto commento"

#: wp-includes/comment.php:3215
msgid "Comment Date"
msgstr "Data commento"

#: wp-includes/comment.php:3210
msgid "Comment Author"
msgstr "Autore commento"

#: wp-includes/comment.php:3214
msgid "Comment Author User Agent"
msgstr "User agent autore commento"

#: wp-includes/comment.php:3213
msgid "Comment Author IP"
msgstr "IP autore commento"

#: wp-includes/comment.php:3212
msgid "Comment Author URL"
msgstr "URL autore commento"

#: wp-includes/comment.php:3211
msgid "Comment Author Email"
msgstr "Email autore commento"

#. translators: deleted text
#: wp-includes/functions.php:5993
msgid "[deleted]"
msgstr "[eliminato]"

#. translators: deleted long text
#: wp-includes/functions.php:5997
msgid "This content was deleted by the author."
msgstr "Questo contenuto è stato eliminato dall'autore."

#. translators: %d: Comment ID
#: wp-includes/comment.php:3361
msgid "Comment %d contains personal data but could not be anonymized."
msgstr "Il commento %d contiene dati personali che potrebbero non essere anonimizzabili."

#: wp-includes/comment-template.php:2211
msgid "Save my name, email, and website in this browser for the next time I comment."
msgstr "Do il mio consenso affinché un cookie salvi i miei dati  (nome, email, sito web) per il prossimo commento."

#: wp-includes/comment-template.php:2263
msgid "Post Comment"
msgstr "Pubblica il commento"

#. translators: %s: "Update now" button
#: wp-includes/customize/class-wp-customize-theme-control.php:95
msgid "Update now"
msgstr "Aggiorna il tema ora"

#: wp-includes/customize/class-wp-customize-themes-panel.php:49
msgid "Change theme"
msgstr "Cambia il tema"

#. translators: Site admin email change notification email subject. %s: Site
#. title
#: wp-includes/functions.php:5856
msgid "[%s] Notice of Admin Email Change"
msgstr "[%s] Avviso del cambio di email dell'amministratore"

#: wp-includes/comment.php:3159
msgid "<strong>ERROR</strong>: The comment could not be saved. Please try again later."
msgstr "<strong>ERRORE</strong>: il commento non può essere salvato. Riprovare più tardi."

#. translators: If months in your language require a genitive case, * translate
#. this to 'on'. Do not translate into your own language.
#: wp-includes/functions.php:177
msgctxt "decline months names: on or off"
msgid "off"
msgstr "on"

#: wp-includes/comment.php:244
msgctxt "comment status"
msgid "Trash"
msgstr "Eliminato"

#: wp-includes/link-template.php:2824
msgid "Newer comments"
msgstr "Commenti più recenti"

#: wp-includes/link-template.php:2823
msgid "Older comments"
msgstr "Commenti meno recenti"

#: wp-includes/media-template.php:438 wp-includes/media-template.php:541
msgctxt "verb"
msgid "Trash"
msgstr "Elimina"

#: wp-includes/media-template.php:670 wp-includes/media-template.php:728
#: wp-includes/media-template.php:977
msgid "Attachment Page"
msgstr "Pagina dell’allegato"

#: wp-includes/media-template.php:382 wp-includes/media-template.php:563
#: wp-includes/revision.php:34 wp-includes/class-wp-editor.php:1077
msgid "Title"
msgstr "Titolo"

#: wp-includes/class-wp-editor.php:1640
msgid "Y/m/d"
msgstr "d/m/Y"

#: wp-includes/widgets/class-wp-widget-categories.php:30
#: wp-includes/widgets/class-wp-widget-categories.php:47
#: wp-includes/category-template.php:519
#: wp-includes/theme-compat/sidebar.php:97
msgid "Categories"
msgstr "Categorie"

#: wp-includes/class-wp-customize-widgets.php:751 wp-includes/media.php:3538
#: wp-includes/class-wp-editor.php:1259
msgid "Apply"
msgstr "Applica"

#: wp-includes/media-template.php:408 wp-includes/media-template.php:589
#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:123
#: wp-includes/class-wp-editor.php:1080
msgid "Description"
msgstr "Descrizione"

#: wp-includes/class-wp-editor.php:1081 wp-includes/theme-compat/sidebar.php:27
msgid "Author"
msgstr "Autore"

#: wp-includes/widgets/class-wp-widget-media-image.php:105
#: wp-includes/media-template.php:376 wp-includes/media-template.php:557
#: wp-includes/media.php:3494 wp-includes/media.php:4017
#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:86
#: wp-includes/class-wp-editor.php:1134 wp-includes/class-wp-editor.php:1706
#: wp-includes/class-wp-customize-nav-menus.php:1111
msgid "URL"
msgstr "URL"

#: wp-includes/class-wp-editor.php:1257
#: wp-includes/class-walker-comment.php:236
#: wp-includes/class-walker-comment.php:340
msgid "Edit"
msgstr "Modifica"

#: wp-includes/class-wp-editor.php:1046
msgid "Undo"
msgstr "Annulla"

#: wp-includes/script-loader.php:242 wp-includes/script-loader.php:527
#: wp-includes/script-loader.php:897
#: wp-includes/class-wp-customize-nav-menus.php:276
msgid "No results found."
msgstr "Nessun risultato trovato."

#: wp-includes/script-loader.php:549 wp-includes/script-loader.php:701
#: wp-includes/class-wp-customize-manager.php:4617
msgid "Publish"
msgstr "Pubblica"

#: wp-includes/script-loader.php:525 wp-includes/class-wp-editor.php:1750
msgid "Add Link"
msgstr "Aggiungi link"

#: wp-includes/script-loader.php:733 wp-includes/class-wp-editor.php:1119
msgid "Save"
msgstr "Salva"

#: wp-includes/class-wp-editor.php:1131
msgid "Target"
msgstr "Destinazione"

#: wp-includes/class-wp-editor.php:1087
msgid "Advanced"
msgstr "Avanzate"

#: wp-includes/admin-bar.php:468
msgid "Users"
msgstr "Utenti"

#: wp-includes/media-template.php:1247
#: wp-includes/class-wp-customize-manager.php:4156
#: wp-includes/class-wp-editor.php:1117
msgid "Preview"
msgstr "Anteprima"

#: wp-includes/admin-bar.php:486
msgid "Plugins"
msgstr "Plugin"

#: wp-includes/script-loader.php:510 wp-includes/script-loader.php:561
#: wp-includes/script-loader.php:693 wp-includes/media.php:3498
#: wp-includes/class-wp-editor.php:1050 wp-includes/class-wp-editor.php:1747
msgid "Cancel"
msgstr "Annulla"

#: wp-includes/script-loader.php:551 wp-includes/script-loader.php:705
#: wp-includes/class-wp-customize-manager.php:4622
msgid "Save Draft"
msgstr "Salva bozza"

#: wp-includes/class-wp-editor.php:1047
msgid "Redo"
msgstr "Ripeti"

#: wp-includes/media-template.php:620 wp-includes/media-template.php:851
#: wp-includes/media-template.php:917
#: wp-includes/customize/class-wp-customize-background-position-control.php:47
#: wp-includes/class-wp-editor.php:1202
msgid "Left"
msgstr "Sinistra"

#: wp-includes/media-template.php:623 wp-includes/media-template.php:854
#: wp-includes/media-template.php:920
#: wp-includes/customize/class-wp-customize-background-position-control.php:48
#: wp-includes/class-wp-editor.php:1203
msgid "Center"
msgstr "Centro"

#: wp-includes/media-template.php:626 wp-includes/media-template.php:857
#: wp-includes/media-template.php:923
#: wp-includes/customize/class-wp-customize-background-position-control.php:49
#: wp-includes/class-wp-editor.php:1204
msgid "Right"
msgstr "Destra"

#: wp-includes/media-template.php:612 wp-includes/class-wp-editor.php:1200
msgid "Alignment"
msgstr "Allineamento"

#: wp-includes/widgets/class-wp-widget-media-image.php:83
#: wp-includes/media-template.php:398 wp-includes/media-template.php:579
#: wp-includes/media-template.php:837 wp-includes/media-template.php:902
#: wp-includes/class-wp-editor.php:1199
msgid "Caption"
msgstr "Didascalia"

#: wp-includes/media.php:4022 wp-includes/class-wp-editor.php:1101
msgid "Media"
msgstr "Media"

#: wp-includes/script-loader.php:692 wp-includes/class-wp-editor.php:1049
msgid "OK"
msgstr "OK"

#: wp-activate.php:178 wp-includes/post-template.php:1611
msgid "Password:"
msgstr "Password:"

#: wp-includes/class-wp-customize-control.php:634
#: wp-includes/class-wp-customize-nav-menus.php:1081
msgid "Add"
msgstr "Aggiungi"

#: wp-includes/admin-bar.php:904 wp-includes/admin-bar.php:905
#: wp-includes/media.php:3496 wp-includes/class-wp-editor.php:1722
msgid "Search"
msgstr "Cerca"

#: wp-includes/category-template.php:148
msgid "Uncategorized"
msgstr "Senza categoria"

#: wp-includes/script-loader.php:277 wp-includes/script-loader.php:371
#: wp-includes/script-loader.php:562 wp-includes/script-loader.php:1137
#: wp-includes/class-wp-customize-manager.php:5301
#: wp-includes/class-wp-editor.php:1051 wp-includes/class-wp-editor.php:1701
msgid "Close"
msgstr "Chiudi"

#: wp-includes/admin-bar.php:550 wp-includes/post.php:1449
msgid "New Post"
msgstr "Nuovo articolo"

#: wp-includes/class-wp-customize-section.php:359
#: wp-includes/class-wp-customize-panel.php:366
#: wp-includes/customize/class-wp-customize-themes-panel.php:80
#: wp-includes/customize/class-wp-customize-nav-menus-panel.php:82
msgid "Help"
msgstr "Aiuto"

#: wp-includes/widgets/class-wp-widget-media-image.php:70
#: wp-includes/media-template.php:965 wp-includes/class-wp-editor.php:1198
msgid "Width"
msgstr "Larghezza"

#: wp-includes/admin-bar.php:355 wp-includes/admin-bar.php:451
#: wp-includes/admin-bar.php:534 wp-includes/deprecated.php:2800
#: wp-includes/deprecated.php:2802
msgid "Dashboard"
msgstr "Bacheca"

#. translators: %s: themes panel title in the Customizer
#: wp-includes/admin-bar.php:477 wp-includes/admin-bar.php:817
#: wp-includes/customize/class-wp-customize-themes-panel.php:75
msgid "Themes"
msgstr "Temi"

#: wp-includes/admin-bar.php:495
msgid "Settings"
msgstr "Impostazioni"

#: wp-includes/class-wp-editor.php:1086
msgid "General"
msgstr "Generale"

#: wp-includes/widgets/class-wp-widget-media-image.php:76
#: wp-includes/media-template.php:965 wp-includes/class-wp-editor.php:1197
msgid "Height"
msgstr "Altezza"

#: wp-includes/script-loader.php:865
#: wp-includes/customize/class-wp-customize-color-control.php:51
#: wp-includes/customize/class-wp-customize-media-control.php:225
#: wp-includes/customize/class-wp-customize-media-control.php:235
#: wp-includes/customize/class-wp-customize-media-control.php:245
#: wp-includes/customize/class-wp-customize-media-control.php:255
#: wp-includes/class-wp-customize-manager.php:4941
msgid "Default"
msgstr "Predefinito"

#: wp-includes/admin-bar.php:830 wp-includes/class-wp-customize-widgets.php:412
#: wp-includes/functions.php:3764
msgid "Widgets"
msgstr "Widget"

#: wp-includes/category-template.php:1131
msgid "Tags: "
msgstr "Tag:"

#. translators: %s: author's display name
#: wp-includes/author-template.php:294 wp-includes/author-template.php:456
msgid "Posts by %s"
msgstr "Articoli scritti da: %s"

#: wp-includes/bookmark-template.php:83
msgid "Last updated: %s"
msgstr "Ultimo aggiornamento: %s"

#: wp-includes/category-template.php:516 wp-includes/taxonomy.php:521
msgid "No categories"
msgstr "Nessuna categoria"

#: wp-includes/class-walker-category.php:132
msgid "Feed for all posts filed under %s"
msgstr "Feed per tutti gli articoli archiviati in %s"

#. translators: 1: comment date, 2: comment time
#: wp-includes/class-walker-comment.php:282
msgid "(Edit)"
msgstr "(Modifica)"

#: wp-includes/class-http.php:267
msgid "User has blocked requests through HTTP."
msgstr "L'utente ha bloccato le richieste via HTTP."

#: wp-includes/class-http.php:970 wp-includes/class-wp-http-curl.php:232
#: wp-includes/class-wp-http-curl.php:271
msgid "Too many redirects."
msgstr "Troppi redirect."

#: wp-includes/class-wp-editor.php:1118
msgid "Print"
msgstr "Stampa"

#: wp-includes/class-wp-editor.php:1085
msgid "Insert/edit image"
msgstr "Inserisci/Modifica immagine"

#: wp-includes/script-loader.php:523 wp-includes/class-wp-editor.php:1124
#: wp-includes/class-wp-editor.php:1130 wp-includes/class-wp-editor.php:1700
msgid "Insert/edit link"
msgstr "Inserisci/Modifica link"

#: wp-includes/class-wp-editor.php:1184
msgid "Insert row before"
msgstr "Inserisci una riga prima"

#: wp-includes/class-wp-editor.php:1185
msgid "Insert row after"
msgstr "Inserisci una riga dopo"

#: wp-includes/class-wp-editor.php:1186
msgid "Insert column before"
msgstr "Inserisci una colonna prima"

#: wp-includes/class-wp-editor.php:1187
msgid "Insert column after"
msgstr "Inserisci una colonna dopo"

#: wp-includes/class-wp-editor.php:1194
msgid "Merge table cells"
msgstr "Unisci celle tabella"

#: wp-includes/class-wp-editor.php:1169
msgid "Table properties"
msgstr "Proprietà tabella"

#: wp-includes/class-wp-editor.php:1188
msgid "Paste table row before"
msgstr "Incolla riga tabella prima"

#: wp-includes/class-wp-editor.php:1189
msgid "Paste table row after"
msgstr "Incolla riga tabella dopo"

#: wp-includes/class-wp-editor.php:1192
msgid "Cut table row"
msgstr "Taglia riga tabella"

#: wp-includes/class-wp-editor.php:1174
msgid "Row"
msgstr "Riga"

#: wp-includes/class-wp-editor.php:1075
msgid "Document properties"
msgstr "Proprietà documento"

#: wp-includes/class-wp-editor.php:1015
msgid "Paragraph"
msgstr "Paragrafo"

#: wp-includes/script-loader.php:105 wp-includes/class-wp-editor.php:1016
msgid "Blockquote"
msgstr "Citazione"

#: wp-includes/script-loader.php:118 wp-includes/class-wp-editor.php:1030
msgid "Code"
msgstr "Codice"

#: wp-includes/script-loader.php:100 wp-includes/class-wp-editor.php:1028
msgid "Bold"
msgstr "Grassetto"

#: wp-includes/script-loader.php:102 wp-includes/class-wp-editor.php:1029
msgid "Italic"
msgstr "Corsivo"

#: wp-includes/class-wp-editor.php:1023
msgid "Underline"
msgstr "Sottolineato"

#: wp-includes/class-wp-editor.php:1024
msgid "Strikethrough"
msgstr "Barrato"

#: wp-includes/class-wp-editor.php:1025
msgid "Subscript"
msgstr "Pedice"

#: wp-includes/class-wp-editor.php:1026
msgid "Superscript"
msgstr "Apice"

#: wp-includes/class-wp-editor.php:1042
msgid "Cut"
msgstr "Taglia"

#: wp-includes/class-wp-customize-manager.php:4202
#: wp-includes/class-wp-editor.php:1043
msgid "Copy"
msgstr "Copia"

#: wp-includes/class-wp-editor.php:1044
msgid "Paste"
msgstr "Incolla"

#: wp-includes/class-wp-editor.php:1002
msgid "New document"
msgstr "Nuovo documento"

#: wp-includes/class-wp-editor.php:1092
msgid "Image description"
msgstr "Descrizione immagine"

#: wp-includes/class-wp-editor.php:1089
msgid "Border"
msgstr "Bordo"

#: wp-includes/class-wp-editor.php:1094
msgid "Dimensions"
msgstr "Dimensioni"

#: wp-includes/class-wp-editor.php:1091
msgid "Vertical space"
msgstr "Spaziatura verticale"

#: wp-includes/class-wp-editor.php:1122
msgid "Horizontal space"
msgstr "Spaziatura orizzontale"

#: wp-includes/customize/class-wp-customize-background-position-control.php:43
#: wp-includes/class-wp-editor.php:1207
msgid "Top"
msgstr "Alto"

#: wp-includes/class-wp-editor.php:1208
msgid "Middle"
msgstr "Mediano"

#: wp-includes/customize/class-wp-customize-background-position-control.php:53
#: wp-includes/class-wp-editor.php:1209
msgid "Bottom"
msgstr "Basso"

#: wp-includes/class-wp-editor.php:1090
msgid "Constrain proportions"
msgstr "Mantieni le proporzioni"

#: wp-includes/admin-bar.php:842
msgid "Background"
msgstr "Sfondo"

#: wp-includes/script-loader.php:366 wp-includes/class-wp-editor.php:1120
msgid "Fullscreen"
msgstr "Schermo intero"

#: wp-includes/class-wp-editor.php:1088
msgid "Source"
msgstr "Sorgente"

#: wp-includes/class-wp-editor.php:1277
msgid "Letter"
msgstr "Lettera"

#: wp-includes/script-loader.php:563 wp-includes/class-wp-editor.php:1278
msgid "Action"
msgstr "Azione"

#: wp-includes/class-wp-editor.php:1045
msgid "Select all"
msgstr "Seleziona tutto"

#: wp-includes/class-wp-editor.php:1160
msgid "Check Spelling"
msgstr "Verifica ortografia"

#: wp-includes/script-loader.php:104 wp-includes/class-wp-editor.php:1129
msgid "Insert link"
msgstr "Inserisci link"

#: wp-includes/class-wp-editor.php:1125
msgid "Remove link"
msgstr "Rimuovi link"

#: wp-includes/script-loader.php:275
#: wp-includes/widgets/class-wp-widget-media-image.php:25
#: wp-includes/class-wp-editor.php:1084
msgid "Image"
msgstr "Immagine"

#: wp-includes/script-loader.php:146 wp-includes/script-loader.php:307
#: wp-includes/widgets/class-wp-widget-text.php:521
#: wp-includes/widgets/class-wp-widget-text.php:536
#: wp-includes/class-wp-customize-manager.php:4129
msgid "Dismiss"
msgstr "Rimuovi"

#: wp-activate.php:125
msgid "Activation Key Required"
msgstr "&Egrave; richiesta una chiave di attivazione"

#: wp-activate.php:128
msgid "Activation Key:"
msgstr "Chiave di attivazione:"

#: wp-activate.php:177 wp-signup.php:230
msgid "Username:"
msgstr "Nome utente:"

#: wp-includes/admin-bar.php:459
msgid "Sites"
msgstr "Siti"

#: wp-includes/admin-bar.php:836
#: wp-includes/customize/class-wp-customize-nav-menus-panel.php:98
#: wp-includes/class-wp-customize-nav-menus.php:581
msgid "Menus"
msgstr "Menu"

#: wp-includes/class-wp-xmlrpc-server.php:540
#: wp-includes/class-wp-customize-manager.php:4872
msgid "Site Title"
msgstr "Titolo sito"

#: wp-includes/script-loader.php:1141
#: wp-includes/class-wp-customize-nav-menus.php:967 wp-signup.php:564
msgid "Next"
msgstr "Successivo"

#: wp-activate.php:30 wp-activate.php:165
msgid "An error occurred during the activation"
msgstr "Si è verificato un errore durante l'attivazione."

#: wp-includes/admin-bar.php:559
msgid "Manage Comments"
msgstr "Gestione commenti"

#: wp-includes/admin-bar.php:593
msgid "Shortlink"
msgstr "Shortlink"

#: wp-includes/class-http.php:549
msgid "There are no HTTP transports available which can complete the requested request."
msgstr "Non vi sono transport HTTP disponibili che permettono di completare la richiesta effettuata."

#: wp-includes/class-http.php:280
msgid "Destination directory for file streaming does not exist or is not writable."
msgstr "La directory di destinazione per il file di streaming non esiste o non è scrivibile."

#: wp-activate.php:132 wp-includes/script-loader.php:841
#: wp-includes/script-loader.php:842
msgid "Activate"
msgstr "Attiva"

#: wp-includes/class-wp-editor.php:1103
msgid "Poster"
msgstr "Poster"

#: wp-includes/class-wp-editor.php:1128
msgid "Link"
msgstr "Link"

#: wp-includes/admin-bar.php:147
msgid "WordPress.org"
msgstr "WordPress.org"

#: wp-includes/admin-bar.php:155
msgid "Documentation"
msgstr "Documentazione"

#: wp-includes/admin-bar.php:163
msgid "Support Forums"
msgstr "Forum di supporto"

#: wp-includes/admin-bar.php:120 wp-includes/admin-bar.php:138
msgid "About WordPress"
msgstr "Informazioni su WordPress"

#: wp-includes/admin-bar.php:790
msgid "%s comment awaiting moderation"
msgid_plural "%s comments awaiting moderation"
msgstr[0] "%s commento in attesa di approvazione"
msgstr[1] "%s commenti in attesa di approvazione"

#: wp-includes/admin-bar.php:757
msgctxt "admin bar menu group label"
msgid "New"
msgstr "Nuovo"

#: wp-includes/class-wp-customize-manager.php:5237
msgid "Your latest posts"
msgstr "Gli ultimi articoli"

#. translators: %s: document title from the preview
#: wp-includes/class-wp-customize-manager.php:4369
msgid "Customize: %s"
msgstr "Personalizza %s"

#: wp-includes/class-wp-customize-manager.php:4883
msgid "Tagline"
msgstr "Motto"

#: wp-includes/admin-bar.php:345
msgid "Edit Site"
msgstr "Modifica sito"

#: wp-includes/class-wp-customize-manager.php:5238
msgid "A static page"
msgstr "Una pagina statica"

#: wp-includes/customize/class-wp-customize-background-image-control.php:30
#: wp-includes/class-wp-customize-manager.php:5105
msgid "Background Image"
msgstr "Immagine di sfondo"

#: wp-includes/class-wp-customize-manager.php:5260
msgid "Posts page"
msgstr "Pagina articoli"

#: wp-includes/media-template.php:474
#: wp-includes/class-wp-customize-widgets.php:753 wp-includes/media.php:3501
#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:139
#: wp-includes/customize/class-wp-customize-media-control.php:226
#: wp-includes/customize/class-wp-customize-media-control.php:236
#: wp-includes/customize/class-wp-customize-media-control.php:246
#: wp-includes/customize/class-wp-customize-media-control.php:256
#: wp-includes/class-wp-customize-manager.php:4940
#: wp-includes/class-wp-editor.php:1256
msgid "Remove"
msgstr "Rimuovi"

#: wp-includes/admin-bar.php:397
#: wp-includes/customize/class-wp-customize-theme-control.php:110
msgid "Customize"
msgstr "Personalizza"

#: wp-includes/class-wp-customize-manager.php:4958
msgid "Colors"
msgstr "Colori"

#: wp-includes/widgets/class-wp-nav-menu-widget.php:143
#: wp-includes/class-wp-customize-control.php:586
#: wp-includes/class-wp-customize-nav-menus.php:610
msgid "&mdash; Select &mdash;"
msgstr "&mdash; Seleziona &mdash;"

#: wp-includes/class-wp-admin-bar.php:128
msgid "The menu ID should not be empty."
msgstr "L'ID del menu non dovrebbe essere vuoto."

#: wp-includes/class-wp-editor.php:1006
msgid "Heading 1"
msgstr "Titolo 1"

#: wp-includes/class-wp-editor.php:1007
msgid "Heading 2"
msgstr "Titolo 2"

#: wp-includes/class-wp-editor.php:1008
msgid "Heading 3"
msgstr "Titolo 3"

#: wp-includes/class-wp-editor.php:1009
msgid "Heading 4"
msgstr "Titolo 4"

#: wp-includes/class-wp-editor.php:1010
msgid "Heading 5"
msgstr "Titolo 5"

#: wp-includes/class-wp-editor.php:1011
msgid "Heading 6"
msgstr "Titolo 6"

#: wp-includes/class-wp-editor.php:184 wp-includes/class-wp-editor.php:1262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Testo"

#: wp-includes/class-wp-image-editor-imagick.php:134
#: wp-includes/class-wp-image-editor-gd.php:92
msgid "File doesn&#8217;t exist?"
msgstr "Il file non esiste?"

#. translators: 1: login URL, 2: network home URL
#: wp-activate.php:193
msgid "Your account is now activated. <a href=\"%1$s\">Log in</a> or go back to the <a href=\"%2$s\">homepage</a>."
msgstr "Il tuo account è stato attivato. <a href=\"%1$s\">Accedi</a> o torna alla <a href=\"%2$s\">homepage</a>."

#. translators: %s: comment author link
#: wp-includes/class-walker-comment.php:269
#: wp-includes/class-walker-comment.php:325
msgid "%s <span class=\"says\">says:</span>"
msgstr "%s <span class=\"says\">ha detto:</span>"

#: wp-includes/class-walker-comment.php:236
msgid "Pingback:"
msgstr "Pingback:"

#. translators: 1: site URL, 2: username, 3: user email, 4: lost password URL
#: wp-activate.php:155
msgid "Your site at %1$s is active. You may now log in to your site using your chosen username of &#8220;%2$s&#8221;. Please check your email inbox at %3$s for your password and login instructions. If you do not receive an email, please check your junk or spam folder. If you still do not receive an email within an hour, you can <a href=\"%4$s\">reset your password</a>."
msgstr "Il sito <a href=\"%1$s\">%2$s</a> è attivo. Ora puoi accedere al sito utilizzando il nome utente scelto &#8220;%3$s&#8221;. Controlla la tua email %4$s per la password e le istruzioni di login. Se non ricevi alcuna email controlla la cartella di spam o junk. Se entro un'ora non hai ancora ricevuto l'email, puoi <a href=\"%5$s\">reimpostare la password</a>."

#: wp-includes/admin-bar.php:444
msgid "Network Admin"
msgstr "Gestione network"

#: wp-includes/admin-bar.php:431
msgid "My Sites"
msgstr "I miei siti"

#: wp-includes/class-wp-http-streams.php:261
#: wp-includes/class-wp-http-curl.php:257
msgid "Failed to write request to temporary file."
msgstr "Fallito il tentativo di scrittura di un file temporano."

#: wp-includes/class-wp-http-streams.php:155
#: wp-includes/class-wp-http-streams.php:163
msgid "The SSL certificate for the host could not be verified."
msgstr "Il certificato SSL per questo host non può essere verificato."

#: wp-includes/admin-bar.php:187
msgid "Menu"
msgstr "Menu"

#. translators: block tags
#: wp-includes/class-wp-editor.php:1014
msgctxt "TinyMCE"
msgid "Blocks"
msgstr "Blocchi"

#: wp-includes/class-wp-editor.php:1067
msgctxt "Name of link anchor (TinyMCE)"
msgid "Name"
msgstr "Nome"

#: wp-includes/class-wp-editor.php:1068
msgctxt "Link anchor (TinyMCE)"
msgid "Anchor"
msgstr "Ancora"

#: wp-includes/class-wp-editor.php:1151
msgctxt "find/replace"
msgid "Next"
msgstr "Prossimo"

#: wp-includes/class-wp-editor.php:1157
msgctxt "find/replace"
msgid "Find"
msgstr "Cerca"

#: wp-includes/class-wp-editor.php:1163
msgctxt "spellcheck"
msgid "Ignore"
msgstr "Ignora"

#: wp-includes/class-wp-editor.php:1162
msgctxt "spellcheck"
msgid "Ignore all"
msgstr "Ignora tutto"

#: wp-includes/class-wp-editor.php:1161
msgctxt "spellcheck"
msgid "Finish"
msgstr "Finito"

#: wp-includes/class-wp-editor.php:1177
msgctxt "table columns"
msgid "Cols"
msgstr "Colonne"

#: wp-includes/class-wp-editor.php:1150
msgctxt "find/replace"
msgid "Replace"
msgstr "Sostituire"

#. translators: previous
#: wp-includes/class-wp-editor.php:1153
msgctxt "find/replace"
msgid "Prev"
msgstr "Precedente"

#: wp-includes/class-wp-editor.php:1158
msgctxt "find/replace"
msgid "Replace all"
msgstr "Sostituire tutto"

#: wp-includes/class-wp-editor.php:1156
msgctxt "find/replace"
msgid "Replace with"
msgstr "Sostituire con"

#: wp-includes/class-wp-editor.php:1154
msgctxt "find/replace"
msgid "Whole words"
msgstr "Parole intere"

#: wp-includes/class-wp-editor.php:1181
msgctxt "table body"
msgid "Body"
msgstr "Corpo"

#: wp-includes/class-wp-editor.php:1243
msgctxt "TinyMCE menu"
msgid "Edit"
msgstr "Modifica"

#: wp-includes/class-wp-editor.php:1224
msgctxt "editor button"
msgid "Show blocks"
msgstr "Mostra blocchi"

#: wp-includes/class-wp-editor.php:1037
msgid "Align left"
msgstr "Allinea a sinistra"

#: wp-includes/class-wp-editor.php:1035
msgid "Align center"
msgstr "Allinea al centro"

#: wp-includes/class-wp-editor.php:1036
msgid "Align right"
msgstr "Allinea a destra"

#: wp-includes/script-loader.php:114 wp-includes/class-wp-editor.php:1055
msgid "Numbered list"
msgstr "Elenco numerato"

#: wp-includes/script-loader.php:112 wp-includes/class-wp-editor.php:1054
msgid "Bulleted list"
msgstr "Elenco puntato"

#: wp-includes/class-wp-editor.php:1056
msgctxt "list style"
msgid "Square"
msgstr "Quadrato"

#: wp-includes/class-wp-editor.php:1058
msgctxt "list style"
msgid "Circle"
msgstr "Cerchio vuoto"

#: wp-includes/class-wp-editor.php:1059
msgctxt "list style"
msgid "Disc"
msgstr "Cerchio pieno"

#: wp-includes/class-wp-editor.php:1060
msgctxt "list style"
msgid "Lower Greek"
msgstr "Lettera greca minuscola"

#: wp-includes/class-wp-editor.php:1061
msgctxt "list style"
msgid "Lower Alpha"
msgstr "Lettera minuscola"

#: wp-includes/class-wp-editor.php:1062
msgctxt "list style"
msgid "Upper Alpha"
msgstr "Lettera maiuscola"

#: wp-includes/class-wp-editor.php:1063
msgctxt "list style"
msgid "Upper Roman"
msgstr "Numero romano maiuscolo"

#: wp-includes/class-wp-editor.php:1064
msgctxt "list style"
msgid "Lower Roman"
msgstr "Numero romano minuscolo"

#: wp-includes/class-wp-editor.php:1003
msgctxt "TinyMCE"
msgid "Formats"
msgstr "Formati"

#: wp-includes/class-wp-editor.php:1217
msgctxt "table cell scope attribute"
msgid "Scope"
msgstr "Ambito"

#: wp-includes/class-wp-editor.php:1176
msgctxt "table column"
msgid "Column"
msgstr "Colonna"

#: wp-includes/class-wp-editor.php:1017
msgctxt "HTML tag"
msgid "Div"
msgstr "Div"

#: wp-includes/class-wp-editor.php:1018
msgctxt "HTML tag"
msgid "Pre"
msgstr "Pre"

#: wp-includes/class-wp-editor.php:1022
msgctxt "HTML elements"
msgid "Inline"
msgstr "Inline"

#: wp-includes/class-wp-editor.php:1069
msgctxt "Link anchors (TinyMCE)"
msgid "Anchors"
msgstr "Ancore"

#: wp-includes/class-wp-editor.php:1178
msgctxt "table cell"
msgid "Cell"
msgstr "Cella"

#. translators: word count
#: wp-includes/class-wp-editor.php:1228
msgid "Words: %s"
msgstr "Parole: %s"

#: wp-includes/class-wp-editor.php:1244
msgctxt "TinyMCE menu"
msgid "Tools"
msgstr "Strumenti"

#: wp-includes/class-wp-editor.php:1246
msgctxt "TinyMCE menu"
msgid "Table"
msgstr "Tabella"

#: wp-includes/class-wp-customize-section.php:350
#: wp-includes/class-wp-customize-widgets.php:797 wp-includes/media.php:3502
#: wp-includes/class-wp-customize-panel.php:359
#: wp-includes/customize/class-wp-customize-themes-panel.php:70
#: wp-includes/customize/class-wp-customize-nav-menus-panel.php:72
#: wp-includes/class-wp-customize-nav-menus.php:1001
msgid "Back"
msgstr "Indietro"

#: wp-includes/script-loader.php:111 wp-includes/class-wp-editor.php:1095
msgid "Insert image"
msgstr "Inserisci immagine"

#: wp-includes/class-wp-editor.php:1242
msgctxt "TinyMCE menu"
msgid "File"
msgstr "File"

#: wp-includes/class-wp-editor.php:1149
msgid "Could not find the specified string."
msgstr "Non riesco a trovare la stringa specifica."

#: wp-includes/class-wp-editor.php:1212
msgid "Column group"
msgstr "Colonna gruppo"

#: wp-includes/class-wp-editor.php:1093
msgid "Style"
msgstr "Stile"

#: wp-includes/class-wp-editor.php:1039
msgid "Increase indent"
msgstr "Aumenta rientro"

#: wp-includes/class-wp-editor.php:1027
msgid "Clear formatting"
msgstr "Cancella formattazione"

#: wp-includes/class-wp-editor.php:1040
msgid "Decrease indent"
msgstr "Decrementa rientro"

#: wp-includes/class-wp-editor.php:1038
msgid "Justify"
msgstr "Giustificato"

#: wp-includes/class-wp-editor.php:1123
msgid "Restore last draft"
msgstr "Ripristina l'ultima bozza"

#: wp-includes/class-wp-editor.php:1110
msgid "Special character"
msgstr "Caratteri speciali"

#: wp-includes/class-wp-editor.php:1031
msgid "Source code"
msgstr "Codice sorgente"

#: wp-includes/class-wp-editor.php:1113
msgid "Emoticons"
msgstr "Emoticons"

#: wp-includes/class-wp-editor.php:1076
msgid "Robots"
msgstr "Robots"

#: wp-includes/class-wp-editor.php:1213
msgid "Row type"
msgstr "Tipo riga"

#: wp-includes/class-wp-editor.php:1167
msgid "Insert table"
msgstr "Inserisci tabella"

#: wp-includes/class-wp-editor.php:1214
msgid "Cell type"
msgstr "Tipo cella"

#: wp-includes/class-wp-editor.php:1211
msgid "Row group"
msgstr "Gruppo riga"

#: wp-includes/class-wp-editor.php:1222
msgid "Background color"
msgstr "Colore di sfondo"

#: wp-includes/class-wp-editor.php:1223
msgid "Text color"
msgstr "Colore del testo"

#: wp-includes/class-wp-editor.php:1225
msgid "Show invisible characters"
msgstr "Mostra caratteri invisibili"

#: wp-includes/class-wp-editor.php:1104
msgid "Alternative source"
msgstr "Sorgente alternativa"

#: wp-includes/class-wp-editor.php:1078
msgid "Keywords"
msgstr "Parole chiave"

#: wp-includes/class-wp-editor.php:1097
msgid "Insert date/time"
msgstr "Inserisci data/ora"

#: wp-includes/class-wp-editor.php:1106
msgid "Insert video"
msgstr "Inserisci video"

#: wp-includes/class-wp-editor.php:1107
msgid "Embed"
msgstr "Embed"

#: wp-includes/class-wp-editor.php:1114
msgid "Nonbreaking space"
msgstr "Spazio senza interruzioni"

#: wp-includes/class-wp-editor.php:1115
msgid "Page break"
msgstr "Interruzione di pagina"

#: wp-includes/class-wp-editor.php:1116
msgid "Paste as text"
msgstr "Incolla come testo"

#: wp-includes/class-wp-editor.php:1155
msgid "Find and replace"
msgstr "Trova e sostituisci"

#: wp-includes/class-wp-editor.php:1175
msgid "Rows"
msgstr "Righe"

#: wp-includes/class-wp-editor.php:1215
msgid "Cell padding"
msgstr "Padding cella"

#: wp-includes/class-wp-editor.php:1216
msgid "Cell spacing"
msgstr "Spaziatura cella"

#: wp-includes/class-wp-editor.php:1159
msgid "Match case"
msgstr "Argomento per confronto"

#: wp-includes/class-wp-editor.php:1265
msgid "Keyboard Shortcuts"
msgstr "Scorciatoie da tastiera"

#: wp-includes/class-wp-editor.php:1205
msgctxt "table cell alignment attribute"
msgid "None"
msgstr "Nessuno"

#: wp-includes/class-wp-editor.php:1032
msgid "Font Family"
msgstr "Tipo carattere"

#: wp-includes/class-wp-editor.php:1033
msgid "Font Sizes"
msgstr "Dimensione carattere"

#: wp-includes/class-wp-editor.php:1195
msgid "Split table cell"
msgstr "Dividi cella"

#: wp-includes/class-wp-editor.php:1005
msgctxt "TinyMCE"
msgid "Headings"
msgstr "Titoli"

#: wp-includes/class-wp-customize-widgets.php:692
msgctxt "Move widget"
msgid "Move"
msgstr "Sposta"

#: wp-includes/class-wp-customize-widgets.php:754
msgid "Trash widget by moving it to the inactive widgets sidebar."
msgstr "Cestina il widget spostandolo nell&#8217;area dei widget inattivi."

#: wp-includes/class-wp-customize-widgets.php:752
msgid "Save and preview changes before publishing them."
msgstr "Salva e vedi in anteprima le modifiche prima di pubblicarle."

#: wp-includes/class-wp-customize-widgets.php:808
msgid "Search Widgets"
msgstr "Ricerca widget"

#: wp-includes/class-wp-customize-widgets.php:809
msgid "Search widgets&hellip;"
msgstr "Ricerca widget&hellip;"

#: wp-includes/class-wp-customize-widgets.php:691
msgid "Select an area to move this widget into:"
msgstr "Seleziona un&#8217;area nella quale spostare questo widget:"

#: wp-includes/admin-bar.php:751
msgctxt "add new from admin bar"
msgid "User"
msgstr "Utente"

#: wp-includes/admin-bar.php:278
msgid "Edit My Profile"
msgstr "Modifica il tuo profilo"

#: wp-includes/class-wp-editor.php:1020
msgctxt "HTML tag"
msgid "Address"
msgstr "Indirizzo"

#: wp-includes/class-wp-customize-widgets.php:1139
msgid "Shift-click to edit this widget."
msgstr "Fai clic tenendo premuto il tasto maiuscole per modificare questo widget"

#. translators: 1: Title of a menu item, 2: Type of a menu item
#: wp-includes/class-wp-customize-nav-menus.php:935
msgid "Add to menu: %1$s (%2$s)"
msgstr "Aggiungi al menu: %1$s (%2$s)"

#: wp-includes/class-wp-editor.php:1283
msgid "When starting a new paragraph with one of these formatting shortcuts followed by a space, the formatting will be applied automatically. Press Backspace or Escape to undo."
msgstr "Quando inizi un nuovo paragrafo con uno di questi modelli seguiti da uno spazio, verrà applicata automaticamente la formattazione indicata. Premi Backspace oppure Esc per annullare."

#: wp-includes/class-wp-customize-manager.php:4910
msgid "Site Icon"
msgstr "Icona del sito"

#: wp-includes/customize/class-wp-customize-nav-menu-item-setting.php:322
#: wp-includes/class-wp-customize-nav-menus.php:155
#: wp-includes/class-wp-customize-nav-menus.php:382
#: wp-includes/class-wp-customize-nav-menus.php:429
#: wp-includes/nav-menu.php:821
msgid "Custom Link"
msgstr "Link personalizzato"

#: wp-includes/media-template.php:820 wp-includes/class-wp-editor.php:1710
#: wp-includes/class-wp-customize-nav-menus.php:1115
msgid "Link Text"
msgstr "Testo del link"

#: wp-includes/class-wp-editor.php:1019
msgctxt "HTML tag"
msgid "Preformatted"
msgstr "Preformattato"

#: wp-includes/class-wp-customize-nav-menus.php:578
msgid "Menus can be displayed in locations defined by your theme."
msgstr "I menu possono venir visualizzati in differenti posizioni definite dal tema utilizzato."

#: wp-includes/customize/class-wp-customize-nav-menu-setting.php:430
#: wp-includes/class-wp-customize-nav-menus.php:428
msgctxt "Missing menu name."
msgid "(unnamed)"
msgstr "(senza nome)"

#: wp-includes/class-wp-customize-widgets.php:762
#: wp-includes/class-wp-customize-nav-menus.php:455
msgid "Reorder mode closed"
msgstr "Modalità di riordino chiusa"

#: wp-includes/class-wp-customize-widgets.php:761
#: wp-includes/class-wp-customize-nav-menus.php:454
msgid "Reorder mode enabled"
msgstr "Modalità di riordino abilitata"

#: wp-includes/class-wp-customize-nav-menus.php:457
msgid "Close reorder mode"
msgstr "Chiudere la modalità di riordino"

#: wp-includes/customize/class-wp-customize-nav-menu-control.php:53
#: wp-includes/class-wp-customize-nav-menus.php:456
msgid "Reorder menu items"
msgstr "Riordinare gli elementi del menu"

#: wp-includes/class-wp-customize-nav-menus.php:573
msgid "This panel is used for managing navigation menus for content you have already published on your site. You can create menus and add items for existing content such as pages, posts, categories, tags, formats, or custom links."
msgstr "Questo pannello è utilizzato per gestire i menu di navigazione per contenuti che hai già pubblicato sul tuo sito. Puoi creare i menu ed aggiungere degli elementi per contenuti esistenti come pagine, articoli, categorie, tag, formati di articoli oppure link personalizzati."

#: wp-includes/class-wp-customize-section.php:232
msgid "Customizing"
msgstr "Stai personalizzando"

#. translators: %s: number of menu locations
#: wp-includes/class-wp-customize-nav-menus.php:595
msgid "Your theme can display menus in %s location. Select which menu you would like to use."
msgid_plural "Your theme can display menus in %s locations. Select which menu appears in each location."
msgstr[0] "Il tuo tema supporta %s posizione per i menu. Seleziona il menu che vuoi usare."
msgstr[1] "Il tuo tema supporta %s posizioni per i menu. Seleziona quale menu appare in ciascuna posizione."

#. translators: &#9656; is the unicode right-pointing triangle, and %s is the
#. section title in the Customizer
#: wp-includes/class-wp-customize-section.php:230
#: wp-includes/class-wp-customize-widgets.php:802
#: wp-includes/class-wp-customize-nav-menus.php:446
#: wp-includes/class-wp-customize-nav-menus.php:1007
msgid "Customizing &#9656; %s"
msgstr "Stai personalizzando &#9656; %s"

#: wp-includes/class-wp-customize-nav-menus.php:443
msgid "Menu item moved out of submenu"
msgstr "Elemento del menu spostato fuori dal submenu"

#: wp-includes/class-wp-editor.php:1269
msgid "Inline toolbar (when an image, link or preview is selected)"
msgstr "Barra degli strumenti in linea (quando una immagine, un link o una anteprima è selezionata)"

#: wp-includes/class-wp-customize-nav-menus.php:441
msgid "Menu item moved up"
msgstr "Voce di menu spostata su"

#: wp-includes/class-wp-customize-nav-menus.php:439
msgid "Menu created"
msgstr "Menu creato"

#: wp-includes/class-wp-customize-nav-menus.php:452
msgid "Additional items found: %d"
msgstr "Trovati elementi addizionali: %d"

#: wp-includes/class-wp-customize-nav-menus.php:444
msgid "Menu item is now a sub-item"
msgstr "La voce del menu è ora una sotto-voce"

#: wp-includes/class-wp-customize-nav-menus.php:437
msgid "Menu item added"
msgstr "Voce menu aggiunta"

#: wp-includes/class-wp-customize-nav-menus.php:442
msgid "Menu item moved down"
msgstr "Voce del menu spostata giù"

#: wp-includes/class-wp-customize-nav-menus.php:451
msgid "Number of items found: %d"
msgstr "Numero di elementi trovati: %d"

#: wp-includes/class-wp-customize-nav-menus.php:1010
msgid "Add Menu Items"
msgstr "Aggiungi elementi al menu"

#. translators: %s: site name
#: wp-includes/admin-bar.php:319
msgid "User Dashboard: %s"
msgstr "Bacheca utente: %s"

#: wp-includes/class-wp-editor.php:1274
msgid "Shift + Alt + letter:"
msgstr "Shift + Alt + lettera:"

#: wp-includes/class-wp-editor.php:1267
msgid "Additional shortcuts,"
msgstr "Scorciatoie aggiuntive,"

#: wp-includes/class-wp-customize-nav-menus.php:950
msgid "Move one level up"
msgstr "Sposta sopra di un livello"

#: wp-includes/class-wp-editor.php:1266
msgid "Default shortcuts,"
msgstr "Scorciatoie predefinite,"

#: wp-includes/class-wp-customize-nav-menus.php:951
msgid "Move one level down"
msgstr "Sposta sotto di un livello"

#: wp-includes/class-wp-customize-nav-menus.php:433
msgid "Menu Locations"
msgstr "Posizioni del menu"

#: wp-includes/class-wp-customize-nav-menus.php:1016
msgid "Search menu items&hellip;"
msgstr "Cerca tra gli elementi del menu&hellip;"

#: wp-includes/class-wp-editor.php:1276
msgid "Ctrl + letter:"
msgstr "Ctrl + lettera:"

#: wp-includes/class-wp-editor.php:1275
msgid "Cmd + letter:"
msgstr "Cmd + lettera:"

#: wp-includes/class-wp-editor.php:1273
msgid "Ctrl + Alt + letter:"
msgstr "Ctrl + Alt + lettera:"

#: wp-includes/class-wp-customize-nav-menus.php:1120
msgid "Add to Menu"
msgstr "Aggiungi al menu"

#: wp-includes/class-wp-customize-nav-menus.php:1102
msgid "Custom Links"
msgstr "Link personalizzati"

#. translators: 1: error message, 2: line number
#: wp-includes/atomlib.php:175
msgid "XML Error: %1$s at line %2$s"
msgstr "Errore XML: %1$s alla riga %2$s"

#: wp-includes/class-wp-customize-nav-menus.php:153
#: wp-includes/class-wp-customize-nav-menus.php:375
msgctxt "nav menu home label"
msgid "Home"
msgstr "Home"

#. translators: %s: title of menu item which is invalid
#: wp-includes/class-wp-customize-nav-menus.php:448
msgid "%s (Invalid)"
msgstr "%s (non valido)"

#. translators: %s: title of menu item in draft status
#: wp-includes/class-wp-customize-nav-menus.php:450
msgid "%s (Pending)"
msgstr "%s (in sospeso)"

#: wp-includes/class-wp-customize-nav-menus.php:474
msgid "Move up one"
msgstr "Sposta in su di uno"

#: wp-includes/class-wp-customize-nav-menus.php:475
msgid "Move down one"
msgstr "Sposta in giù di uno"

#: wp-includes/class-wp-customize-nav-menus.php:476
msgid "Move to the top"
msgstr "Sposta in cima"

#. translators: %s: previous item name
#: wp-includes/class-wp-customize-nav-menus.php:478
msgid "Move under %s"
msgstr "Sposta sotto %s"

#. translators: %s: previous item name
#: wp-includes/class-wp-customize-nav-menus.php:480
msgid "Move out from under %s"
msgstr "Sposta fuori da sotto %s"

#. translators: %s: previous item name
#: wp-includes/class-wp-customize-nav-menus.php:482
msgid "Under %s"
msgstr "Sotto %s"

#. translators: %s: previous item name
#: wp-includes/class-wp-customize-nav-menus.php:484
msgid "Out from under %s"
msgstr "Fuori da sotto %s"

#. translators: 1: item name, 2: item position, 3: total number of items
#: wp-includes/class-wp-customize-nav-menus.php:486
msgid "%1$s. Menu item %2$d of %3$d."
msgstr "%1$s. Elemento %2$d di %3$d.del menu"

#. translators: 1: item name, 2: item position, 3: parent item name
#: wp-includes/class-wp-customize-nav-menus.php:488
msgid "%1$s. Sub item number %2$d under %3$s."
msgstr "%1$s. Sottoelemento numero %2$d sotto %3$s."

#: wp-includes/class-wp-customize-widgets.php:812
#: wp-includes/customize/class-wp-customize-themes-section.php:119
#: wp-includes/customize/class-wp-customize-themes-section.php:134
#: wp-includes/class-wp-customize-nav-menus.php:1017
msgid "The search results will be updated as you type."
msgstr "I risultati della ricerca vengono aggiornati mentre digiti."

#: wp-includes/class-wp-customize-widgets.php:756
msgid "Widget moved up"
msgstr "Widget spostato in su"

#: wp-includes/class-wp-editor.php:1281
msgid "To move focus to other buttons use Tab or the arrow keys. To return focus to the editor press Escape or use one of the buttons."
msgstr "Per spostare il focus su altri pulsanti usare Tab o i tasti cursore. Per ripristinare il focus sull'editor premere Esc oppure premere uno dei pulsanti."

#: wp-includes/admin-bar.php:156
msgid "https://codex.wordpress.org/"
msgstr "https://codex.wordpress.org/"

#: wp-includes/class-wp-editor.php:1052
msgid "Visual aids"
msgstr "Aiuti visuali"

#: wp-includes/class-wp-editor.php:1718
msgid "Or link to existing content"
msgstr "Oppure inserisci un link a un contenuto esistente "

#: wp-includes/class-wp-admin-bar.php:415
msgid "Skip to toolbar"
msgstr "Vai alla barra degli strumenti"

#: wp-includes/class-wp-editor.php:1735
msgid "No search term specified. Showing recent items."
msgstr "Nessun termine di ricerca inserito. Vengono mostrati i contenuti più recenti."

#. translators: %d: ID of a post
#: wp-includes/class-walker-page-dropdown.php:71
#: wp-includes/class-walker-page.php:152
#: wp-includes/customize/class-wp-customize-nav-menu-item-setting.php:278
#: wp-includes/class-wp-customize-nav-menus.php:194
#: wp-includes/class-wp-customize-nav-menus.php:337
#: wp-includes/nav-menu.php:784 wp-includes/nav-menu.php:862
msgid "#%d (no title)"
msgstr "#%d (senza titolo)"

#: wp-includes/class-wp-editor.php:1253
msgid "Read more..."
msgstr "Leggi tutto..."

#. translators: %s: the site/panel title in the Customizer
#: wp-includes/class-wp-customize-panel.php:363
#: wp-includes/customize/class-wp-customize-nav-menus-panel.php:78
msgid "You are customizing %s"
msgstr "Stai personalizzando %s"

#: wp-includes/class-wp-customize-panel.php:339
msgid "Press return or enter to open this panel"
msgstr "Premi return o invio per aprire questo pannello"

#: wp-includes/script-loader.php:900
#: wp-includes/class-wp-customize-nav-menus.php:427
msgctxt "missing menu item navigation label"
msgid "(no label)"
msgstr "(nessuna etichetta)"

#: wp-includes/class-wp-editor.php:1247
msgctxt "TinyMCE menu"
msgid "Format"
msgstr "Formato"

#: wp-includes/admin-bar.php:731
msgctxt "add new from admin bar"
msgid "Link"
msgstr "Link"

#: wp-includes/class-wp-editor.php:1057
msgctxt "list style"
msgid "Default"
msgstr "Predefinito"

#: wp-includes/class-wp-editor.php:1704
msgid "Enter the destination URL"
msgstr "Inserisci la URL di destinazione"

#: wp-includes/class-wp-editor.php:1140
msgid "Color"
msgstr "Colore"

#: wp-includes/class-wp-editor.php:1142
msgctxt "label for custom color"
msgid "Custom..."
msgstr "Personalizza..."

#: wp-includes/class-wp-editor.php:1143
msgid "No color"
msgstr "Nessun colore"

#: wp-includes/class-wp-editor.php:1206
msgctxt "vertical table cell alignment"
msgid "V Align"
msgstr "V Align"

#: wp-includes/class-wp-editor.php:1201
msgctxt "horizontal table cell alignment"
msgid "H Align"
msgstr "H Align"

#: wp-includes/class-wp-editor.php:1141
msgid "Custom color"
msgstr "Colore personalizzato"

#: wp-includes/class-wp-editor.php:1164
msgid "Add to Dictionary"
msgstr "Aggiungi al dizionario"

#: wp-includes/class-wp-editor.php:1172
msgid "Border color"
msgstr "Colore bordo"

#: wp-includes/class-wp-editor.php:1255
msgid "No alignment"
msgstr "Nessun allineamento"

#: wp-includes/class-wp-editor.php:1268
msgid "Focus shortcuts:"
msgstr "Scorciatoie focus:"

#: wp-includes/class-wp-editor.php:1272
msgid "Elements path"
msgstr "Percorso elementi"

#: wp-includes/class-wp-customize-widgets.php:757
msgid "Widget moved down"
msgstr "Widget spostato giù"

#: wp-includes/class-wp-editor.php:1271
msgid "Editor toolbar"
msgstr "Barra degli strumenti dell'editor"

#: wp-includes/class-wp-editor.php:1270
msgid "Editor menu (when enabled)"
msgstr "Menu dell'editor (se abilitato)"

#: wp-includes/script-loader.php:99 wp-includes/class-wp-editor.php:1254
msgid "Distraction-free writing mode"
msgstr "Modalità di scrittura senza distrazioni"

#: wp-includes/class-wp-customize-nav-menus.php:1066
msgid "No items"
msgstr "Nessun elemento presente"

#: wp-includes/class-wp-customize-nav-menus.php:435
msgid "Menu Name"
msgstr "Nome menu"

#. translators: %s: Title of a section with menu items
#: wp-includes/class-wp-customize-nav-menus.php:1070
msgid "Toggle section: %s"
msgstr "Cambia sezione: %s"

#: wp-includes/class-walker-comment.php:275
#: wp-includes/class-walker-comment.php:344
msgid "Your comment is awaiting moderation."
msgstr "Il tuo commento è in attesa di moderazione."

#: wp-includes/class-wp-customize-manager.php:581
msgid "The requested theme does not exist."
msgstr "Il tema richiesto non esiste."

#: wp-includes/class-wp-customize-widgets.php:763
#: wp-includes/customize/class-wp-widget-area-customize-control.php:59
msgid "Reorder widgets"
msgstr "Riordina i widget"

#. translators: 1: "type => link", 2: "taxonomy => link_category"
#. translators: 1: caller_get_posts, 2: ignore_sticky_posts
#: wp-includes/category.php:46 wp-includes/category-template.php:343
#: wp-includes/class-wp-query.php:1669
msgid "%1$s is deprecated. Use %2$s instead."
msgstr "%1$s è deprecato. Utilizza invece %2$s ."

#. translators: %s: document title from the preview
#: wp-includes/class-wp-customize-manager.php:4372
msgid "Live Preview: %s"
msgstr "Anteprima in tempo reale: %s"

#. translators: %s: get_trackback_url()
#. translators: %s: get_the_author()
#: wp-includes/comment-template.php:1138 wp-includes/author-template.php:66
msgid "Use %s instead if you do not want the value echoed."
msgstr "Utilizza invece %s se non vuoi che il valore venga visualizzato."

#: wp-includes/class-wp-editor.php:1245
msgctxt "TinyMCE menu"
msgid "View"
msgstr "Visualizza"

#: wp-includes/class-wp-xmlrpc-server.php:6326 wp-includes/class-http.php:263
#: wp-includes/class-http.php:482
msgid "A valid URL was not provided."
msgstr "Non è stata fornita una URL valida."

#: wp-includes/class-wp-admin-bar.php:417
msgid "Toolbar"
msgstr "Barra degli strumenti"

#: wp-includes/class-wp-customize-manager.php:4996
msgid "Background Color"
msgstr "Colore di sfondo"

#: wp-includes/class-wp-editor.php:1219
msgctxt "TinyMCE"
msgid "Insert template"
msgstr "Inserisci modello"

#: wp-includes/class-wp-customize-widgets.php:413
msgid "Widgets are independent sections of content that can be placed into widgetized areas provided by your theme (commonly called sidebars)."
msgstr "I widget sono sezioni di contenuto indipendente che possono venir inserite nelle aree widget predisposte, fornite dal proprio tema (comunemente sono indicate come barre laterali)."

#: wp-includes/class-wp-editor.php:1220
msgctxt "TinyMCE"
msgid "Templates"
msgstr "Modelli"

#: wp-includes/class-wp-customize-nav-menus.php:1015
msgid "Search Menu Items"
msgstr "Cerca tra gli elementi del menu"

#: wp-includes/class-wp-editor.php:1238
msgid "Your browser does not support direct access to the clipboard. Please use keyboard shortcuts or your browser&#8217;s edit menu instead."
msgstr "Il tuo browser non supporta l'accesso diretto agli appunti. Utilizza allora le scorciatoie di tastiera o il menu modifica del tuo browser."

#: wp-includes/class-wp-customize-section.php:344
msgid "Press return or enter to open this section"
msgstr "Premi return o invio per aprire questa sezione"

#. translators: %s:      menu location
#: wp-includes/class-wp-customize-nav-menus.php:432
msgctxt "menu"
msgid "(Currently set to: %s)"
msgstr "(Attualmente impostato a: %s)"

#: wp-includes/class-wp-customize-nav-menus.php:1104
msgid "Toggle section: Custom Links"
msgstr "Cambia sezione: link personalizzati"

#: wp-includes/class-wp-editor.php:1250
msgid "Toolbar Toggle"
msgstr "Mostra/nascondi la barra degli strumenti"

#: wp-includes/script-loader.php:852 wp-includes/script-loader.php:878
#: wp-includes/class-wp-customize-widgets.php:755
msgid "An error has occurred. Please reload the page and try again."
msgstr "Abbiamo riscontrato un errore. Ricarica la pagina e prova di nuovo."

#: wp-includes/class-wp-editor.php:1230
msgid "Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off."
msgstr "Incolla come testo semplice. Il contenuto verr&aacute; incollato come semplice testo finch&eacute; mantieni attiva questa opzione."

#: wp-includes/class-wp-editor.php:1231
msgid "If you&#8217;re looking to paste rich content from Microsoft Word, try turning this option off. The editor will clean up text pasted from Word automatically."
msgstr "Se stai incollando un contenuto da Microsoft Word, prova ad attivare questa opzione. L'editor pulirà automaticamente il testo incollato da Word."

#: wp-login.php:117 wp-includes/admin-bar.php:148
#: wp-includes/widgets/class-wp-widget-meta.php:72
msgid "https://wordpress.org/"
msgstr "https://it.wordpress.org/"

#: wp-includes/class-wp-customize-widgets.php:811
#: wp-includes/class-wp-customize-nav-menus.php:1021
msgid "Clear Results"
msgstr "Cancella risultati"

#: wp-includes/class-wp-editor.php:1111
msgctxt "editor button"
msgid "Right to left"
msgstr "Da destra a sinistra"

#: wp-includes/class-wp-editor.php:1112
msgctxt "editor button"
msgid "Left to right"
msgstr "Da sinistra a destra"

#: wp-includes/class-wp-customize-widgets.php:684
#: wp-includes/class-wp-customize-nav-menus.php:949
msgid "Move down"
msgstr "Muovi giù"

#: wp-includes/class-wp-editor.php:1121
msgid "Horizontal line"
msgstr "Linea orizzontale"

#: wp-includes/class-wp-customize-widgets.php:804
#: wp-includes/customize/class-wp-widget-area-customize-control.php:57
msgid "Add a Widget"
msgstr "Aggiungi un widget"

#: wp-includes/script-loader.php:477 wp-includes/script-loader.php:559
#: wp-includes/script-loader.php:712 wp-includes/script-loader.php:735
#: wp-includes/script-loader.php:899 wp-includes/theme.php:2962
#: wp-includes/class-wp-editor.php:1236
msgid "The changes you made will be lost if you navigate away from this page."
msgstr "Se si abbandona questa pagina i cambiamenti effettuati verranno persi."

#: wp-includes/class-wp-editor.php:1252
msgid "Insert Page Break tag"
msgstr "Inserisci tag di interruzione pagina"

#: wp-includes/class-wp-customize-manager.php:4942
msgid "No logo selected"
msgstr "Nessun logo selezionato"

#: wp-includes/class-wp-customize-manager.php:4944
msgid "Choose logo"
msgstr "Scegli il logo"

#: wp-includes/class-wp-customize-manager.php:4938
#: wp-includes/class-wp-customize-manager.php:4943
msgid "Select logo"
msgstr "Seleziona il logo"

#: wp-includes/class-wp-customize-manager.php:4939
msgid "Change logo"
msgstr "Cambia il logo"

#: wp-comments-post.php:29
msgid "Comment Submission Failure"
msgstr "Inserimento commento fallito."

#: wp-includes/class-wp-customize-manager.php:4930
msgid "Logo"
msgstr "Logo"

#: wp-includes/class-wp-editor.php:1258
msgid "Paste URL or type to search"
msgstr "Incolla l'URL oppure digita per cercare"

#: wp-includes/customize/class-wp-customize-selective-refresh.php:192
#: wp-includes/class-wp-customize-manager.php:2063
msgid "Shift-click to edit this element."
msgstr "Fare clic+shift (maiusc) per modificare questo elemento."

#. translators: 1: panel id, 2: link to 'customize_loaded_components' filter
#. reference
#: wp-includes/class-wp-customize-manager.php:3760
msgid "Removing %1$s manually will cause PHP warnings. Use the %2$s filter instead."
msgstr "Rimuovere manualmente %1$s causerà dei warning PHP . Utilizza in alternativa il filtro %2$s."

#: wp-includes/class-wp-editor.php:1287
msgid "The next group of formatting shortcuts are applied as you type or when you insert them around plain text in the same paragraph. Press Escape or the Undo button to undo."
msgstr "Il prossimo gruppo di scorciatoie di formattazione vengono applicate quando digiti o inserisci attorno ad un testo normale nel medesimo paragrafo. Per annullare premere Esc o il pulsante Annulla."

#: wp-includes/class-wp-editor.php:1260
msgid "Link options"
msgstr "Opzioni link"

#. translators: 1: login URL, 2: username, 3: user email, 4: lost password URL
#: wp-activate.php:146
msgid "Your account has been activated. You may now <a href=\"%1$s\">log in</a> to the site using your chosen username of &#8220;%2$s&#8221;. Please check your email inbox at %3$s for your password and login instructions. If you do not receive an email, please check your junk or spam folder. If you still do not receive an email within an hour, you can <a href=\"%4$s\">reset your password</a>."
msgstr "Il tuo account è stato attivato. Ora puoi <a href=\"%1$s\">accedere</a> al sito utilizzando il nome utente scelto, &#8220;%2$s&#8221;. Per favore controlla la tua casella email %3$s per la password e le istruzioni di accesso. Se non ricevi alcuna email, per favore controlla la cartella junk o spam. Se continui a non ricevere l'email nell'arco di un'ora, puoi <a href=\"%4$s\">reimpostare la password</a>."

#. translators: 1: site URL, 2: login URL
#: wp-activate.php:188
msgid "Your account is now activated. <a href=\"%1$s\">View your site</a> or <a href=\"%2$s\">Log in</a>"
msgstr "Il tuo account è ora attivo. <a href=\"%1$s\">Visualizza il tuo sito</a> o <a href=\"%2$s\">Accedi</a>"

#: wp-includes/class-wp-customize-manager.php:4896
#: wp-includes/class-wp-customize-manager.php:4974
msgid "Display Site Title and Tagline"
msgstr "Mostra il titolo del sito e il motto"

#: wp-includes/class-wp-editor.php:1285
msgid "The following formatting shortcuts are replaced when pressing Enter. Press Escape or the Undo button to undo."
msgstr "Le seguenti scorciatoie per la formattazione sono sostituite quando si preme Invio. Premi «Esc» o il pulsante «Annulla» per annullare."

#. translators: %s: author's display name
#: wp-includes/author-template.php:227
msgid "Visit %s&#8217;s website"
msgstr "Visita il sito web di %s"

#: wp-includes/bookmark-template.php:206
msgid "Bookmarks"
msgstr "Segnalibri"

#: wp-includes/class-wp-customize-manager.php:4790
msgid "Enter desktop preview mode"
msgstr "Abilita l'anteprima in modalità desktop"

#: wp-includes/class-wp-customize-manager.php:4794
msgid "Enter tablet preview mode"
msgstr "Abilita l'anteprima in modalità tablet"

#: wp-includes/class-wp-customize-manager.php:4797
msgid "Enter mobile preview mode"
msgstr "Abilita l'anteprima in modalità mobile"

#: wp-includes/class-wp-customize-nav-menus.php:453
msgid "Loading more results... please wait."
msgstr "Caricamento di ulteriori risultati in corso... attendi."

#: wp-includes/class-wp-customize-widgets.php:685
#: wp-includes/class-wp-customize-nav-menus.php:948
msgid "Move up"
msgstr "Sposta sopra"

#: wp-includes/widgets/class-wp-widget-media-image.php:136
#: wp-includes/media-template.php:1008
#: wp-includes/customize/class-wp-customize-nav-menu-item-control.php:100
#: wp-includes/class-wp-editor.php:1715
msgid "Open link in a new tab"
msgstr "Apri il link in una nuova scheda"

#: wp-includes/class-wp-editor.php:1241
msgctxt "TinyMCE menu"
msgid "Insert"
msgstr "Inserisci"

#: wp-includes/script-loader.php:607
#: wp-includes/class-wp-customize-setting.php:579
#: wp-includes/class-wp-customize-manager.php:2300
msgid "Invalid value."
msgstr "Valore non valido."

#: wp-includes/script-loader.php:567
#: wp-includes/class-wp-customize-manager.php:516
msgid "Sorry, you are not allowed to customize this site."
msgstr "Non hai i permessi per personalizzare questo sito."

#: wp-includes/class-wp-customize-manager.php:571
msgid "Sorry, you are not allowed to edit theme options on this site."
msgstr "Non hai i permessi per modificare le opzioni del tema in questo sito."

#: wp-includes/class-wp-editor.php:1736
msgid "Search or use up and down arrow keys to select an item."
msgstr "Cerca o usa i tasti freccia su e freccia giù per selezionare un elemento."

#: wp-includes/class-wp-editor.php:1233
msgid "Rich Text Area. Press Alt-Shift-H for help."
msgstr "Area testo elaborato. Premi Alt-Shift-H per aiuto."

#: wp-includes/class-wp-editor.php:1234
msgid "Rich Text Area. Press Control-Option-H for help."
msgstr "Area testo elaborato. Premi Control-Option-H per aiuto."

#: wp-includes/class-wp-editor.php:1279
msgid "Warning: the link has been inserted but may have errors. Please test it."
msgstr "Attenzione:  hai inserito il link ma ci potrebbero essere degli errori. Provalo. "

#: wp-includes/class-wp-customize-manager.php:4861
msgid "Site Identity"
msgstr "Denominazione del sito"

#: wp-includes/class-wp-editor.php:1170
msgid "Table row properties"
msgstr "Proprietà della riga della tabella"

#: wp-includes/class-wp-editor.php:1171
msgid "Table cell properties"
msgstr "Proprietà della cella della tabella"

#: wp-includes/class-wp-editor.php:1193
msgid "Copy table row"
msgstr "Copia la riga della tabella"

#: wp-includes/class-wp-editor.php:1180
msgctxt "table header"
msgid "Header"
msgstr "Header"

#: wp-includes/class-wp-editor.php:1179
msgid "Header cell"
msgstr "Cella dell'header "

#: wp-includes/class-wp-customize-manager.php:4981
msgid "Header Text Color"
msgstr "Colore del testo dell'header"

#: wp-includes/customize/class-wp-customize-header-image-control.php:31
#: wp-includes/class-wp-customize-manager.php:5031
msgid "Header Image"
msgstr "Immagine dell'header"

#: wp-includes/admin-bar.php:854
msgid "Header"
msgstr "Header"

#: wp-includes/class-wp-editor.php:1079
msgid "Encoding"
msgstr "Codifica"

#: wp-includes/class-wp-customize-widgets.php:683
msgid "Move to another area&hellip;"
msgstr "Sposta in un'altra area&hellip;"

#: wp-includes/class-wp-editor.php:1105
msgid "Paste your embed code below:"
msgstr "Inserisci il codice di embed qui di seguito:"

#: wp-includes/class-wp-customize-manager.php:5135
#: wp-includes/class-wp-customize-manager.php:5176
msgid "Fill Screen"
msgstr "Riempi lo schermo"

#: wp-includes/class-wp-customize-manager.php:5248
msgid "Homepage"
msgstr "Home page"

#: wp-includes/class-wp-customize-control.php:632
msgid "New page title"
msgstr "Nuovo titolo di pagina"

#. translators: %s: current user's display name
#: wp-includes/admin-bar.php:217
msgid "Howdy, %s"
msgstr "Ciao, %s"

#: wp-includes/class-wp-customize-control.php:633
msgid "New page title&hellip;"
msgstr "Nuovo titolo di pagina&hellip;"

#: wp-includes/class-wp-customize-manager.php:5586
#: wp-includes/class-wp-customize-manager.php:5590
msgid "Invalid value for background size."
msgstr "Valore non valido per la dimensione dello sfondo."

#: wp-includes/class-wp-customize-manager.php:5138
msgctxt "Custom Preset"
msgid "Custom"
msgstr "Personalizzato"

#: wp-includes/class-wp-customize-manager.php:5272
msgid "Learn more about CSS"
msgstr "Per saperne di più sul CSS"

#. translators: %d: number of theme search results, which cannot currently
#. consider singular vs. plural forms
#: wp-includes/class-wp-customize-manager.php:4706
msgid "%d themes found"
msgstr "%d temi trovati"

#: wp-includes/class-wp-customize-manager.php:5305
msgid "Additional CSS"
msgstr "CSS aggiuntivo"

#: wp-includes/class-wp-customize-manager.php:5187
msgid "Repeat Background Image"
msgstr "Ripeti l'immagine di sfondo"

#: wp-includes/class-wp-customize-manager.php:5174
msgid "Original"
msgstr "Originale"

#: wp-includes/class-wp-customize-manager.php:5170
msgid "Image Size"
msgstr "Dimensione dell'immagine"

#: wp-includes/customize/class-wp-customize-background-position-control.php:66
#: wp-includes/class-wp-customize-manager.php:5155
msgid "Image Position"
msgstr "Posizione dell'immagine"

#: wp-includes/class-wp-customize-manager.php:4704
msgid "Are you sure you want to delete this theme?"
msgstr "Desideri eliminare questo tema?"

#: wp-includes/class-wp-customize-widgets.php:766
#: wp-includes/class-wp-customize-widgets.php:820
msgid "No widgets found."
msgstr "Nessun widget trovato."

#: wp-includes/class-wp-customize-manager.php:2272
msgid "Setting does not exist or is unrecognized."
msgstr "L'impostazione non esiste o non è riconosciuta."

#: wp-includes/class-wp-customize-nav-menus.php:808
msgid "Empty title"
msgstr "Titolo vuoto"

#: wp-includes/class-wp-customize-nav-menus.php:897
msgid "Post"
msgstr "Articolo"

#: wp-includes/class-wp-customize-manager.php:5199
msgid "Scroll with Page"
msgstr "Scorri con la pagina"

#: wp-includes/class-wp-customize-manager.php:5137
msgctxt "Repeat Image"
msgid "Repeat"
msgstr "Ripeti"

#: wp-includes/class-wp-customize-manager.php:5136
#: wp-includes/class-wp-customize-manager.php:5175
msgid "Fit to Screen"
msgstr "Adatta allo schermo"

#: wp-includes/class-wp-customize-manager.php:5134
msgctxt "Default Preset"
msgid "Default"
msgstr "Predefinito"

#. translators: %d: number of themes being displayed, which cannot currently
#. consider singular vs. plural forms
#: wp-includes/class-wp-customize-manager.php:4708
msgid "Displaying %d themes"
msgstr "Visualizza %d temi"

#. translators: %s: theme name
#: wp-includes/class-wp-customize-manager.php:4710
msgid "Showing details for theme: %s"
msgstr "Visualizza i dettagli per il tema: %s"

#. translators: %d: the number of widgets found
#: wp-includes/class-wp-customize-widgets.php:765
msgid "Number of widgets found: %d"
msgstr "Numero di widget trovati: %d"

#. translators: %1$s is the post type name and %2$s is the error message.
#: wp-includes/class-wp-customize-nav-menus.php:902
msgid "%1$s could not be created: %2$s"
msgstr "%1$s non può essere creato: %2$s"

#: wp-includes/class-wp-customize-manager.php:5635
msgid "This video file is too large to use as a header video. Try a shorter video or optimize the compression settings and re-upload a file that is less than 8MB. Or, upload your video to YouTube and link it with the option below."
msgstr "Questo file video è troppo grande per essere usato come video dell'Header. Provane uno più corto oppure ottimizza le impostazioni di compressione per renderlo inferiore a 8MB e prova a ricaricarlo. In alternativa puoi caricare il tuo video su YouTube ed inserire il link di YouTube nell'opzione sottostante."

#: wp-includes/class-wp-customize-manager.php:5004
msgid "If you add a video, the image will be used as a fallback while the video loads."
msgstr "Se aggiungi un video, l'immagine sarà usata come alternativa mentre il video è in fase di caricamento."

#: wp-includes/class-wp-customize-manager.php:5574
msgid "Invalid value for background attachment."
msgstr "Valore non valido per l'allegato per lo sfondo."

#: wp-includes/class-wp-customize-manager.php:5578
msgid "Invalid value for background position X."
msgstr "Valore non valido per la posizione X dello sfondo."

#: wp-includes/class-wp-customize-manager.php:5130
msgctxt "Background Preset"
msgid "Preset"
msgstr "Impostazioni predefinite"

#: wp-includes/atomlib.php:151 wp-includes/feed.php:542
#: wp-includes/IXR/class-IXR-message.php:48
msgid "PHP's XML extension is not available. Please contact your hosting provider to enable PHP's XML extension."
msgstr "L'estensione PHP per XML non è disponibile. Contatta il tuo fornitore di hosting per abilitare l'estensione PHP per XML."

#: wp-includes/class-wp-customize-manager.php:5582
msgid "Invalid value for background position Y."
msgstr "Valore non valido per la posizione Y dello sfondo."

#: wp-includes/class-wp-customize-manager.php:5570
msgid "Invalid value for background repeat."
msgstr "Valore non valido per la ripetizione dello sfondo."

#: wp-includes/class-wp-customize-manager.php:5088
msgid "Or, enter a YouTube URL:"
msgstr "Oppure inserisci un URL di YouTube:"

#: wp-includes/class-wp-customize-manager.php:5665
msgid "Please enter a valid YouTube URL."
msgstr "Inserisci un URL valido di YouTube"

#: wp-includes/class-wp-customize-manager.php:1849
msgid "Unauthorized. You may remove the customize_messenger_channel param to preview as frontend."
msgstr "Non autorizzato. Puoi rimuovere il parametro customize_messenger_channel per visualizzare un'anteprima come frontend."

#: wp-includes/class-wp-customize-manager.php:2065
msgid "This form is not live-previewable."
msgstr "Questo modulo non è visualizzabile in anteprima live."

#: wp-includes/class-wp-customize-manager.php:2064
msgid "This link is not live-previewable."
msgstr "Questo link non è visualizzabile in anteprima live."

#: wp-includes/class-wp-customize-manager.php:552
msgid "Non-existent changeset UUID."
msgstr "UUID del changeset non esistente."

#: wp-includes/class-wp-customize-manager.php:5595
msgid "Unrecognized background setting."
msgstr "Impostazione di sfondo non riconosciuta."

#: wp-includes/class-wp-customize-manager.php:2277
msgid "Unauthorized to modify setting due to capability."
msgstr "Non autorizzato a modificare le impostazioni a causa delle capacità."

#: wp-includes/class-wp-customize-manager.php:525
msgid "Invalid changeset UUID"
msgstr "UUID del changeset non valido."

#: wp-includes/class-wp-customize-nav-menus.php:811
msgid "Status is forbidden"
msgstr "Stato vietato"

#: wp-includes/class-wp-editor.php:1190
msgid "Delete row"
msgstr "Elimina riga"

#: wp-includes/class-wp-editor.php:1168
msgid "Delete table"
msgstr "Elimina tabella"

#: wp-includes/class-wp-editor.php:1191
msgid "Delete column"
msgstr "Elimina colonna"

#: wp-includes/class-wp-customize-nav-menus.php:960
msgid "Delete Menu"
msgstr "Elimina menu"

#: wp-includes/class-wp-customize-nav-menus.php:438
msgid "Menu item deleted"
msgstr "Voce menu eliminata"

#: wp-includes/class-wp-customize-nav-menus.php:440
msgid "Menu deleted"
msgstr "Menu eliminato"

#. translators: %s: add new page label
#: wp-includes/class-wp-customize-control.php:628
msgid "+ %s"
msgstr "+ %s"

#: wp-includes/class-wp-customize-manager.php:5003
msgid "Header Media"
msgstr "Media dell'header"

#. translators: 1: .mp4, 2: .mov
#: wp-includes/class-wp-customize-manager.php:5641
msgid "Only %1$s or %2$s files may be used for header video. Please convert your video file and try again, or, upload your video to YouTube and link it with the option below."
msgstr "Per il video dell'Header puoi usare solo file %1$s or %2$s files. Converti il tuo file video e riprova oppure carica il tuo video su YouTube ed inserisci il link nell'opzione sottostante."

#. translators: 1: .mp4, 2: header height in pixels
#: wp-includes/class-wp-customize-manager.php:5025
msgid "Upload your video in %1$s format and minimize its file size for best results. Your theme recommends a height of %2$s pixels."
msgstr "Carica il tuo video in formato %1$s e minimizza la dimensione del file per ottenere un risultato migliore. Il tuo tema consiglia di usare un'altezza di %2$s pixel."

#. translators: 1: .mp4, 2: header width in pixels
#: wp-includes/class-wp-customize-manager.php:5018
msgid "Upload your video in %1$s format and minimize its file size for best results. Your theme recommends a width of %2$s pixels."
msgstr "Carica il tuo video in formato %1$s e minimizza la dimensione del file per ottenere un risultato migliore. Il tuo tema consiglia di usare un larghezza di %2$s pixel."

#: wp-includes/customize/class-wp-customize-nav-menu-item-setting.php:320
#: wp-includes/class-wp-customize-nav-menus.php:165
#: wp-includes/nav-menu.php:799
msgid "Post Type Archive"
msgstr "Archivio dei tipi di contenuto (Post Type)"

#. translators: 1: post type, 2: capability name
#: wp-includes/capabilities.php:83 wp-includes/capabilities.php:153
#: wp-includes/capabilities.php:221 wp-includes/capabilities.php:258
msgid "The post type %1$s is not registered, so it may not be reliable to check the capability \"%2$s\" against a post of that type."
msgstr "Il tipo di contenuto (post type) %1$s non è registrato, quindi non è affidabile verificare la capacità \"%2$s\" con questo tipo di contenuto."

#: wp-includes/class-wp-xmlrpc-server.php:1377
#: wp-includes/class-wp-xmlrpc-server.php:1861
#: wp-includes/class-wp-xmlrpc-server.php:3381
#: wp-includes/class-wp-xmlrpc-server.php:4196
#: wp-includes/class-wp-xmlrpc-server.php:4959
#: wp-includes/class-wp-xmlrpc-server.php:5017
#: wp-includes/class-wp-xmlrpc-server.php:5295
#: wp-includes/class-wp-xmlrpc-server.php:5359
#: wp-includes/rest-api/endpoints/class-wp-rest-post-types-controller.php:123
#: wp-includes/post.php:1234 wp-includes/class-wp-customize-nav-menus.php:805
msgid "Invalid post type."
msgstr "Tipo di contenuto (post type) non valido."

#: wp-includes/class-wp-customize-manager.php:5271
msgid "https://codex.wordpress.org/CSS"
msgstr "https://codex.wordpress.org/CSS"

#: wp-activate.php:140 wp-activate.php:174
msgid "Your account is now active!"
msgstr "Ora il tuo account è attivo!"

#: wp-includes/admin-bar.php:337 wp-includes/admin-bar.php:567
#: wp-includes/deprecated.php:2798
msgid "Visit Site"
msgstr "Visita il sito"

#: wp-includes/admin-bar.php:286 wp-includes/class-wp-admin-bar.php:423
msgid "Log Out"
msgstr "Esci"

#: wp-includes/class-wp-editor.php:1100
msgid "Language"
msgstr "Lingua"

#. translators: accessibility text
#: wp-includes/widgets/class-wp-widget-custom-html.php:304
#: wp-includes/customize/class-wp-customize-nav-menu-locations-control.php:56
#: wp-includes/class-wp-customize-manager.php:4199
#: wp-includes/class-wp-customize-manager.php:5274
#: wp-includes/class-wp-customize-manager.php:5294
msgid "(opens in a new window)"
msgstr "(si apre in una nuova finestra)"

#: wp-includes/class-wp-editor.php:1136
msgid "The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?"
msgstr "L'URL che hai inserito sembra un indirizzo email, vuoi aggiungere il necessario prefisso mailto:?"

#: wp-includes/class-wp-editor.php:1138
msgid "The URL you entered seems to be an external link. Do you want to add the required http:// prefix?"
msgstr "L'URL che hai inserito sembra un link esterno, vuoi aggiungere il necessario prefisso http://?"

#: wp-includes/class-wp-editor.php:1146
msgctxt "Short for blue in RGB"
msgid "B"
msgstr "B"

#: wp-includes/class-wp-editor.php:1145
msgctxt "Short for green in RGB"
msgid "G"
msgstr "G"

#: wp-includes/class-wp-editor.php:1096
msgid "Date/time"
msgstr "Data/ora"

#: wp-includes/class-wp-editor.php:1072
msgctxt "Id for link anchor (TinyMCE)"
msgid "Id"
msgstr "Id"

#: wp-includes/class-wp-editor.php:1132
msgid "New window"
msgstr "Nuova finestra"

#: wp-includes/class-wp-editor.php:1133
msgid "Text to display"
msgstr "Testo da visualizzare"

#: wp-includes/class-wp-editor.php:1144
msgctxt "Short for red in RGB"
msgid "R"
msgstr "R"

#: wp-includes/class-wp-editor.php:1102
msgid "Insert/edit media"
msgstr "Aggiungi/modifica media"

#: wp-includes/class-wp-editor.php:1098
msgid "Table of Contents"
msgstr "Indice dei contenuti"

#. translators: %s: the total number of widget areas registered
#: wp-includes/class-wp-customize-widgets.php:737
msgid "Your theme has %s widget area, but this particular page doesn&#8217;t display it."
msgid_plural "Your theme has %s widget areas, but this particular page doesn&#8217;t display them."
msgstr[0] "Il tuo tema ha %s area widget, ma questa specifica pagina non la visualizza."
msgstr[1] "Il tuo tema ha %s aree widget, ma questa specifica pagina non le visualizza."

#: wp-includes/class-wp-editor.php:1099
msgid "Insert/edit code sample"
msgstr "Inserisci/modifica il codice d'esempio"

#: wp-includes/class-wp-customize-widgets.php:732
msgid "Your theme has 1 widget area, but this particular page doesn&#8217;t display it."
msgstr "Il tuo tema ha 1 area widget, ma questa particolare pagina non la visualizza."

#: wp-includes/class-wp-customize-widgets.php:713
msgid "Your theme has 1 other widget area, but this particular page doesn&#8217;t display it."
msgstr "Il tuo tema ha un'altra area widget, ma questa particolare pagina non la visualizza."

#: wp-includes/class-wp-editor.php:1071
msgid "Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores."
msgstr "L'ID deve iniziare con una lettera, seguita solo da lettere, numeri, trattini, punti, virgole o sottolineatura."

#. translators: %s: the number of other widget areas registered but not
#. rendered
#: wp-includes/class-wp-customize-widgets.php:721
msgid "Your theme has %s other widget area, but this particular page doesn&#8217;t display it."
msgid_plural "Your theme has %s other widget areas, but this particular page doesn&#8217;t display them."
msgstr[0] "Il tuo tema ha ancora %s area widget, ma questa particolare pagina non la visualizza."
msgstr[1] "Il tuo tema ha altre %s aree widget, ma questa particolare pagina non le visualizza."

#: wp-includes/category-template.php:801 wp-includes/category-template.php:810
msgid "%s item"
msgid_plural "%s items"
msgstr[0] "%s elemento "
msgstr[1] "%s elementi"

#. translators: 1: .mp4, 2: header size in pixels
#: wp-includes/class-wp-customize-manager.php:5011
msgid "Upload your video in %1$s format and minimize its file size for best results. Your theme recommends dimensions of %2$s pixels."
msgstr "Carica il video nel formato %1$s  e minimizza la dimensione del file per ottenere il miglior risultato. Il tema consiglia una dimensione di %2$s pixel."

#: wp-includes/class-wp-customize-manager.php:5078
msgid "Header Video"
msgstr "Video dell'header"

#: wp-includes/class-wp-customize-widgets.php:758
msgid "You can navigate to other pages on your site while using the Customizer to view and edit the widgets displayed on those pages."
msgstr "Puoi navigare nelle altre pagine del tuo sito mentre usi Personalizza per vedere e modificare i widget visualizzati in queste pagine."

#: wp-includes/class-wp-customize-nav-menus.php:984
msgid "Create New Menu"
msgstr "Crea un nuovo menu"

#: wp-includes/class-wp-customize-nav-menus.php:709
msgid "New Menu"
msgstr "Nuovo menu"

#: wp-includes/class-wp-customize-manager.php:4196
msgid "Preview Link"
msgstr "Link anteprima "

#: wp-includes/class-wp-customize-manager.php:4191
msgid "Share Preview Link"
msgstr "Condividi link anteprima "

#: wp-includes/class-wp-customize-manager.php:5318
msgid "CSS code"
msgstr "Codice CSS"

#: wp-includes/class-wp-customize-manager.php:5220
msgid "Homepage Settings"
msgstr "Impostazioni Homepage"

#: wp-includes/class-wp-customize-manager.php:4835
msgid "Installed themes"
msgstr "Temi installati"

#: wp-includes/class-wp-customize-manager.php:5268
msgid "Add your own CSS code here to customize the appearance and layout of your site."
msgstr "Aggiungi qui il tuo codice CSS per personalizzare l'aspetto ed il layout del tuo sito."

#: wp-includes/class-wp-customize-nav-menus.php:436
msgid "If your theme has multiple menus, giving them clear names will help you manage them."
msgstr "Se il tuo tema ha più di un menu, assegna ad ognuno dei nomi chiari per poterli gestire più facilmente."

#. translators: %s: site icon size in pixels
#: wp-includes/class-wp-customize-manager.php:4914
msgid "Site Icons should be square and at least %s pixels."
msgstr "L'icona del sito dovrebbe essere quadrata e con una dimensione di almeno %s pixel per lato."

#: wp-includes/class-wp-customize-manager.php:3027
msgid "Changes have already been trashed."
msgstr "Le modifiche sono già state cancellate."

#: wp-includes/class-wp-customize-manager.php:3042
msgid "Changes trashed successfully."
msgstr "Le modifiche sono state cancellate"

#: wp-includes/class-wp-customize-manager.php:3004
msgid "There was an authentication problem. Please reload and try again."
msgstr "C'e un problema di autenticazione. Ricarica e prova ancora."

#: wp-includes/class-wp-customize-nav-menus.php:433
msgid "Menu Location"
msgstr "Posizione menu"

#: wp-includes/class-wp-customize-manager.php:4844
msgid "WordPress.org themes"
msgstr "Temi WordPress.org"

#: wp-includes/class-wp-customize-manager.php:4202
msgid "Copied"
msgstr "Copiato"

#: wp-includes/admin-bar.php:705
msgid "Edit User"
msgstr "Modifica utente"

#: wp-includes/admin-bar.php:668
msgid "View User"
msgstr "Visualizza utente"

#: wp-includes/class-wp-customize-nav-menus.php:604
msgctxt "menu locations"
msgid "View All Locations"
msgstr "Vedi tutte le posizioni"

#: wp-includes/class-wp-customize-manager.php:3021
#: wp-includes/class-wp-customize-manager.php:3037
msgid "Unable to trash changes."
msgstr "Non è possibile cancellare le modifiche."

#: wp-includes/class-wp-customize-manager.php:3012
msgid "No changes saved yet, so there is nothing to trash."
msgstr "Ancora nessuna modifica salvata, non c'è niente da cancellare."

#. translators: %s: number of invalid settings
#: wp-includes/script-loader.php:594 wp-includes/script-loader.php:596
#: wp-includes/class-wp-customize-manager.php:2707
msgid "Unable to save due to %s invalid setting."
msgid_plural "Unable to save due to %s invalid settings."
msgstr[0] "Impossibile salvare, %s impostazione non valida."
msgstr[1] "Impossibile salvare, %s impostazioni non valide."

#: wp-includes/widgets/class-wp-widget-custom-html.php:312
#: wp-includes/class-wp-customize-manager.php:5281
msgid "To move away from this area, press the Esc key followed by the Tab key."
msgstr "Per uscire da questa area, premere il tasto Esc seguito dal tasto Tab."

#: wp-includes/class-wp-customize-nav-menus.php:416
msgid "Your theme can display menus in one location."
msgstr "Il tuo tema può visualizzare i menu in una posizione."

#: wp-includes/class-wp-customize-nav-menus.php:604
msgctxt "menu locations"
msgid "View Location"
msgstr "Vedi posizione"

#. translators: %s: number of menu locations
#: wp-includes/class-wp-customize-nav-menus.php:419
msgid "Your theme can display menus in %s location."
msgid_plural "Your theme can display menus in %s locations."
msgstr[0] "Il tuo tema può visualizzare i menu in %s posizione."
msgstr[1] "Il tuo tema può visualizzare i menu in %s posizioni."

#: wp-includes/class-wp-customize-manager.php:5233
msgid "Your homepage displays"
msgstr "La tua homepage mostra"

#: wp-includes/widgets/class-wp-widget-custom-html.php:311
#: wp-includes/class-wp-customize-manager.php:5280
msgid "In the editing area, the Tab key enters a tab character."
msgstr "Nell'area di modifica il tasto Tab inserisce un carattere di tabulazione."

#: wp-includes/class-wp-customize-manager.php:4827
msgid "Looking for a theme? You can search or browse the WordPress.org theme directory, install and preview themes, then activate them right here."
msgstr "Stai cercando un tema? Da qui puoi consultare la directory dei temi di WordPress.org, visualizzare le anteprime e attivare direttamente il tema che preferisci."

#: wp-includes/script-loader.php:558
#: wp-includes/class-wp-customize-manager.php:2593
#: wp-includes/class-wp-customize-manager.php:2608
msgid "You must supply a future date to schedule."
msgstr "Inserisci una data futura per poter programmare."

#: wp-includes/class-wp-editor.php:182 wp-includes/class-wp-editor.php:1261
msgctxt "Name for the Visual editor tab"
msgid "Visual"
msgstr "Visuale"

#: wp-includes/class-wp-customize-manager.php:2456
msgid "Changeset is being edited by other user."
msgstr "Changeset è in modifica da un altro utente."

#: wp-includes/class-wp-customize-manager.php:3212
msgid "Security check failed."
msgstr "Controllo di sicurezza fallito."

#: wp-includes/class-wp-customize-manager.php:3221
msgid "No changeset found to take over"
msgstr "Nessun changeset trovato da controllare"

#: wp-includes/class-wp-customize-manager.php:3228
msgid "Sorry, you are not allowed to take over."
msgstr "Non hai il permesso di prenderne il controllo."

#. translators: %s: User who is customizing the changeset in customizer.
#: wp-includes/class-wp-customize-manager.php:3977
msgid "%s is already customizing this site. Do you want to take over?"
msgstr "%s sta attualmente modificando questo sito. Vuoi prenderne il controllo?"

#: wp-includes/class-wp-customize-manager.php:4158
msgid "Take over"
msgstr "Prendi il controllo"

#: wp-includes/class-wp-customize-manager.php:4828
msgid "While previewing a new theme, you can continue to tailor things like widgets and menus, and explore theme-specific options."
msgstr "Durante l'anteprima di un nuovo tema, è possibile continuare a personalizzare oggetti come i widget e i menu e esplorare le opzioni specifiche del tema."

#. translators: %s: User who is customizing the changeset in customizer.
#: wp-includes/class-wp-customize-manager.php:3975
msgid "%s is already customizing this site. Please wait until they are done to try customizing. Your latest changes have been autosaved."
msgstr "%s sta attualmente modificando questo sito. Attendi fino a che ha terminato, prima di riprovare a modificare. Le tue ultime modifiche sono state salvate in automatico."

#: wp-includes/class-wp-customize-nav-menus.php:977
msgid "It doesn&#8217;t look like your site has any menus yet. Want to build one? Click the button to start."
msgstr "Sembra che il tuo sito non abbia ancora un menu. Vuoi crearne uno? Fai clic sul pulsante per iniziare."

#: wp-includes/class-wp-customize-nav-menus.php:980
msgid "You&#8217;ll create a menu, assign it a location, and add menu items like links to pages and categories. If your theme has multiple menu areas, you might need to create more than one."
msgstr "Creerai un menu, assegnandolo ad una posizione ed aggiungendo elementi del menu quali link a pagine o categorie. Se il tuo tema ha più aree di menu, dovrai crearne più di uno."

#: wp-includes/class-wp-customize-manager.php:5282
msgid "Screen reader users: when in forms mode, you may need to press the escape key twice."
msgstr "Utenti screen reader: in modalità modulo, potrebbe essere necessario utilizzare il tasto escape due volte."

#: wp-includes/class-wp-customize-manager.php:4912
msgid "Site Icons are what you see in browser tabs, bookmark bars, and within the WordPress mobile apps. Upload one here!"
msgstr "Le icone del sito sono quelle che vedete nelle schede del browser, nella barra dei preferiti, e all'interno delle app mobile di WordPress, Caricane una qui!"

#: wp-includes/class-wp-customize-manager.php:2570
msgid "The previous set of changes has already been published. Please try saving your current set of changes again."
msgstr "Le modifiche precedenti sono state pubblicate. Prova a salvare ancore le modifiche attuali."

#. translators: URL to the widgets panel of the customizer
#: wp-includes/class-wp-customize-nav-menus.php:576
msgid "Menus can be displayed in locations defined by your theme or in <a href=\"%s\">widget areas</a> by adding a &#8220;Navigation Menu&#8221; widget."
msgstr "I Menu possono venir visualizzati in differenti posizioni definite dal tema o in <a href=\"%s\">aree widget</a> aggiungendo un widget &#8220;Menu di navigazione&#8221;."

#. translators: URL to the widgets panel of the customizer
#: wp-includes/class-wp-customize-nav-menus.php:600
msgid "If your theme has widget areas, you can also add menus there. Visit the <a href=\"%s\">Widgets panel</a> and add a &#8220;Navigation Menu widget&#8221; to display a menu in a sidebar or footer."
msgstr "Se il tuo tema ha delle aree per i widget, puoi aggiungere lì un menu. Vai alla voce <a href=\"%s\">Widget</a> e aggiungi un &#8220;Menu di navigazione&#8220;  nelle aree widget della sidebar o del footer."

#. translators: 1: link to user profile, 2: additional link attributes, 3:
#. accessibility text
#: wp-includes/widgets/class-wp-widget-custom-html.php:299
#: wp-includes/class-wp-customize-manager.php:5289
msgid "The edit field automatically highlights code syntax. You can disable this in your <a href=\"%1$s\" %2$s>user profile%3$s</a> to work in plain text mode."
msgstr "Il campo di modifica evidenzia automaticamente la sintassi del codice. Puoi disabilitare questa funzione nel tuo <a href=\"%1$s\" %2$s>profilo utente%3$s</a> per lavorare in modalità solo testo."

#: wp-includes/class-wp-customize-manager.php:5222
msgid "You can choose what&#8217;s displayed on the homepage of your site. It can be posts in reverse chronological order (classic blog), or a fixed/static page. To set a static homepage, you first need to create two Pages. One will become the homepage, and the other will be where your posts are displayed."
msgstr "Puoi scegliere cosa visualizzare nella homepage del tuo sito. Possono essere articoli in ordine cronologico inverso (il classico blog) oppure una pagina fissa/statica. Per impostare una homepage statica, devi prima creare due Pagine. Una diverrà la homepage e l'altra sarà quella dove appariranno i tuoi articoli."

#: wp-includes/script-loader.php:554
#: wp-includes/class-wp-customize-manager.php:4627
msgctxt "customizer changeset action/button label"
msgid "Schedule"
msgstr "Pianifica"

#. translators: 1: fopen() 2: file name
#: wp-includes/class-wp-http-streams.php:229
#: wp-includes/class-wp-http-curl.php:185
msgid "Could not open handle for %1$s to %2$s."
msgstr "Non è possibile aprire un handle tra %1$s e %2$s."

#: wp-includes/class-wp-customize-manager.php:4172
msgid "Update anyway, even though it might break your site?"
msgstr "Aggiornare comunque, anche se potrebbe bloccare il tuo sito?"

#: wp-includes/widgets/class-wp-widget-custom-html.php:309
#: wp-includes/class-wp-customize-manager.php:5278
msgid "When using a keyboard to navigate:"
msgstr "Quando si utilizza la tastiera per navigare:"

#: wp-includes/class-wp-customize-nav-menus.php:966
msgid "Click &#8220;Next&#8221; to start adding links to your new menu."
msgstr "Fai clic su &#8220;Avanti&#8221; per cominciare ad aggiungere i link al tuo nuovo menu."

#: wp-includes/class-wp-customize-nav-menus.php:592
msgid "Your theme can display menus in one location. Select which menu you would like to use."
msgstr "Il tuo tema può visualizzare i menu in una sola posizione. Seleziona quale menu vuoi utilizzare."

#: wp-includes/script-loader.php:120 wp-includes/class-wp-editor.php:1251
msgid "Insert Read More tag"
msgstr "Inserisci il tag Leggi tutto"

#. translators: %s: User who is customizing the changeset in customizer.
#: wp-includes/class-wp-customize-manager.php:3970
msgid "%s is already customizing this changeset. Do you want to take over?"
msgstr "%s sta personalizzando questo changeset. Vuoi prenderne il controllo?"

#. translators: %s: User who is customizing the changeset in customizer.
#: wp-includes/class-wp-customize-manager.php:3968
msgid "%s is already customizing this changeset. Please wait until they are done to try customizing. Your latest changes have been autosaved."
msgstr "%s sta personalizzando questo changeset. Aspetta fino a che non ha finito per fare le tue personalizzazioni. Le tue ultime modifiche sono state salvate in automatico."

#: wp-includes/script-loader.php:566
#: wp-includes/class-wp-customize-manager.php:515
msgid "You need a higher level of permission."
msgstr "Hai bisogno di autorizzazioni più elevate."

#: wp-includes/script-loader.php:133 wp-includes/script-loader.php:565
#: wp-includes/script-loader.php:645 wp-includes/script-loader.php:850
#: wp-includes/class-wp-xmlrpc-server.php:3689 wp-includes/functions.php:2666
#: wp-includes/class-wp-customize-manager.php:450
msgid "Something went wrong."
msgstr "Qualcosa è andato storto."

#: wp-login.php:921 wp-includes/admin-bar.php:164 wp-includes/wp-db.php:1062
#: wp-includes/wp-db.php:1595 wp-includes/wp-db.php:1753
#: wp-includes/update.php:153 wp-includes/update.php:345
#: wp-includes/update.php:527
msgid "https://wordpress.org/support/"
msgstr "https://it.wordpress.org/support/"

#: wp-includes/admin-bar.php:172
msgid "https://wordpress.org/support/forum/requests-and-feedback"
msgstr "https://it.wordpress.org/support/forums/"

#: wp-includes/class-wp-customize-manager.php:4193
msgid "See how changes would look live on your website, and share the preview with people who can't access the Customizer."
msgstr "Guarda dal vivo come appariranno le modifiche sul tuo tuo sito web e condividi l'anteprima con persone che non hanno accesso allo strumento Personalizza."

#: wp-includes/class-wp-customize-manager.php:4154
msgid "Go back"
msgstr "Torna indietro"

#. translators: %s: site name
#: wp-includes/admin-bar.php:316
msgid "Network Admin: %s"
msgstr "Amministratore del network %s"

#. translators: 1: comment date, 2: comment time
#: wp-includes/class-walker-comment.php:282
#: wp-includes/class-walker-comment.php:336
msgid "%1$s at %2$s"
msgstr "%1$s @ %2$s"

#: wp-includes/admin-bar.php:171
msgid "Feedback"
msgstr "Feedback"

#: wp-includes/class-wp-editor.php:1182
msgctxt "table footer"
msgid "Footer"
msgstr "Footer"