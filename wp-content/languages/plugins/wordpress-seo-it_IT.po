# Translation of Plugins - Yoast SEO - Stable (latest release) in Italian
# This file is distributed under the same license as the Plugins - Yoast SEO - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-04-28 19:05:34+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: it\n"
"Project-Id-Version: Plugins - Yoast SEO - Stable (latest release)\n"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:55
msgid "Personal info"
msgstr "Unformazioni personali"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:51
msgid "Organization logo"
msgstr "Logo dell'Organizzazione"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:50
msgid "Organization name"
msgstr "Nome dell'Organizzazione"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:41
msgid "Choose whether you're an organization or a person"
msgstr "Scegli se essere un'Organizzazione o una Persona"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:37
msgid "Choose whether the site represents an organization or a person."
msgstr "Scegli se il sito rappresenta un'Organizzazione o una Persona."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:23
msgid "Knowledge Graph & Schema.org"
msgstr "Knowledge Graph e Schema.org"

#: admin/views/tabs/social/accounts.php:84
msgid "Organization social profiles"
msgstr "Profili dei social network dell'Organizzazione"

#: admin/views/tabs/social/accounts.php:74
msgid "To make your site represent a Company or Organization go to %1$sSearch Appearance%2$s and set Organization or Person to \"Organization\"."
msgstr "Per impostare se il sito rappresenta una Organizzazione o una Peronsa vai a %1$sAspetto della ricerca%2$s e imposta Organizzazione o Persona nel campo \"Organizzazione\"."

#: admin/views/tabs/social/accounts.php:72
msgid "To change the social accounts used for your site, update the details for %1$s."
msgstr "Per cambiare l'account dei social network usati per il sito, aggiorna i dettagli in %1$s."

#: admin/views/tabs/social/accounts.php:67
msgid "That means that the form and information below is disabled, and not used."
msgstr "Ciò significa che il modulo e le informazioni sotto sono disattivate e non usate."

#: admin/views/tabs/social/accounts.php:65
msgid "Your website is currently configured to represent a Person"
msgstr "Il tuo sito web e attualmente configurato per rappresentare una Persona"

#: admin/config-ui/class-configuration-structure.php:69
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:45
msgid "Organization or person"
msgstr "Organizzazione o Persona"

#: admin/config-ui/fields/class-field-company-or-person.php:23
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:42
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:48
msgid "Organization"
msgstr "Organizzazione"

#: admin/config-ui/fields/class-field-company-or-person.php:19
msgid "Does your site represent a person or an organization?"
msgstr "Il tuo sito rappresenta un'Organizzazione o una Persona?"

#: admin/config-ui/fields/class-field-company-logo.php:19
msgid "Provide an image of the organization logo"
msgstr "Fornisce un'immagine del logo dell'Organizzazione"

#: admin/config-ui/fields/class-field-company-name.php:19
msgid "The name of the organization"
msgstr "Il nome dell'Organizzazione"

#: admin/config-ui/fields/class-field-person.php:19
msgid "The person"
msgstr "La Persona"

#. translators: %1$s is a link start tag to the Configuration Wizard, %2$s is
#. the link closing tag.
#: admin/class-schema-person-upgrade-notification.php:59
msgid "You have previously set your site to represent a person. We’ve improved our functionality around Schema and the Knowledge Graph, so you should go in and %1$scomplete those settings%2$s."
msgstr "In precedenza hai impostato il sito per rappresentare una persona. Abbiamo migliorato le nostre funzionalità riguardanti i dati Schema e il Knowledge Graph, per questo dovresti andare a %1$scompletare queste impostazioni%2$s."

#: admin/class-admin.php:275
msgid "(if one exists)"
msgstr "(se uno esiste)"

#: admin/class-admin.php:275
msgid "Wikipedia page about you"
msgstr "La tua pagina Wikipedia"

#: admin/class-admin.php:274
msgid "YouTube profile URL"
msgstr "URL del profilo YouTube"

#: admin/class-admin.php:272
msgid "Tumblr profile URL"
msgstr "URL del profiloTumblr"

#: admin/class-admin.php:271
msgid "SoundCloud profile URL"
msgstr "URL del profilo SoundCloud"

#: admin/class-admin.php:269
msgid "MySpace profile URL"
msgstr "URL del profilo MySpace"

#: frontend/schema/class-schema-article.php:157
msgid "Uncategorized"
msgstr "Senza categoria"

#: languages/wordpress-seojs.php:57
msgid "You have selected the user %1$s as the person this site represents. Their user profile information will now be used in search results. %2$sUpdate their profile to make sure the information is correct.%3$s"
msgstr "Hai selezionato l'utente %1$s come la Persona che il sito rappresenta. Le sue informazioni personali saranno ora usate nei risultati di ricerca.  %2$sAggiornane il profilo per assicurarti che siano corrette .%3$s"

#: languages/wordpress-seojs.php:54
msgid "Error: Please select a user below to make your site's meta data complete."
msgstr "Errore: seleziona un utente sotto per completare i dati meta del sito."

#: languages/wordpress-seojs.php:51
msgid "Name:"
msgstr "Nome:"

#: languages/wordpress-seojs.php:48
msgid "You can edit the details shown in meta data, like the social profiles, the name and the description of this user on their %1$s profile page."
msgstr "Puoi modificare i dettagli mostrati nei meta dati, come i profili dei social network, il nome e la descrizione di questo utente o la sua pagina del profilo %1$s."

#: languages/wordpress-seojs.php:42
msgid "Select a user..."
msgstr "Seleziona un utente..."

#. translators: %s expands to the SEO score.
#: inc/class-wpseo-admin-bar-menu.php:606
msgid "SEO score: %s"
msgstr "Punteggio SEO: %s"

#: admin/views/tabs/social/accounts.php:20
msgid "If a Wikipedia page for you or your organization exists, add it too."
msgstr "Se hai una pagina Wikipedia a tuo nome della tua organizzazione, aggiungila. "

#: admin/class-admin.php:270
msgid "Pinterest profile URL"
msgstr "URL del profilo di Pinterest "

#: admin/class-admin.php:268
msgid "LinkedIn profile URL"
msgstr "URL del profilo LinkedIn "

#: admin/class-admin.php:267
msgid "Instagram profile URL"
msgstr "URL del profilo Instagram "

#: admin/config-ui/fields/class-field-profile-url-wikipedia.php:19
#: admin/views/tabs/social/accounts.php:57
msgid "Wikipedia URL"
msgstr "URL di Wikipedia "

#: admin/views/sidebar.php:52
msgid "SEO for Beginners training"
msgstr "SEO for Beginners training"

#: admin/views/sidebar.php:77
msgid "All-around SEO training"
msgstr "All-around SEO training"

#. translators: %s expands to Yoast SEO
#: admin/views/sidebar.php:65
msgid "%s for WordPress training"
msgstr "%s for WordPress training"

#: inc/class-my-yoast-api-request.php:187
msgid "No JSON object was returned."
msgstr "Non è stato restituito nessun oggetto JSON. "

#: languages/yoast-components.php:232
msgid "The image you selected is too small for Facebook"
msgstr "L'immagine che hai selezionato è troppo piccola per Facebook "

#. translators: %1$s resolves to Keyword research training
#: admin/config-ui/components/class-component-suggestions.php:57
msgid "Keyword research is essential in any SEO strategy. You decide the search terms you want to be found for, and figure out what words your audience uses to find you. Great keyword research tells you what content you need to start ranking for the terms you want to rank for. Make sure your efforts go into the keywords you actually have a chance at ranking for! The %1$s walks you through this process, step by step."
msgstr "La ricerca per parole chiave è essenziale in qualunque strategia SEO. Devi decidere per quali termini di ricerca vuoi essere trovato ed immaginare quali parole può utilizzare il tuo pubblico per trovarti. Una buona scelta delle parole chiave ti dice quali contenuti servono per iniziare a salire le classifiche per i termini di tuo interesse. Concentra i tuoi sforzi sulle parole chiave che hanno qualche effettiva possibilità di finire in cima alla lista! Il corso %1$s ti guida passo passo in questo processo."

#: languages/yoast-components.php:229
msgid "The given image url cannot be loaded"
msgstr "L'URL dell'immagine inserito non può essere caricato"

#: languages/yoast-seo-js.php:473
msgid "Content optimization: Needs improvement"
msgstr "Ottimizzazione del contenuto: da migliorare "

#: admin/config-ui/components/class-component-suggestions.php:54
msgid "Find out what words your audience uses to find you"
msgstr "Scopri quali parole utilizza il tuo pubblico per trovarti"

#: admin/links/class-link-columns.php:193
msgid "Received internal links"
msgstr "Link interni ricevuti"

#: admin/links/class-link-columns.php:186
msgid "Outgoing internal links"
msgstr "Link interni in uscita"

#: languages/wordpress-seojs.php:200
msgid "New step added"
msgstr "È stato aggiunto un nuovo passaggio"

#: languages/wordpress-seojs.php:163
msgid "New question added"
msgstr "È stata aggiunta una nuova domanda"

#. Translators: %1$s expands to a link on yoast.com, %2$s to the anchor end
#. tag, %3$s expands to the numeric Flesch reading ease score, %4$s to the
#. easiness of reading, %5$s expands to a call to action based on the score
#: languages/yoast-seo-js.php:41
msgid "%1$sFlesch Reading Ease%2$s: The copy scores %3$s in the test, which is considered %4$s to read. %5$s"
msgstr "%1$sFlesch Reading Ease%2$s: Il punteggio del contenuto nel test è %3$s, che è considerato %4$s da leggere. %5$s"

#: languages/wordpress-seojs.php:139
msgid "To be able to create a redirect and fix this issue, you need %1$s. "
msgstr "Per poter creare un reindirizzamento (redirect) e risolvere questo problema, è necessario %1$s."

#: languages/wordpress-seojs.php:142
msgid "You can buy the plugin, including one year of support and updates, on %1$s."
msgstr "Puoi acquistare il plug-in, incluso un anno di supporto e aggiornamenti, su %1$s."

#: admin/class-meta-columns.php:78
msgid "Keyphrase"
msgstr "Frase chiave"

#: languages/wordpress-seojs.php:151 languages/yoast-components.php:104
msgid "Free"
msgstr "Gratuito"

#. translators: %1$s expands to the link to the article, %2$s closes the link
#. to the article
#: admin/views/tabs/metas/paper-content/media-content.php:41
msgid "Please carefully consider the implications and %1$sread this post%2$s if you want more information about the impact of showing media in search results."
msgstr "Prendi in considerazione attentamente le implicazioni e %1$sleggi questo articolo%2$s se vuoi avere maggiori informazioni sull'impatto che ha mostrare i media nei risultati di ricerca."

#: admin/views/tabs/metas/paper-content/media-content.php:38
msgid "By enabling this option, attachment URLs become visible to both your visitors and Google. To add value to your website, they should contain useful information, or they might have a negative impact on your ranking."
msgstr "Abilitando questa opzione, l'URL degli allegati diventa visibile sia per i tuoi visitatori sia per Google. Per aggiungere valore al tuo sito, essi dovrebbero contenere informazioni utili; diversamente, potrebbero avere un impatto negativo sul tuo posizionamento."

#. translators: %1$s: Yoast SEO.
#: admin/links/class-link-table-accessible-notifier.php:49
msgid "For this feature to work, %1$s needs to create a table in your database. We were unable to create this table automatically."
msgstr "Perché questa opzione funzioni, %1$s deve creare una tabella nel tuo database. Non è stato possibile creare questa tabella in automatico."

#. translators: %1$s: link to knowledge base article about solving table issue.
#. %2$s: is anchor closing.
#. translators: %1$s: link to knowledge base article about solving PHP issue.
#. %2$s: is anchor closing.
#: admin/links/class-link-table-accessible-notifier.php:54
#: admin/links/class-link-compatibility-notifier.php:56
msgid "Please read the following %1$sknowledge base article%2$s to find out how to resolve this problem."
msgstr "Leggi il seguente %1$sarticolo nella documentazipone base%2$s per trovare la soluzione a questo problema."

#. translators: %1$s: Yoast SEO. %2$s: PHP version %3$s: The current PHP
#. version.
#: admin/links/class-link-compatibility-notifier.php:49
msgid "For this feature to work %1$s requires at least PHP version %2$s. We have detected PHP version %3$s on this website."
msgstr "Affinché questa caratteristica funzioni in %1$s è necessario aver installato almeno la versione %2$s di PHP. Su questo sito abbiamo rilevato la versione  %3$s di PHP."

#. translators: %1$s: Yoast SEO. %2$s: Version number of Yoast SEO. %3$s:
#. strong opening tag, %4$s: strong closing tag
#. translators: %1$s: Yoast SEO. %2$s: Version number of Yoast SEO.
#: admin/links/class-link-table-accessible-notifier.php:41
#: admin/links/class-link-compatibility-notifier.php:41
msgid "The %3$sText link counter%4$s feature (introduced in %1$s %2$s) is currently disabled."
msgstr "Il %3$sContatore dei link del testo%4$s (introdotto con %1$s %2$s) è al momento attuale disabilitato."

#. translators: 1: link to yoast.com post about internal linking suggestion. 2:
#. is anchor closing.
#: admin/links/class-link-notifier.php:97
msgid "The Text link counter feature provides insights in how many links are found in your text and how many links are referring to your text. This is very helpful when you are improving your %1$sinternal linking%2$s."
msgstr "La funzione Contatore dei link di testo fornisce informazioni sul numero di link trovati nel testo e sul numero di link a cui si riferiscono. Questo è molto utile quando stai migliorando il tuo sistema di %1$slink interni%2$s."

#: admin/links/class-link-notifier.php:94
msgid "Count links"
msgstr "Conta i link"

#: admin/links/class-link-notifier.php:93
msgid "All you have to do is press the following button and we'll go through all your texts for you."
msgstr "Devi solo fare clic sul pulsante seguente e noi faremo l'analisi di tutti i testi per te."

#: admin/links/class-link-notifier.php:92
msgid "To make sure all the links in your texts are counted, we need to analyze all your texts."
msgstr "Per essere sicuri che tutti i link nel testo siano contati, abbiamo bisogno di analizzare tutti i tuoi testi."

#: deprecated/class-recalibration-beta.php:97
msgid "Simply switch the toggle to \"on\" and you'll be able to use the recalibrated analysis. At the same time, we'll add you to our specific mailing list. We'll only email you about your experiences with this recalibration!"
msgstr "Seleziona \"on\" e potrai usare le analisi Ricalibrate. Nello stesso tempo, ti aggiungeremo alla nostra speciale mailing list. Ti scriveremo solo in relazione alla tua esperienza con questa nuova ricalibrazione!"

#. translators: 1: link opening tag, 2: link closing tag, 3: strong opening
#. tag, 4: strong closing tag
#: deprecated/class-recalibration-beta.php:71
msgid "We have %1$srecalibrated our analysis%2$s. With the new analysis, we will get even closer to how Google sees your website. It would be %3$sawesome%4$s if you would like to %3$sbeta test this feature%4$s for us!"
msgstr "Abbiamo le nostre %1$snuove analisi Ricalibrate%2$s. Con queste nuove analisi, ci avviciniamo sempre più a come Google vede il tuo sito web. Sarebbe %3$smagnifico%4$s se tu testassi %3$squesta funzione in beta%4$s per noi!"

#: deprecated/class-recalibration-beta.php:67
msgid "Get an even better analysis"
msgstr "Ottieni un'analisi ancora migliore"

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:40
msgid "Cannot get the size of %1$s because of unknown reasons."
msgstr "Non puoi avere la dimensione di %1$s per un motivo sconosciuto."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:23
msgid "Cannot get the size of %1$s because it is hosted externally."
msgstr "Non puoi avere la dimensione di %1$s perché è su un servizio di hosting esterno."

#: admin/views/sidebar.php:49
msgctxt "course"
msgid "Free:"
msgstr "Gratuito:"

#. translators: %s expands to the current page number
#: frontend/class-breadcrumbs.php:658
msgid "Page %s"
msgstr "Pagina %s"

#. Translators: %1$d expands to the number of images containing an alt
#. attribute with the keyword, * %2$d expands to the total number of images,
#. %3$s and %4$s expand to a link on yoast.com, * %5$s expands to the anchor
#. end tag.
#: languages/yoast-seo-js.php:399
msgid "%3$sImage alt attributes%5$s: Out of %2$d images on this page, %1$d have alt attributes with words from your keyphrase or synonyms. That's a bit much. %4$sOnly include the keyphrase or its synonyms when it really fits the image%5$s."
msgstr "%3$sAttributo alt dell'immagine%5$s: su %2$d immagini in questa pagina, %1$d hanno l'attributo alt con parole della tua frase chiave o dei suoi sinonimi. È un po' troppo. %4$sDovresti inserire la frase chiave o i suoi sinonimi solo quando davvero descrivono l'immagine%5$s."

#. Translators: %1$s expands to a link on yoast.com, * %2$s expands to the
#. anchor end tag.
#: languages/yoast-seo-js.php:393
msgid "%1$sImage alt attributes%2$s: Good job!"
msgstr "%1$sAttributo alt dell'immagine%2$s: ottimo lavoro!"

#. Translators: %1$d expands to the number of images containing an alt
#. attribute with the keyword, * %2$d expands to the total number of images,
#. %3$s and %4$s expand to links on yoast.com, * %5$s expands to the anchor end
#. tag.
#: languages/yoast-seo-js.php:388
msgid "%3$sImage alt attributes%5$s: Out of %2$d images on this page, only %1$d has an alt attribute that reflects the topic of your text. %4$sAdd your keyphrase or synonyms to the alt tags of more relevant images%5$s!"
msgid_plural "%3$sImage alt attributes%5$s: Out of %2$d images on this page, only %1$d have alt attributes that reflect the topic of your text. %4$sAdd your keyphrase or synonyms to the alt tags of more relevant images%5$s!"
msgstr[0] "%3$sAttributo alt dell'immagine%5$s: su %2$d immagini in questa pagina, solo %1$d ha l'attributo alt che riflette l'argomento del tuo testo.%4$sDovresti inserire la frase chiave o i suoi sinonimi nel tag alt delle immagini più importanti!%5$s."
msgstr[1] "%3$sAttributo alt dell'immagine%5$s: su %2$d immagini in questa pagina, solo %1$d hanno l'attributo alt che riflette l'argomento del tuo testo.%4$sDovresti inserire la frase chiave o i suoi sinonimi nel tag alt delle immagini più importanti!%5$s."

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:382
msgid "%1$sImage alt attributes%3$s: Images on this page do not have alt attributes that reflect the topic of your text. %2$sAdd your keyphrase or synonyms to the alt tags of relevant images%3$s!"
msgstr "%1$sAttributo alt dell'immagine%3$s: le immagini in questa pagina non hanno l'attributo alt che riflette l'argomento del tuo testo %2$sDovresti inserire la frase chiave o i suoi sinonimi nel tag alt delle immagini più importanti%3$s!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:377
msgid "%1$sImage alt attributes%3$s: Images on this page have alt attributes, but you have not set your keyphrase. %2$sFix that%3$s!"
msgstr "%1$sAttributo alt dell'immagine%3$s: le immagini in questa pagina hanno l'attributo alt ma tu non hai impostato la frase chiave. %2$sInseriscila ora%3$s!"

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag, %3$d expands to the number of subheadings containing the keyphrase.
#: languages/yoast-seo-js.php:319
msgid "%1$sKeyphrase in subheading%2$s: %3$s of your higher-level subheadings reflects the topic of your copy. Good job!"
msgid_plural "%1$sKeyphrase in subheading%2$s: %3$s of your higher-level subheadings reflect the topic of your copy. Good job!"
msgstr[0] "%1$sFrase chiave nei sottotitoli%2$s: %3$s dei tuoi sottotitoli di livello più alto riflette l'argomento del tuo contenuto. Ottimo lavoro!"
msgstr[1] "%1$sFrase chiave nei sottotitoli%2$s: %3$s dei tuoi sottotitoli di livello più alto riflettono l'argomento del tuo contenuto. Ottimo lavoro!"

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag, %3$d expands to the number of subheadings containing the keyphrase.
#: languages/yoast-seo-js.php:314
msgid "%1$sKeyphrase in subheading%2$s: Your higher-level subheading reflects the topic of your copy. Good job!"
msgstr "%1$sFrase chiave nei sottotitoli%2$s: il sottotitolo di livello più alto riflette l'argomento del tuo contenuto. Perfetto!"

#. Translators: %1$s and %2$s expand to a link on yoast.com, %3$s expands to
#. the anchor end tag.
#: languages/yoast-seo-js.php:309
msgid "%1$sKeyphrase in subheading%3$s: More than 75%% of your higher-level subheadings reflect the topic of your copy. That's too much. %2$sDon't over-optimize%3$s!"
msgstr "%1$sFrase chiave nei sottotitoli%3$s: più del 75%% dei sottotitoli del livello più alto riflettono l'argomento del tuo contenuto. Questo è troppo. %2$sNon esagerare%3$s!"

#. Translators: %1$s and %2$s expand to a link on yoast.com, %3$s expands to
#. the anchor end tag.
#: languages/yoast-seo-js.php:305
msgid "%1$sKeyphrase in subheading%3$s: %2$sUse more keyphrases or synonyms in your higher-level subheadings%3$s!"
msgstr "%1$sFrase chiave nei sottotitoli%3$s: %2$susa di più le frasi chiave o i loro sinonimi nei sottotitoli di livello più alto%3$s!"

#. translators: 1: object ID; 2: object type.
#: src/models/indexable.php:174
msgid "Indexable deleted for object %1$s with type %2$s"
msgstr "L'indicizzazione è eliminata per l'oggetto %1$s con il tipo %2$s"

#. translators: 1: object ID; 2: object type.
#: src/models/indexable.php:147
msgid "Indexable saved for object %1$s with type %2$s"
msgstr "L'indicizzazione è salvata per l'oggetto %1$s con il tipo %2$s"

#. translators: 1: object ID; 2: object type.
#: src/models/indexable.php:101
msgid "Indexable created for object %1$s with type %2$s"
msgstr "L'indicizzazione è creata per l'oggetto %1$s con il tipo %2$s"

#: src/models/indexable-meta.php:120
msgid "Indexable meta deleted."
msgstr "L'indicizzazione per i meta è eliminata. "

#. translators: 1: ID; 2: value of a meta key.
#: src/models/indexable-meta.php:99
msgid "Indexable meta saved for indexable id %1$s with meta key %2$s"
msgstr "Un meta indicizzabile è stato salvato per l'id %1$s con la chiave meta %2$s"

#. translators: 1: ID; 2: value of a meta key.
#: src/models/indexable-meta.php:60
msgid "Indexable meta created for indexable id %1$s with meta key %2$s"
msgstr "Un meta indicizzabile è stato creato per l'id %1$s con la chiave meta %2$s"

#. translators: 1: expands to the indexable id. 2: expand to the meta key
#: src/exceptions/no-indexable-found.php:100
msgid "There is no meta found for indexable id %1$s and meta key %2$s."
msgstr "Non ci sono meta indicizzabili per l'id %1$s e la chiave meta %2$s."

#. translators: 1: expands to the author id
#: src/exceptions/no-indexable-found.php:82
msgid "There is no indexable found for author id %1$s."
msgstr "Non è stato trovato alcun risultato indicizzabile per l'autore con id %1$s."

#. translators: 1: expands to the term id. 2: expand to the taxonomy
#: src/exceptions/no-indexable-found.php:64
msgid "There is no primary term found for post id %1$s and taxonomy %2$s."
msgstr "Non è stato trovato alcun termine principale per l'articolo con id %1$s e la tassonomia %2$s."

#. translators: 1: expands to the term id. 2: expand to the taxonomy
#: src/exceptions/no-indexable-found.php:45
msgid "There is no indexable found for term id %1$s and taxonomy %2$s."
msgstr "Non sono stati trovati risultati indicizzabili per il termine con id %1$s e la tassonomia %2$s."

#. translators: %1$s expands to the post id
#: src/exceptions/no-indexable-found.php:27
msgid "There is no indexable found for post id %1$s."
msgstr "Non è stato trovato nulla da indicizzare per l'articolo con id %1$s."

#. translators: %1$s expands to the method name. %2$s expands to the class name
#: src/exceptions/missing-method.php:27
msgid "Method %1$s() does not exist in class %2$s"
msgstr "Il metodo  %1$s() non esiste nella classe %2$s"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:300
msgid "%1$sSingle title%3$s: H1s should only be used as your main title. Find all H1s in your text that aren't your main title and %2$schange them to a lower heading level%3$s!"
msgstr "%1$sUnico titolo%3$s: gli H1 dovrebbero essere usati soltanto come titolo principale. Trova tutti gli H1 nel tuo testo che non sono il tuo titolo principale e %2$smodificali con un livello di titolo inferiore%3$s!"

#: languages/yoast-seo-js.php:235
msgid "%1$sKeyphrase density%2$s: The focus keyphrase was found %5$d time. That's way more than the recommended maximum of %3$d times for a text of this length. %4$sDon't overoptimize%2$s!"
msgid_plural "%1$sKeyphrase density%2$s: The focus keyphrase was found %5$d times. That's way more than the recommended maximum of %3$d times for a text of this length. %4$sDon't overoptimize%2$s!"
msgstr[0] "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata %5$d volta. Questo è più del massimo suggerito di %3$d volte per un testo di questa lunghezza. %4$sNon esagerare%2$s!"
msgstr[1] "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata %5$d volte. Questo è più del massimo suggerito di %3$d volte per un testo di questa lunghezza. %4$sNon esagerare%2$s!"

#: languages/yoast-seo-js.php:232
msgid "%1$sKeyphrase density%2$s: The focus keyphrase was found %5$d time. That's more than the recommended maximum of %3$d times for a text of this length. %4$sDon't overoptimize%2$s!"
msgid_plural "%1$sKeyphrase density%2$s: The focus keyphrase was found %5$d times. That's more than the recommended maximum of %3$d times for a text of this length. %4$sDon't overoptimize%2$s!"
msgstr[0] "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata %5$d volta. Questo è più del massimo suggerito di %3$d volte per un testo di questa lunghezza. %4$sNon esagerare%2$s!"
msgstr[1] "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata %5$d volte. Questo è più del massimo suggerito di %3$d volte per un testo di questa lunghezza. %4$sNon esagerare%2$s!"

#: languages/yoast-seo-js.php:229
msgid "%1$sKeyphrase density%2$s: The focus keyphrase was found %3$d time. This is great!"
msgid_plural "%1$sKeyphrase density%2$s: The focus keyphrase was found %3$d times. This is great!"
msgstr[0] "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata  %3$d volta. Questo è ottimo!"
msgstr[1] "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata  %3$d volte. Questo è ottimo!"

#: languages/yoast-seo-js.php:226
msgid "%1$sKeyphrase density%2$s: The focus keyphrase was found %5$d time. That's less than the recommended minimum of %3$d times for a text of this length. %4$sFocus on your keyphrase%2$s!"
msgid_plural "%1$sKeyphrase density%2$s: The focus keyphrase was found %5$d times. That's less than the recommended minimum of %3$d times for a text of this length. %4$sFocus on your keyphrase%2$s!"
msgstr[0] "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata %5$d volta. Questo è meno del minimo suggerito di %3$d volte per un testo di questa lunghezza. %4$sInserisci la tua frase chiave%2$s!"
msgstr[1] "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata %5$d volte. Questo è meno del minimo suggerito di %3$d volte per un testo di questa lunghezza. %4$sInserisci la tua frase chiave%2$s!"

#: languages/yoast-seo-js.php:223
msgid "%1$sKeyphrase density%2$s: The focus keyphrase was found 0 times. That's less than the recommended minimum of %3$d times for a text of this length. %4$sFocus on your keyphrase%2$s!"
msgstr "%1$sDensità della frase chiave%2$s: la frase chiave è stata trovata 0 volte. Questo è meno del minimo suggerito di %3$d volte per un testo di questa lunghezza. %4$sInserisci la tua frase chiave%2$s!"

#. translators: %s expands to Yoast SEO Premium
#: admin/watchers/class-slug-change-watcher.php:219
msgid "With %s, you can easily create such redirects."
msgstr "Con %s, tu puoi facilmente creare tali reindirizzamenti (redirect)."

#: admin/views/tabs/tool/wpseo-import.php:34
msgid "Paste your settings from another Yoast SEO installation."
msgstr "Incolla le tue impostazioni da un'altra installazione di Yoast SEO."

#. translators: 1: Import settings button string from below.
#: admin/views/tabs/tool/wpseo-import.php:23
msgid "Import settings by pasting the settings you copied from another site here and clicking \"%s\"."
msgstr "Importa le impostazioni incollando le impostazioni che hai copiato da un altro sito e fai clic su \"%s\"."

#: admin/views/tabs/tool/wpseo-import.php:15
msgid "Import of settings is only supported on servers that run PHP 5.3 or higher."
msgstr "L'importazione delle impostazioni è supportata solo sui server che eseguono PHP 5.3 o versioni successive."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:25
msgid "Export your %1$s settings here, to copy them on another site."
msgstr "Esporta le impostazioni di %1$s qui, per poi copiarle in un altro sito."

#: admin/views/licenses.php:95
msgid "Improve sharing on Facebook and Pinterest"
msgstr "Migliora la condivisione su Facebook e Pinterest"

#: admin/import/class-import-settings.php:81
msgid "No settings found."
msgstr "Nessuna impostazione trovata."

#. translators: %1$s expands to Yoast SEO, %2$s expands to Yoast.com
#: admin/class-export.php:108
msgid "These are settings for the %1$s plugin by %2$s"
msgstr "Queste sono le impostazioni per il plugin %1$s di %2$s"

#: admin/class-export.php:49
msgid "You do not have the required rights to export settings."
msgstr "Non hai i diritti necessari per esportare le impostazioni."

#. translators: %1$s expands to Import settings
#: admin/class-export.php:56
msgid "Copy all these settings to another site's %1$s tab and click \"%1$s\" there."
msgstr "Copia tutte queste impostazioni nella scheda %1$s di un altro sito e fai clic \"%1$s\" lì."

#: inc/class-wpseo-admin-bar-menu.php:253
msgid "Google Ads"
msgstr "Google Ads"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:27
msgid "Not showing the date archives in the search results technically means those will have a %1$s robots meta. %2$sMore info on the search results settings%3$s."
msgstr "Non mostrare gli archivi per data nei motori di ricerca significa tecnicamente  che essi avranno impostato un %1$s nei metadati dei robots. %2$sLeggi di più riguardo alle impostazioni dei risultati di ricerca%3$s."

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-upsell-admin-block.php:95
msgid "Upgrade to %s"
msgstr "Passa a %s"

#. translators: %s expands to "Yoast SEO Premium".
#: admin/class-premium-upsell-admin-block.php:69
msgid "Dismiss %s upgrade notice"
msgstr "Ignora il messaggio che ti invita a passare a %s "

#: admin/class-admin-init.php:674
msgid "Learn about why permalinks are important for SEO."
msgstr "Approfondisci il motivo per cui i permalink sono così importanti per la SEO. "

#. translators: %1$s and %2$s expand to <i> items to emphasize the word in the
#. middle.
#: admin/class-admin-init.php:673
msgid "Changing your permalinks settings can seriously impact your search engine visibility. It should almost %1$s never %2$s be done on a live website."
msgstr "Cambiare le impostazioni dei permalink può influenzare la visibilità sui motori di ricerca. Non dovresti quasi  %1$s mai %2$s farlo quando un sito è online."

#: admin/class-admin-init.php:671
msgid "WARNING:"
msgstr "ATTENZIONE:"

#. Translators: %1$s and $3$s expand to the admin search page for the keyword,
#. %2$d expands to the number of times this keyword has been used before, %4$s
#. and %5$s expand to links to yoast.com, %6$s expands to the anchor end tag
#: languages/yoast-seo-js.php:460
msgid "%4$sPreviously used keyphrase%6$s: You've used this keyphrase %1$s%2$d times before%3$s. %5$sDo not use your keyphrase more than once%6$s."
msgstr "%4$sFrase chiave usata in precedenza%6$s: hai usato questa frase chiave %1$s%2$d volte prima%3$s. %5$sNon dovresti usare la frase chiave più di una volta%6$s."

#. Translators: %1$s and %2$s expand to an admin link where the keyword is
#. already used. %3$s and %4$s expand to links on yoast.com, %4$s expands to
#. the anchor end tag.
#: languages/yoast-seo-js.php:454
msgid "%3$sPreviously used keyphrase%5$s: You've used this keyphrase %1$sonce before%2$s. %4$sDo not use your keyphrase more than once%5$s."
msgstr "%3$sFrase chiave usata in precedenza%5$s: hai già usato questa frase chiave %1$suna volta%3$s. %4$sNon dovresti usare la frase chiave più di una volta%5$s."

#: languages/yoast-seo-js.php:449
msgid "%1$sPreviously used keyphrase%2$s: You've not used this keyphrase before, very good."
msgstr "%1$sFrase chiave usata in precedenza%2$s: non hai mai usato questa frase chiave. Ottimo!"

#. Translators: %1$s and %2$s open links to an articles about stopwords, %3$s
#. closes the links
#: languages/yoast-seo-js.php:441
msgid "%1$sSlug stopwords%3$s: The slug for this page contains a stop word. %2$sRemove it%3$s!"
msgid_plural "%1$sSlug stopwords%3$s: The slug for this page contains stop words. %2$sRemove them%3$s!"
msgstr[0] "%1$sStopword nello slug%3$s:  lo slug di questa pagina contiene una stopword. %2$sTi suggeriamo di toglierla%3$s!"
msgstr[1] "%1$sStopword nello slug%3$s:  lo slug di questa pagina contiene delle stopword. %2$sTi suggeriamo di toglierle%3$s!"

#. Translators:  %1$s and %2$s expand to links on yoast.com, %3$s expands to
#. the anchor end tag
#: languages/yoast-seo-js.php:437
msgid "%1$sSlug too long%3$s: the slug for this page is a bit long. %2$sShorten it%3$s!"
msgstr "%1$sSlug troppo lungo%3$s: lo slug di questa pagina è un po' troppo lungo. %2$sTi suggeriamo di accorciarlo%3$s!"

#. Translators:  %1$s expands to a link on yoast.com, %2$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:433
msgid "%1$sKeyphrase in slug%2$s: More than half of your keyphrase appears in the slug. That's great!"
msgstr "%1$sFrase chiave nello slug%2$s: più di mezza frase chiave è contenuta nello slug. Perfetto!"

#. Translators:  %1$s and %2$s expand to links on yoast.com, %3$s expands to
#. the anchor end tag
#: languages/yoast-seo-js.php:429
msgid "%1$sKeyphrase in slug%3$s: (Part of) your keyphrase does not appear in the slug. %2$sChange that%3$s!"
msgstr "%1$sFrase chiave nello slug%2$s: (parte) della tua frase chiave non è contenuta nello slug. %2$sTi suggeriamo di cambiarlo%3$s!"

#. Translators:  %1$s expands to a link on yoast.com, %2$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:424
msgid "%1$sKeyphrase in slug%2$s: Great work!"
msgstr "%1$sFrase chiave nello slug%2$s: ottimo lavoro!"

#. Translators: %1$s and %2$s expand to a link on yoast.com, %3$s expands to
#. the anchor end tag, %4$s expands to the keyword of the article.
#: languages/yoast-seo-js.php:420
msgid "%1$sKeyphrase in title%3$s: Not all the words from your keyphrase \"%4$s\" appear in the SEO title. %2$sTry to use the exact match of your keyphrase in the SEO title%3$s."
msgstr "%1$sFrase chiave nel titolo%3$s: non tutte le parole della tua frase chiave \"%4$s\" sono contenute nel titolo SEO. %2$sCerca di usare la frase chiave esatta nel titolo SEO%3$s."

#. Translators: %1$s and %2$s expand to a link on yoast.com, %3$s expands to
#. the anchor end tag.
#: languages/yoast-seo-js.php:415
msgid "%1$sKeyphrase in title%3$s: Does not contain the exact match. %2$sTry to write the exact match of your keyphrase in the SEO title%3$s."
msgstr "%1$sFrase chiave nel titolo%3$s: il titolo non contiene la frase esatta. %2$sCerca di usare la frase chiave esatta nel titolo SEO%3$s."

#. Translators: %1$s and %2$s expand to a link on yoast.com, %3$s expands to
#. the anchor end tag.
#: languages/yoast-seo-js.php:409
msgid "%1$sKeyphrase in title%3$s: The exact match of the keyphrase appears in the SEO title, but not at the beginning. %2$sTry to move it to the beginning%3$s."
msgstr "%1$sFrase chiave nel titolo%3$s: il titolo contiene la frase esatta, ma non all'inizio. %2$sCerca di usare la frase chiave esatta all'inizio del titolo SEO%3$s."

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag.
#: languages/yoast-seo-js.php:404
msgid "%1$sKeyphrase in title%2$s: The exact match of the keyphrase appears at the beginning of the SEO title. Good job!"
msgstr "%1$sFrase chiave nel titolo%2$s: il titolo SEO contiene la frase esatta all'inizio. Magnifico!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:373
msgid "%1$sImage alt attributes%3$s: No images appear on this page. %2$sAdd some%3$s!"
msgstr "%1$sAttributo alt dell'immagine%3$s: sembra che in questa pagina non ci siano immagini. %2$sTi suggeriamo di aggiungerne qualcuna%3$s!"

#. Translators:  %1$s and %2$s expand to links on yoast.com, %3$s expands to
#. the anchor end tag
#: languages/yoast-seo-js.php:369
msgid "%1$sLink keyphrase%3$s: You're linking to another page with the words you want this page to rank for. %2$sDon't do that%3$s!"
msgstr "%1$sLink alla frase chiave%3$s: stai inserendo un link a una pagina per le parole per le quali vuoi posizionare questa pagina %2$sTi suggeriamo di non farlo%3$s!"

#. Translators: The preceding sentence is "Text length: The text contains x
#. words.", %3$s expands to a link on yoast.com, %4$s expands to the anchor end
#. tag, %5$d expands to the recommended minimum of words.
#: languages/yoast-seo-js.php:365
msgid "This is far below the recommended minimum of %5$d word. %3$sAdd more content%4$s."
msgid_plural "This is far below the recommended minimum of %5$d words. %3$sAdd more content%4$s."
msgstr[0] "Questo è molto lontano da %5$d, il numero minimo di parole che ti suggeriamo per il tuo contenuto.  %3$sValuta se aggiungere altro contenuto%4$s."
msgstr[1] "Questo è molto lontano da %5$d, il numero minimo di parole che ti suggeriamo per il tuo contenuto.  %3$sValuta se aggiungere altro contenuto%4$s."

#. Translators: The preceding sentence is "Text length: The text contains x
#. words.", %3$s expands to a link on yoast.com, %4$s expands to the anchor end
#. tag, %5$d expands to the recommended minimum of words.
#: languages/yoast-seo-js.php:357
msgid "This is below the recommended minimum of %5$d word. %3$sAdd more content%4$s."
msgid_plural "This is below the recommended minimum of %5$d words. %3$sAdd more content%4$s."
msgstr[0] "Questo è sotto %5$d, il numero minimo di parole che ti suggeriamo per il tuo contenuto.  %3$sValuta se aggiungere altro contenuto%4$s."
msgstr[1] "Questo è sotto %5$d, il numero minimo di parole che ti suggeriamo per il tuo contenuto.  %3$sValuta se aggiungere altro contenuto%4$s."

#. Translators: %1$d expands to the number of words in the text, %2$s expands
#. to a link on yoast.com, %4$d expands to the anchor end tag. Translators:
#. %1$d expands to the number of words in the text, %2$s expands to a link on
#. yoast.com, %4$s expands to the anchor end tag.
#: languages/yoast-seo-js.php:339
msgid "%2$sText length%4$s: The text contains %1$d word."
msgid_plural "%2$sText length%4$s: The text contains %1$d words."
msgstr[0] "%2$sLunghezza del testo%4$s: il testo contiene %1$d parola."
msgstr[1] "%2$sLunghezza del testo%4$s: il testo contiene %1$d parole."

#. Translators: %1$d expands to the number of words in the text, %2$s expands
#. to a link on yoast.com, %3$s expands to the anchor end tag
#: languages/yoast-seo-js.php:325
msgid "%2$sText length%3$s: The text contains %1$d word. Good job!"
msgid_plural "%2$sText length%3$s: The text contains %1$d words. Good job!"
msgstr[0] "%2$sLunghezza del testo%3$s: il testo contiene %1$d parola. Ottimo lavoro!"
msgstr[1] "%2$sLunghezza del testo%3$s: il testo contiene %1$d parole. Ottimo lavoro!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:296
msgid "%1$sSEO title width%3$s: %2$sPlease create an SEO title%3$s."
msgstr "%1$sLarghezza del titolo SEO%3$s: %2$sScrivi un titolo SEO%3$s."

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:292
msgid "%1$sSEO title width%3$s: The SEO title is wider than the viewable limit. %2$sTry to make it shorter%3$s."
msgstr "%1$sLarghezza del titolo SEO%3$s: il titolo SEO è più lungo dei limiti di carattere previsti. %2$sProva a scrivere un titolo più corto%3$s."

#. Translators:  %1$s expands to a link on yoast.com, %2$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:288
msgid "%1$sSEO title width%2$s: Good job!"
msgstr "%1$sLarghezza del titolo SEO%3$s: magnifico lavoro!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:284
msgid "%1$sSEO title width%3$s: The SEO title is too short. %2$sUse the space to add keyphrase variations or create compelling call-to-action copy%3$s."
msgstr "%1$sLarghezza del titolo SEO%3$s: il titolo SEO è troppo corto %2$sTi suggeriamo di aggiungere variazioni della frase chiave per creare un testo accattivante che spinga i lettori all'azione%3$s."

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag
#: languages/yoast-seo-js.php:280
msgid "%1$sOutbound links%2$s: There are both nofollowed and normal outbound links on this page. Good job!"
msgstr "%1$sLink in uscita%2$s: ci sono sia link nofollow sia normali link in uscita in questa pagina. Perfetto!"

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag
#: languages/yoast-seo-js.php:276
msgid "%1$sOutbound links%2$s: Good job!"
msgstr "%1$sLink in uscita%2$s: splendido lavoro!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:272
msgid "%1$sOutbound links%3$s: All outbound links on this page are nofollowed. %2$sAdd some normal links%3$s."
msgstr "%1$sLink in uscita%3$s: tutti i link in uscita di questa pagina sono del tipo unfollow. %2$sTi suggeriamo di aggiungere anche dei link normali%3$s."

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:268
msgid "%1$sOutbound links%3$s: No outbound links appear in this page. %2$sAdd some%3$s!"
msgstr "%1$sLink in uscita%3$s: non ci sono link in uscita in questa pagina. %2$sTi suggeriamo di aggiungerne qualcuno%3$s."

#. Translators:  %1$s expands to a link on yoast.com, %2$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:264
msgid "%1$sMeta description length%2$s: Well done!"
msgstr "%1$sLunghezza della descrizione Meta%2$s: ben fatto!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag, %4$d expands to	the total available number of characters in
#. the meta description
#: languages/yoast-seo-js.php:260
msgid "%1$sMeta description length%3$s: The meta description is over %4$d characters. To ensure the entire description will be visible, %2$syou should reduce the length%3$s!"
msgstr "%1$sLunghezza della descrizione Meta%2$s: la descrizione Meta supera i %4$d caratteri. Per essere sicuro che l'intera descrizione sia visibile, %2$svaluta se ridurre la lunghezza%3$s!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag, %4$d expands to the number of characters in the meta
#. description, %5$d expands to the total available number of characters in the
#. meta description
#: languages/yoast-seo-js.php:255
msgid "%1$sMeta description length%3$s: The meta description is too short (under %4$d characters). Up to %5$d characters are available. %2$sUse the space%3$s!"
msgstr "%1$sLunghezza della descrizione Meta%2$s: la descrizione Meta è troppo corta (al di sotto dei %4$d caratteri). Hai a disposizione %5$d caratteri. %2$sPotresti aggiungere degli spazi%3$s!"

#. Translators:  %1$s and %2$s expand to a links on yoast.com, %3$s expands to
#. the anchor end tag
#: languages/yoast-seo-js.php:249
msgid "%1$sMeta description length%3$s:  No meta description has been specified. Search engines will display copy from the page instead. %2$sMake sure to write one%3$s!"
msgstr "%1$sLunghezza della descrizione Meta%3$s: non hai inserito la descrizione Meta. I motori di ricerca visualizzeranno, così, delle parti di testo prese dalla tua pagina. %2$sTi consigliamo di scriverne una%3$s!"

#: languages/yoast-seo-js.php:245
msgid "%1$sKeyphrase in meta description%2$s: The meta description has been specified, but it does not contain the keyphrase. %3$sFix that%4$s!"
msgstr "%1$sFrase chiave nella descrizione Meta%2$s: hai scritto la descrizione Meta, ma non contiene la frase chiave. %3$sTi suggeriamo di modificarla%4$s!"

#: languages/yoast-seo-js.php:242
msgid "%1$sKeyphrase in meta description%2$s: The meta description contains the keyphrase %3$s times, which is over the advised maximum of 2 times. %4$sLimit that%5$s!"
msgstr "%1$sFrase chiave nella descrizione Meta%2$s: la descrizione Meta contiene la frase chiave %3$s volte, che è più di 2, il numero consigliabile. %4$sTi suggeriamo di modificarla%5$s!"

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag.
#: languages/yoast-seo-js.php:239
msgid "%1$sKeyphrase in meta description%2$s: Keyphrase or synonym appear in the meta description. Well done!"
msgstr "%1$sFrase chiave nella descrizione Meta%2$s: la frase chiave o i suoi sinonimi sono contenuti nella descrizione Meta. Splendido!"

#: languages/yoast-seo-js.php:220
msgid "%3$sKeyphrase length%5$s: The keyphrase is %1$d words long. That's way more than the recommended maximum of %2$d words. %4$sMake it shorter%5$s!"
msgstr "%3$sLunghezza della frase chiave%5$s:  la frase chiave è lunga %1$s parole. È più lunga di  %2$d, il numero di parole suggerito. %4$sTi suggeriamo di scriverne una più corta%5$s!"

#: languages/yoast-seo-js.php:217
msgid "%3$sKeyphrase length%5$s: The keyphrase is %1$d words long. That's more than the recommended maximum of %2$d words. %4$sMake it shorter%5$s!"
msgstr "%3$sLunghezza della frase chiave%5$s: la frase chiave è lunga %1$d parole. È più lunga di %2$d, il numero di parole suggerito. %4$sTi suggeriamo di scriverne una più corta%5$s!"

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag.
#: languages/yoast-seo-js.php:214
msgid "%1$sKeyphrase length%2$s: Good job!"
msgstr "%1$sLunghezza della frase chiave%2$s: ottimo lavoro!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:210
msgid "%1$sKeyphrase length%3$s: No focus keyphrase was set for this page. %2$sSet a keyphrase in order to calculate your SEO score%3$s."
msgstr "%1$sLunghezza della frase chiave%2$s: nessuna frase chiave è stata impostata per questa pagina. %2$sImposta una frase chiave, in modo che sia possibile calcolare il punteggio SEO%3$s."

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:206
msgid "%1$sKeyphrase length%3$s: %2$sSet a keyphrase in order to calculate your SEO score%3$s."
msgstr "%1$sLunghezza della frase chiave%2$s: %2$simposta una frase chiave, in modo che sia possibile calcolare il punteggio SEO%3$s."

#. Translators: %1$s expands to links to Yoast.com articles, %2$s expands to
#. the anchor end tag
#: languages/yoast-seo-js.php:202
msgid "%1$sKeyphrase distribution%2$s: Good job!"
msgstr "%1$sDistribuzione della frase chiave%2$s: grande lavoro!"

#. Translators: %1$s and %2$s expand to links to Yoast.com articles, %3$s
#. expands to the anchor end tag
#: languages/yoast-seo-js.php:198
msgid "%1$sKeyphrase distribution%3$s: Uneven. Some parts of your text do not contain the keyphrase or its synonyms. %2$sDistribute them more evenly%3$s."
msgstr "%1$sDistribuzione della frase chiave%3$s: irregolare. Alcune parti del tuo testo non contengono la frase chiave o i suoi sinonimi. %2$sTi suggeriamo di distribuirli in modo più regolare%3$s."

#. Translators: %1$s and %2$s expand to links to Yoast.com articles, %3$s
#. expands to the anchor end tag
#: languages/yoast-seo-js.php:193
msgid "%1$sKeyphrase distribution%3$s: Very uneven. Large parts of your text do not contain the keyphrase or its synonyms. %2$sDistribute them more evenly%3$s."
msgstr "%1$sDistribuzione della frase chiave%3$s: molto irregolare. Molte parti del tuo testo non contengono la frase chiave o i suoi sinonimi. %2$sTi suggeriamo di distribuirli in modo più regolare%3$s."

#. Translators: %1$s and %2$s expand to links to Yoast.com articles, %3$s
#. expands to the anchor end tag
#: languages/yoast-seo-js.php:188
msgid "%1$sKeyphrase distribution%3$s: %2$sInclude your keyphrase or its synonyms in the text so that we can check keyphrase distribution%3$s."
msgstr "%1$sDistribuzione della frase chiave%3$s: %2$sincludi la parola chiave o i suoi sinonimi nel testo così è possibile calcolarne la distribuzione%3$s."

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag.
#: languages/yoast-seo-js.php:183
msgid "%1$sKeyphrase in introduction%3$s: Your keyphrase or its synonyms do not appear in the first paragraph. %2$sMake sure the topic is clear immediately%3$s."
msgstr "%1$sFrase chiave nell'introduzione%3$s: la frase chiave che hai scelto o i suoi sinonimi non compaiono nel primo paragrafo. %2$sAssicurati che i tuoi lettori capiscano subito l'argomento%3$s."

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag.
#: languages/yoast-seo-js.php:179
msgid "%1$sKeyphrase in introduction%3$s:Your keyphrase or its synonyms appear in the first paragraph of the copy, but not within one sentence. %2$sFix that%3$s!"
msgstr "%1$sFrase chiave nell'introduzione%3$s: la frase chiave che hai scelto o i suoi sinonimi compaiono nel primo paragrafo, ma non in una frase. %2$sTi suggeriamo di modificare il testo%3$s!"

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag.
#: languages/yoast-seo-js.php:175
msgid "%1$sKeyphrase in introduction%2$s: Well done!"
msgstr "%1$sFrase chiave nell'introduzione%2$s: ben fatto!"

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag
#: languages/yoast-seo-js.php:171
msgid "%1$sInternal links%2$s: There are both nofollowed and normal internal links on this page. Good job!"
msgstr "%1$sLink interni%2$s: ci sono sia link nofollow sia link normali in questa pagina. Perfetto!"

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag
#: languages/yoast-seo-js.php:167
msgid "%1$sInternal links%2$s: You have enough internal links. Good job!"
msgstr "%1$sLink interni%2$s: ci sono abbastanza link interni. Ottimo!"

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:163
msgid "%1$sInternal links%3$s: The internal links in this page are all nofollowed. %2$sAdd some good internal links%3$s."
msgstr "%1$sLink interni%3$s: i link in questa pagina sono di tipo nofollow. %2$sTi suggeriamo di aggiungere qualche link normale%3$s."

#. Translators: %1$s and %2$s expand to links on yoast.com, %3$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:159
msgid "%1$sInternal links%3$s: No internal links appear in this page, %2$smake sure to add some%3$s!"
msgstr "%1$sLink interni%3$s: sembra che non ci siano link in questa pagina. %2$sTi suggeriamo di aggiungerne qualcuno%3$s!"

#: languages/yoast-seo-js.php:155
msgid "%1$sFunction words in keyphrase%3$s: Your keyphrase \"%4$s\" contains function words only. %2$sLearn more about what makes a good keyphrase.%3$s"
msgstr "%1$sParole funzionali nell frase chiave%3$s: la tua frase chiave \"%4$s\" contiene solo parole funzionali. %2$sLeggi di più su come scegliere una perfetta frase chiave.%3$s"

#. Translators: %1$s expands to a link on yoast.com, %3$s expands to the anchor
#. end tag.
#: languages/yoast-seo-js.php:140
msgid "%1$sTransition words%2$s: Well done!"
msgstr "%1$sParole di transizione%2$s: ben fatto!"

#. Translators: %1$s and %4$s expand to a link to yoast.com, %2$s expands to
#. the anchor end tag, %3$s expands to the percentage of sentences containing
#. transition words
#: languages/yoast-seo-js.php:136
msgid "%1$sTransition words%2$s: Only %3$s of the sentences contain transition words, which is not enough. %4$sUse more of them%2$s."
msgstr "%1$sParole di transizione%2$s: solo il %3$s delle frasi contengono delle parole di transizione, che non è abbastanza. %4$sTi suggeriamo di aggiungerne altre%2$s."

#. Translators: %1$s and %3$s expand to a link to yoast.com, %2$s expands to
#. the anchor end tag
#: languages/yoast-seo-js.php:131
msgid "%1$sTransition words%2$s: None of the sentences contain transition words. %3$sUse some%2$s."
msgstr "%1$sParole di transizione%2$s: nessuna frase contiene parole di transizione. %4$sTi suggeriamo di aggiungerne alcune%2$s."

#. Translators: %1$s and %3$s expand to links to articles on Yoast.com, %2$s
#. expands to the anchor end tag
#: languages/yoast-seo-js.php:127
msgid "%1$sNot enough content%2$s: %3$sPlease add some content to enable a good analysis%2$s."
msgstr "%1$sNon c'è abbastanza contenuto%2$s: %3$sAggiungi altro contenuto per permettere una buona analisi di leggibilità%2$s."

#. Translators: %1$s expands to a link to https://yoa.st/headings, %2$s expands
#. to the link closing tag.
#: languages/yoast-seo-js.php:122
msgid "%1$sSubheading distribution%2$s: You are not using any subheadings, but your text is short enough and probably doesn't need them."
msgstr "%1$sDistribuzione dei sottotitoli%2$s: non stai usando i sottotitoli, ma il tuo testo è abbastanza corto e probabilmente non ti servono."

#. Translators: %1$s and %3$s expand to a link to https://yoa.st/headings, %2$s
#. expands to the link closing tag.
#: languages/yoast-seo-js.php:118
msgid "%1$sSubheading distribution%2$s: You are not using any subheadings, although your text is rather long. %3$sTry and add some subheadings%2$s."
msgstr "%1$sDistribuzione dei sottotitoli%2$s: non stai usando i sottotitoli, sebbene il tuo testo sia piuttosto lungo.  %3$sTi suggeriamo di aggiungere i sottotitoli%2$s."

#. Translators: %1$s and %5$s expand to a link on yoast.com, %3$d to the number
#. of text sections not separated by subheadings, %4$d expands to the
#. recommended number of words following a subheading, %2$s expands to the link
#. closing tag. Translators: %1$s and %5$s expand to a link on yoast.com, %3$d
#. to the number of text sections not separated by subheadings, %4$d expands to
#. the recommended number of words following a subheading, %2$s expands to the
#. link closing tag.
#: languages/yoast-seo-js.php:114
msgid "%1$sSubheading distribution%2$s: %3$d section of your text is longer than %4$d words and is not separated by any subheadings. %5$sAdd subheadings to improve readability%2$s."
msgid_plural "%1$sSubheading distribution%2$s: %3$d sections of your text are longer than %4$d words and are not separated by any subheadings. %5$sAdd subheadings to improve readability%2$s."
msgstr[0] "%1$sDistribuzione dei sottotitoli%2$s: %3$d sezione del tuo testo è più lunghe di %4$d parole e non è separata da sottotitoli. %5$sAggiungi dei sottotitoli per aumentare la leggibilità del testo%2$s."
msgstr[1] "%1$sDistribuzione dei sottotitoli%2$s: %3$d sezioni del tuo testo sono più lunghe di %4$d parole e non sonoseparate da sottotitoli. %5$sAggiungi dei sottotitoli per aumentare la leggibilità del testo%2$s."

#. Translators: %1$s expands to a link to https://yoa.st/headings, %2$s expands
#. to the link closing tag.
#: languages/yoast-seo-js.php:103
msgid "%1$sSubheading distribution%2$s: Great job!"
msgstr "%1$sDistribuzione dei sottotitoli%2$s: magnifico lavoro!"

#. Translators: %1$s and %6$s expand to a link on yoast.com, %2$s expands to
#. the anchor end tag, %3$d expands to percentage of sentences, %4$s expands to
#. the recommended maximum sentence length, %5$s expands to the recommended
#. maximum percentage.
#: languages/yoast-seo-js.php:98
msgid "%1$sSentence length%2$s: %3$s of the sentences contain more than %4$s words, which is more than the recommended maximum of %5$s. %6$sTry to shorten the sentences%2$s."
msgstr "%1$sLunghezza delle frasi%2$s: il %3$s delle frasi contiene più di %4$s parole, che supera la lunghezza massima suggerita di %5$s. %6$sProva a scrivere frasi più corte%2$s."

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag
#: languages/yoast-seo-js.php:92
msgid "%1$sSentence length%2$s: Great!"
msgstr "%1$sLunghezza delle frasi%2$s: grande!"

#. Translators:  %1$s expands to a link on yoast.com, %2$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:78
msgid "%1$sConsecutive sentences%2$s: There is enough variety in your sentences. That's great!"
msgstr "%1$sFrasi consecutive%2$s: c'è una grande varietà nelle frasi. Davvero grande!"

#. Translators: %1$s and %5$s expand to a link on yoast.com, %2$s expands to
#. the anchor end tag, %3$d expands to the number of consecutive sentences
#. starting with the same word, %4$d expands to the number of instances where 3
#. or more consecutive sentences start with the same word.
#: languages/yoast-seo-js.php:74
msgid "%1$sConsecutive sentences%2$s: The text contains %3$d consecutive sentences starting with the same word. %5$sTry to mix things up%2$s!"
msgid_plural "%1$sConsecutive sentences%2$s: The text contains %4$d instances where %3$d or more consecutive sentences start with the same word. %5$sTry to mix things up%2$s!"
msgstr[0] "%1$sFrasi consecutive%2$s: il testo contiene %3$d frasi consecutive che iniziano con la stessa parola. %5$sTi suggeriamo di provare a variare un po'%2$s!"
msgstr[1] "%1$sFrasi consecutive%2$s: il testo contiene %4$d paragrafi nei quali  %3$d o più frasi iniziano con la stessa parola. %5$sTi suggeriamo di provare a variare un po'%2$s!"

#. Translators: %1$s and %5$s expand to a link on yoast.com, %2$s expands to
#. the anchor end tag, %3$s expands to the percentage of sentences in passive
#. voice, %4$s expands to the recommended value.
#: languages/yoast-seo-js.php:68
msgid "%1$sPassive voice%2$s: %3$s of the sentences contain passive voice, which is more than the recommended maximum of %4$s. %5$sTry to use their active counterparts%2$s."
msgstr "%1$sForme passive%2$s: %3$s delle frasi contiene delle forme passive, che è maggiore del massimo suggerito di %4$s. %5$sTi suggeriamo di provare a trasformarle in forma attiva%2$s."

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor
#. end tag.
#: languages/yoast-seo-js.php:63
msgid "%1$sPassive voice%2$s: You're using enough active voice. That's great!"
msgstr "%1$sForme passive%2$s: stai usando solo forme attive. Davvero grande!"

#. Translators: %1$s and %5$s expand to a link on yoast.com, %2$s expands to
#. the anchor end tag, %3$d expands to the number of paragraphs over the
#. recommended word limit, %4$d expands to the word limit
#: languages/yoast-seo-js.php:59
msgid "%1$sParagraph length%2$s: %3$d of the paragraphs contains more than the recommended maximum of %4$d words. %5$sShorten your paragraphs%2$s!"
msgid_plural "%1$sParagraph length%2$s: %3$d of the paragraphs contain more than the recommended maximum of %4$d words. %5$sShorten your paragraphs%2$s!"
msgstr[0] "%1$sLunghezza dei paragrafi%2$s: %3$d dei paragrafi contiene più di %4$d parole, che è il numero suggerito. %5$sTi consigliamo di scrivere paragrafi più corti%2$s!"
msgstr[1] "%1$sLunghezza dei paragrafi%2$s: %3$d dei paragrafi contiene più di %4$d parole, che è il numero suggerito. %5$sTi consigliamo di scrivere paragrafi più corti%2$s!"

#. Translators:  %1$s expands to a link on yoast.com, %2$s expands to the
#. anchor end tag
#: languages/yoast-seo-js.php:54
msgid "%1$sParagraph length%2$s: None of the paragraphs are too long. Great job!"
msgstr "%1$sLunghezza dei paragrafi%2$s: tutti i paragrafi sono della giusta lunghezza- Ottimo lavoro!"

#: languages/yoast-seo-js.php:5
msgid "Good job!"
msgstr "Ottimo lavoro!"

#. Translators: %1$s and %5$s expand to a link on yoast.com, %2$s to the anchor
#. end tag, %7$s expands to the anchor end tag and a full stop, %3$s expands to
#. the numeric Flesch reading ease score, %4$s to the easiness of reading, %6$s
#. expands to a call to action based on the score
#: languages/yoast-seo-js.php:50
msgid "%1$sFlesch Reading Ease%2$s: The copy scores %3$s in the test, which is considered %4$s to read. %5$s%6$s%7$s"
msgstr "%1$sFlesch Reading Ease%2$s: il contenuto ha un punteggio di %3$s nel test, che è considerato %4$s da leggere. %5$s%6$s%7$s"

#: languages/wordpress-seojs.php:89
msgid "Focus keyphrase"
msgstr "Frase chiave"

#: languages/wordpress-seojs.php:82
msgid "Help on choosing the perfect focus keyphrase"
msgstr "Aiuto nella scelta della perfetta frase chiave"

#: languages/wordpress-seojs.php:76
msgid "Would you like to add a related keyphrase?"
msgstr "Vorresti aggiungere una frase chiave correlata?"

#: languages/wordpress-seojs.php:8
msgid "Go %s!"
msgstr "Vai %s!"

#: languages/wordpress-seojs.php:5
msgid "Did you know %s also analyzes the different word forms of your keyphrase, like plurals and past tenses?"
msgstr "Sai che %s analizza anche le diverse forme delle tue frasi chiave, come i plurali e i tempi verbali?"

#. translators: %1$s expands to object type. %2$s expands to the object ID.
#: inc/exceptions/class-rest-request-exception.php:25
msgid "%1$s with ID %2$s couldn't be patched"
msgstr "Il %1$s con l'ID %2$s non può essere modificato"

#. translators: %1$s expands to the indexable's ID.
#: inc/exceptions/class-invalid-indexable-exception.php:41
msgid "Invalid POST request. Meta values already exist for object with ID %1$s."
msgstr "Richiesta per l'articolo non valida. Il valore meta esiste già per l'oggetto con l'D %1$s."

#. translators: %1$s expands to the indexable's ID.
#: inc/exceptions/class-invalid-indexable-exception.php:24
msgid "Indexable with ID `%1$s` does not exist"
msgstr "L'indicizzazione con l'ID `%1$s` non esiste"

#. translators: %1$s expands to the object ID. %2$s resolved to the object
#. type.
#: inc/exceptions/class-invalid-argument-exception.php:147
msgid "No object with ID %1$s and %2$s could be found"
msgstr "Non è stato trovato alcun oggetto con l'ID %1$s e %2$s "

#. translators: %1$s expands to the object subtype. %2$s resolved to the object
#. type.
#: inc/exceptions/class-invalid-argument-exception.php:128
msgid "`%1$s` is not a valid subtype of `%2$s`"
msgstr "`%1$s` non è un valido sottotipo di `%2$s`"

#. translators: %1$s expands to the object type.
#: inc/exceptions/class-invalid-argument-exception.php:110
msgid "The object type `%1$s` is invalid"
msgstr "Il tipo oggetto `%1$s` non è valido"

#. translators: %1$s expands to the parameter name. %2$s expands to the
#. expected type and %3$s expands to the expected type.
#: inc/exceptions/class-invalid-argument-exception.php:43
msgid "Invalid type for parameter `%1$s` passed. Expected `%2$s`, but got `%3$s`"
msgstr "Il tipo non è valido per il parametro '%1$s' passato.. Il tipo atteso è `%2$s`, e non `%3$s`"

#. translators: %1$s expands to the parameter name.
#: inc/exceptions/class-invalid-argument-exception.php:24
msgid "The parameter `%1$s` cannot be empty."
msgstr "Il parametro `%1$s` non può essere vuoto."

#: inc/class-wpseo-rank.php:141
msgid "SEO: No Focus Keyphrase"
msgstr "SEO: manca la frase chiave"

#: inc/class-wpseo-admin-bar-menu.php:312
msgid "Check Keyphrase Density"
msgstr "Controlla la densità della frase chiave"

#: admin/views/tabs/network/features.php:53
msgid "Disable"
msgstr "Disattiva"

#: admin/views/tabs/network/features.php:52
msgid "Allow Control"
msgstr "Permetti il controllo"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/network/features.php:24
msgid "This tab allows you to selectively disable %s features for all sites in the network. By default all features are enabled, which allows site admins to choose for themselves if they want to toggle a feature on or off for their site. When you disable a feature here, site admins will not be able to use that feature at all."
msgstr "Questa scheda ti permette di disattivare in modo selettivo le funzionalità di %s per tutti i siti del network. In modalità predefinita, tutte le funzionalità sono attivate: questo permette agli amministratori di scegliere quali funzioni attivare o meno per i propri siti. Quando disattivi una funzionalità qui, gli amministratori dei siti non potranno usarla per nulla."

#: languages/wordpress-seojs.php:101 admin/views/sidebar.php:28
msgid "Rank better with synonyms & related keyphrases"
msgstr "Posizionati meglio con i sinonimi e le frasi chiave correlate"

#: admin/views/licenses.php:142
msgid "optimize a single post for synonyms and related keyphrases."
msgstr "ottimizza un singolo articolo per i sinonimi e le frasi chiave correlate."

#. translators: %s: argument name
#: admin/views/class-yoast-feature-toggle.php:88
msgid "%s is a required feature toggle argument."
msgstr "%s è un argomento della funzionalità di attivazione obbligatorio."

#: admin/views/licenses.php:141
msgid "Synonyms & related keyphrases"
msgstr "Sinonimi e frasi chiave correlate."

#: languages/wordpress-seojs.php:79
msgid "Add related keyphrase"
msgstr "Aggiungi una frase chiave correlata"

#: admin/formatter/class-metabox-formatter.php:58
msgid "Remove keyphrase"
msgstr "Rimuovi la frase chiave"

#: admin/formatter/class-metabox-formatter.php:57
msgid "Keyphrase:"
msgstr "Frase chiave:"

#. translators: %1$s expands to Yoast SEO Premium.
#. translators: %1$s expands to Keyword research training.
#. translators: %1$s expands to Yoast SEO plugin training.
#. translators: %1$s expands to Local SEO.
#: admin/config-ui/components/class-component-suggestions.php:46
#: admin/config-ui/components/class-component-suggestions.php:69
#: admin/config-ui/components/class-component-suggestions.php:89
#: admin/config-ui/components/class-component-suggestions.php:110
msgid "%1$s video"
msgstr "video %1$s"

#: admin/class-yoast-form.php:764
msgid "This feature has been disabled by the network admin."
msgstr "Questa funzionalità è stata disattivata dall'amministratore del network."

#: admin/class-meta-columns.php:132 admin/class-meta-columns.php:616
msgid "Focus keyphrase not set."
msgstr "La frase chiave non è stata impostata."

#: admin/class-premium-upsell-admin-block.php:56
msgid "Multiple keyphrases"
msgstr "Frasi chiave multiple"

#: admin/metabox/class-metabox.php:571
#: admin/taxonomy/class-taxonomy-fields-presenter.php:125
#: admin/class-yoast-form.php:564
msgid "Clear Image"
msgstr "Cancella l'immagine"

#. translators: %s expands to 'Yoast SEO Premium'.
#. translators: %s expands to Yoast SEO Premium
#. translators: %s is replaced by the plugin name
#: languages/wordpress-seojs.php:73 admin/class-multiple-keywords-modal.php:36
#: admin/formatter/class-metabox-formatter.php:203
#: admin/class-keyword-synonyms-modal.php:36
#: admin/class-premium-upsell-admin-block.php:72
#: admin/class-premium-popup.php:81 admin/class-add-keyword-modal.php:36
#: admin/watchers/class-slug-change-watcher.php:223 admin/views/sidebar.php:39
msgid "Get %s"
msgstr "Passa a %s"

#: languages/wordpress-seojs.php:239
msgid "Optional. Customize how you want to describe the duration of the instruction"
msgstr "Opzionale. Personalizza come vuoi descrivere la durata del processo indicato nelle istruzioni:"

#: languages/wordpress-seojs.php:236
msgid "Describe the duration of the instruction:"
msgstr "Descrivi la durata del processo indicato nelle istruzioni:"

#: languages/wordpress-seojs.php:67
msgid "Learn more about the readability analysis"
msgstr "Leggi di più sull'Analisi di leggibilità. "

#: inc/class-wpseo-admin-bar-menu.php:656
msgid "There is a new notification."
msgid_plural "There are new notifications."
msgstr[0] "C'è una nuova notifica. "
msgstr[1] "Ci sono nuove notifiche. "

#: inc/options/class-wpseo-option-titles.php:820
msgid "Colon"
msgstr "Due punti"

#. translators: %1$s expands to Yoast SEO, %2$s: 'SEO' plugin name of possibly
#. conflicting plugin with regard to the creation of duplicate SEO meta.
#: admin/class-plugin-conflict.php:165
msgid "Both %1$s and %2$s manage the SEO of your site. Running two SEO plugins at the same time is detrimental."
msgstr "Sia %1$s sia %2$s gestiscono la SEO del tuo sito. Avere due plugin SEO attivi contemporaneamente può esere dannoso."

#: languages/yoast-components.php:17
msgid "This is a list of related content to which you could link in your post. {{a}}Read our article about site structure{{/a}} to learn more about how internal linking can help improve your SEO."
msgstr "Questa è una lista di contenuti correlati che potresti inserire come collegamenti nel tuo articolo {{a}}Leggi il nostro articolo sulla struttura di un sito{{/a}} per approfondire il ruolo dell'internal linking nella tua strategia SEO."

#: languages/wordpress-seojs.php:287
msgid "%s, %s and %s"
msgstr "%s, %s e %s"

#: languages/wordpress-seojs.php:283
msgid "%s and %s"
msgstr "%s e %s"

#: languages/wordpress-seojs.php:279
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minuto"
msgstr[1] "%d minuti"

#: languages/wordpress-seojs.php:275
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ora"
msgstr[1] "%d ore"

#: languages/wordpress-seojs.php:271
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d giorno"
msgstr[1] "%d giorni"

#: languages/wordpress-seojs.php:264
msgid "Enter a step title"
msgstr "Inserisci un titolo del passaggio "

#: languages/wordpress-seojs.php:233
msgid "Optional. This can give you better control over the styling of the steps."
msgstr "Opzionale. Questo ti fornisce un controllo migliore dello stile dei passaggi. "

#: languages/wordpress-seojs.php:230
msgid "CSS class(es) to apply to the steps"
msgstr "Classe(i) CSS da applicare ai passaggi "

#: languages/wordpress-seojs.php:221
msgid "minutes"
msgstr "minuti "

#: languages/wordpress-seojs.php:218
msgid "hours"
msgstr "ore"

#: languages/wordpress-seojs.php:215
msgid "days"
msgstr "giorni"

#: languages/wordpress-seojs.php:191
msgid "Create a How-to guide in an SEO-friendly way. You can only use one How-to block per post."
msgstr "Crea una guida How-to in una modalità SEO-friendly. Puoi usare solo un blocco How-to per articolo."

#: languages/wordpress-seojs.php:154
msgid "List your Frequently Asked Questions in an SEO-friendly way. You can only use one FAQ block per post."
msgstr "Elenca le tue FAQ (Frequently Asked Questions) in una modalità SEO-friendly. Puoi usare solo un blocco FAQ per articolo. "

#: languages/wordpress-seojs.php:26
msgid "Copy error"
msgstr "Copia l'errore "

#: languages/wordpress-seojs.php:23
msgid "An error occurred loading the %s primary taxonomy picker."
msgstr "Si è verificato un errore durante il caricamento del selettore della tassonomia primaria %s. "

#. translators: %1$s expands to Yoast.
#: inc/class-structured-data-blocks.php:62
msgid "%1$s Structured Data Blocks"
msgstr "Blocchi di Structured Data di %1$s"

#: languages/wordpress-seojs.php:249
msgid "Time needed:"
msgstr "Tempo richiesto:"

#: languages/wordpress-seojs.php:178
msgid "Move question down"
msgstr "Sposta la domanda in basso"

#: languages/wordpress-seojs.php:175
msgid "Move question up"
msgstr "Sposta la domanda in alto"

#: languages/wordpress-seojs.php:172
msgid "Insert question"
msgstr "Inserisci domanda"

#: languages/wordpress-seojs.php:169
msgid "Delete question"
msgstr "Elimina domanda"

#: languages/wordpress-seojs.php:184
msgid "Enter the answer to the question"
msgstr "Inserisci la risposta alla domanda"

#: languages/wordpress-seojs.php:181
msgid "Enter a question"
msgstr "Inserisci una domanda"

#: languages/wordpress-seojs.php:166
msgid "Add question"
msgstr "Aggiungi domanda"

#: languages/wordpress-seojs.php:160
msgid "Frequently Asked Questions"
msgstr "Domande frequenti"

#: languages/wordpress-seojs.php:98
msgid "Great news: you can, with %s!"
msgstr "Ottime notizie: puoi con %s!"

#: languages/wordpress-seojs.php:29
msgid "Select the primary %s"
msgstr "Seleziona il %s primario"

#: inc/class-wpseo-admin-bar-menu.php:307
msgid "Check links to this URL"
msgstr "Controlla i link a questo URL"

#: inc/class-wpseo-admin-bar-menu.php:248
msgid "Keyword research training"
msgstr "Keyword research training"

#. translators: %1$s is the archive template link start tag, %2$s is the link
#. closing tag, %3$s is a comma separated string with content types.
#: admin/notifiers/class-post-type-archive-notification-handler.php:65
msgid "Please check the %1$sarchive template%2$s for the following content type: %3$s."
msgid_plural "Please check the %1$sarchive templates%2$s for the following content types: %3$s."
msgstr[0] "Controlla %1$sil template dell'archivio%2$s per il seguente tipo di contenuto: %3$s."
msgstr[1] "Controlla %1$si template dell'archivio%2$s per i seguenti tipi di contenuto: %3$s."

#: languages/yoast-components.php:161
msgid "Are you trying to use multiple keyphrases? You should add them separately below."
msgstr "Stai cercando di utilizzare più frasi chiave? Dovresti aggiungerle separatamente sotto."

#: languages/yoast-components.php:155
msgid "Mark as cornerstone content"
msgstr "Indica come contenuto centrale"

#: languages/wordpress-seojs.php:261
msgid "Move step down"
msgstr "Sposta passaggio in basso"

#: languages/wordpress-seojs.php:258
msgid "Move step up"
msgstr "Sposta passaggio in alto"

#: languages/wordpress-seojs.php:255
msgid "Insert step"
msgstr "Inserisci passaggio"

#: languages/wordpress-seojs.php:252
msgid "Delete step"
msgstr "Elimina passaggio"

#: languages/wordpress-seojs.php:188
msgid "Add image"
msgstr "Aggiungi un'immagine"

#: languages/wordpress-seojs.php:267
msgid "Enter a step description"
msgstr "Inserisci una descrizione del passaggio"

#: languages/wordpress-seojs.php:245
msgid "Enter a description"
msgstr "Inserisci una descrizione"

#: languages/wordpress-seojs.php:242
msgid "Unordered list"
msgstr "Lista non ordinata"

#: languages/wordpress-seojs.php:209
msgid "Showing step items as an ordered list."
msgstr "Mostra gli elementi del passaggio come un elenco ordinato."

#: languages/wordpress-seojs.php:206
msgid "Showing step items as an unordered list"
msgstr "Mostra gli elementi del passaggio come un elenco non ordinato"

#: languages/wordpress-seojs.php:203
msgid "Add step"
msgstr "Aggiungi un passaggio"

#: languages/wordpress-seojs.php:224
msgid "Delete total time"
msgstr "Elimina il tempo totale"

#: languages/wordpress-seojs.php:212
msgid "Add total time"
msgstr "Aggiungi il tempo totale"

#: languages/wordpress-seojs.php:197
msgid "How to"
msgstr "How to"

#: languages/wordpress-seojs.php:194
msgid "How-to"
msgstr "How-to"

#: languages/wordpress-seojs.php:20
msgid "Snippet Preview"
msgstr "Anteprima dello Snippet"

#: languages/wordpress-seojs.php:64
msgid "Analysis results"
msgstr "Risultati dell'analisi"

#: languages/wordpress-seojs.php:85
msgid "Enter a focus keyphrase to calculate the SEO score"
msgstr "Inserisci una frase chiave per calcolare il punteggio SEO"

#: languages/wordpress-seojs.php:17
msgid "Learn more about Cornerstone Content."
msgstr "Ulteriori informazioni sui contenuti Cornerstone."

#: languages/wordpress-seojs.php:14
msgid "Cornerstone content should be the most important and extensive articles on your site."
msgstr "I contenuti Cornerstone (contenuti centrali) dovrebbero essere gli articoli più importanti ed esaustivi del tuo sito."

#: admin/pages/network.php:20
msgid "Restore Site"
msgstr "Ripristina il sito web"

#: admin/menu/class-network-admin-menu.php:34
msgid "Network Settings"
msgstr "Impostazioni di rete"

#: admin/class-yoast-network-admin.php:263
msgid "You are not allowed to perform this action."
msgstr "Non sei autorizzato a eseguire questa azione."

#. translators: %s: error message
#: admin/class-yoast-network-admin.php:195
msgid "Error: %s"
msgstr "Errore: %s"

#. translators: %s: success message
#: admin/class-yoast-network-admin.php:193
msgid "Success: %s"
msgstr "Successo: %s"

#. translators: %s expands to the ID of a site within a multisite network.
#: admin/class-yoast-network-admin.php:155
msgid "Site with ID %d not found."
msgstr "Il sito web con l'ID %d non è stato trovato."

#: admin/class-yoast-network-admin.php:146
msgid "No site has been selected to restore."
msgstr "Nessun sito web è stato selezionato per il ripristino."

#: admin/class-yoast-network-admin.php:110
msgid "You are not allowed to modify unregistered network settings."
msgstr "Non sei autorizzato a modificare le impostazioni di rete non registrate."

#: admin/class-yoast-network-admin.php:81
msgid "deleted"
msgstr "eliminato"

#: admin/class-multiple-keywords-modal.php:21
msgid "Would you like to add another keyphrase?"
msgstr "Vorresti aggiungere un'altra frase chiave?"

#: languages/yoast-components.php:95
msgid "image preview"
msgstr "anteprima dell'immagine"

#: languages/yoast-components.php:29
msgid "Copied!"
msgstr "Copiato!"

#: languages/yoast-components.php:26
msgid "Not supported!"
msgstr "Non supportato!"

#: languages/yoast-components.php:23
msgid "Read {{a}}our article about site structure{{/a}} to learn more about how internal linking can help improve your SEO."
msgstr "Leggi {{a}} il nostro articolo sulla struttura del sito {{/a}} per imparare di più su come i link interni possono aiutarti a migliorare la tua strategia SEO."

#: languages/yoast-components.php:20
msgid "Once you add a bit more copy, we'll give you a list of related content here to which you could link in your post."
msgstr "Una volta aggiunto più contenuto, ti forniremo qui un elenco di contenuti correlati a cui potresti collegare il tuo articolo."

#: languages/yoast-components.php:14
msgid "Consider linking to these {{a}}cornerstone articles:{{/a}}"
msgstr "Prendi in considerazione di inserire il collegamento a questi {{a}}articoli cornerstone:{{/a}}"

#: languages/yoast-components.php:11
msgid "Consider linking to these articles:"
msgstr "Prendi in considerazione di inserire il collegamento a questi articoli:"

#: languages/yoast-components.php:8
msgid "Copy link"
msgstr "Copia il link"

#: languages/yoast-components.php:5
msgid "Copy link to suggested article: %s"
msgstr "Copia il link all'articolo suggerito: %s"

#: inc/class-wpseo-replace-vars.php:1327
msgid "The site's tagline"
msgstr "Tagline (motto) del sito"

#. translators: %1$s is the notification dismissal link start tag, %2$s is the
#. link closing tag.
#: admin/notifiers/class-post-type-archive-notification-handler.php:78
msgid "%1$sRemove this message%2$s"
msgstr "%1$sRimuovi questo messaggio%2$s"

#: admin/notifiers/class-post-type-archive-notification-handler.php:58
msgid "We've recently improved the functionality of the Search Appearance settings. Unfortunately, we've discovered that for some edge-cases, saving the settings for specific post type archives might have gone wrong."
msgstr "Di recente abbiamo migliorato la funzionalità delle impostazioni dell'Aspetto della ricerca. Purtroppo, abbiamo scoperto che in alcuni casi limite, il salvataggio per specifici archivi di post type potrebbe non andare a buon fine."

#. translators: %1$s expands to the missing field name.
#: admin/menu/class-replacevar-editor.php:113
msgid "Not all required fields are given. Missing field %1$s"
msgstr "Non sono stati riempiti tutti i campi obbligatori. Manca il campo %1$s"

#. translators: %s expands to Yoast SEO Premium.
#: admin/views/sidebar.php:133
msgid "Upgrade to %s »"
msgstr "Passa a %s »"

#: admin/views/sidebar.php:123
msgid "Optimize your site for Google News."
msgstr "Ottimizza il tuo sito per Google News."

#: admin/views/sidebar.php:113
msgid "Optimize your shop's SEO and sell more products!"
msgstr "Ottimizza la strategia SEO del tuo negozio e incrementa le vendite!"

#: admin/views/sidebar.php:103
msgid "Be found in Google Video search and enhance your video sharing on social media."
msgstr "Fatti trovare da Google Video Search e migliora la condivisione dei tuoi video sui social media."

#: admin/views/sidebar.php:93
msgid "Be found in Google Maps and local results."
msgstr "Fatti trovare su Google Maps e nelle ricerche locali."

#: admin/views/sidebar.php:85
msgid "Extend Yoast SEO"
msgstr "Estendi Yoast SEO"

#: admin/views/sidebar.php:80
msgid "Learn practical SEO skills to rank higher in Google."
msgstr "Impara nuovi trucchi SEO pratici per migliorare il tuo posizionamento su Google."

#: admin/views/sidebar.php:70
msgid "Don’t waste time figuring out the best settings yourself."
msgstr "Non sprecare il tuo tempo a capire da solo quali sono le impostazioni migliori."

#: admin/views/sidebar.php:55
msgid "Get quick wins to make your site rank higher in search engines."
msgstr "Ottieni utili suggerimenti per innalzare il tuo posizionamento del tuo sito sui motori di ricerca."

#: languages/wordpress-seojs.php:92
msgid "Add synonyms"
msgstr "Aggiungi sinonimi"

#: languages/wordpress-seojs.php:95 admin/class-keyword-synonyms-modal.php:21
msgid "Would you like to add keyphrase synonyms?"
msgstr "Vuoi aggiungere dei sinonimi della frase chiave?"

#: admin/formatter/class-metabox-formatter.php:188
#: admin/class-add-keyword-modal.php:21
msgid "Would you like to add more than one keyphrase?"
msgstr "Vuoi aggiungere più di una frase chiave?"

#. translators: %1$s expands to an opening anchor tag, %2$s expands to a
#. closing anchor tag.
#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:16
msgid "You can edit the SEO meta-data for this custom type on the %1$sShop page%2$s."
msgstr "Puoi modificare i meta-dati SEO pei questo tipo personalizzato sulla %1$spagina del Negozio%2$s."

#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:11
msgid "You haven't set a Shop page in your WooCommerce settings. Please do this first."
msgstr "Non hai impostato una pagina Negozio nelle impostazioni di WooCommerce. Devi farlo come prima cosa."

#: languages/wordpress-seojs.php:290
msgid "Current year"
msgstr "Anno corrente"

#: languages/yoast-components.php:235
msgid "Something went wrong. Please reload the page."
msgstr "Qualcosa non ha funzionato. Prova a ricaricare la pagina."

#: languages/yoast-components.php:223
msgid "Url preview"
msgstr "Anteprima dell'URL"

#: languages/yoast-components.php:217
msgid "Please provide a meta description by editing the snippet below. If you don’t, Google will try to find a relevant part of your post to show in the search results."
msgstr "Inserisci una descrizione meta editando lo snippet qui sotto. Se non lo fai, Google cercherà di indovinare dal tuo articolo cosa è rilevante per mostrarlo nei risultati di ricerca."

#: languages/yoast-components.php:185
msgid "Insert snippet variable"
msgstr "Inserisci una variabile"

#: languages/wordpress-seojs.php:305 inc/class-wpseo-replace-vars.php:1327
msgid "Tagline"
msgstr "Motto del sito"

#: languages/wordpress-seojs.php:299
msgid "Page"
msgstr "Pagina"

#: languages/wordpress-seojs.php:32 languages/yoast-components.php:202
msgid "Modify your meta description by editing it right here"
msgstr "Modifica la tua descrizione meta scrivendola qui"

#: inc/class-wpseo-replace-vars.php:1365
msgid "description (custom taxonomy)"
msgstr "descrizione (tassonomia personalizzata)"

#: inc/class-wpseo-replace-vars.php:1364
msgid "(custom taxonomy)"
msgstr "(tassonomia personalizzata)"

#: inc/class-wpseo-replace-vars.php:1363
msgid "(custom field)"
msgstr "(campo personalizzato)"

#: inc/class-wpseo-replace-vars.php:1362
msgid "Term404"
msgstr "Errore 404"

#: inc/class-wpseo-replace-vars.php:1360
msgid "Caption"
msgstr "Didascalia"

#: inc/class-wpseo-replace-vars.php:1359
msgid "Pagenumber"
msgstr "Numero di pagina"

#: inc/class-wpseo-replace-vars.php:1358
msgid "Pagetotal"
msgstr "Totale pagine"

#: inc/class-wpseo-replace-vars.php:1357
msgid "Page number"
msgstr "Numero di pagina"

#: inc/class-wpseo-replace-vars.php:1356
msgid "User description"
msgstr "Descrizione dell'utente"

#: languages/wordpress-seojs.php:296 inc/class-wpseo-replace-vars.php:1354
msgid "ID"
msgstr "ID"

#: inc/class-wpseo-replace-vars.php:1353
msgid "Modified"
msgstr "Modificato"

#: inc/class-wpseo-replace-vars.php:1352
msgid "Post type (plural)"
msgstr "Post type (plurale)"

#: inc/class-wpseo-replace-vars.php:1351
msgid "Post type (singular)"
msgstr "Post type (singolare)"

#: languages/wordpress-seojs.php:326 inc/class-wpseo-replace-vars.php:1338
msgid "Separator"
msgstr "Separatore"

#: languages/wordpress-seojs.php:302 inc/class-wpseo-replace-vars.php:1337
msgid "Search phrase"
msgstr "Frase di ricerca"

#: inc/class-wpseo-replace-vars.php:1336
msgid "Term title"
msgstr "Titolo del termine"

#: languages/wordpress-seojs.php:338 inc/class-wpseo-replace-vars.php:1335
msgid "Term description"
msgstr "Descrizione del termine"

#: languages/wordpress-seojs.php:335 inc/class-wpseo-replace-vars.php:1334
msgid "Tag description"
msgstr "Descrizione del tag"

#: languages/wordpress-seojs.php:332 inc/class-wpseo-replace-vars.php:1333
msgid "Category description"
msgstr "Descrizione della categoria"

#: languages/wordpress-seojs.php:323 inc/class-wpseo-replace-vars.php:1332
msgid "Primary category"
msgstr "Categoria primaria"

#: languages/wordpress-seojs.php:311 inc/class-wpseo-replace-vars.php:1331
msgid "Category"
msgstr "Categoria"

#: inc/class-wpseo-replace-vars.php:1330
msgid "Tag"
msgstr "Tag"

#: languages/wordpress-seojs.php:329 inc/class-wpseo-replace-vars.php:1329
msgid "Excerpt only"
msgstr "Solo riassunto"

#: languages/wordpress-seojs.php:320 inc/class-wpseo-replace-vars.php:1328
msgid "Excerpt"
msgstr "Riassunto"

#: languages/wordpress-seojs.php:308 inc/class-wpseo-replace-vars.php:1326
msgid "Site title"
msgstr "Titolo del sito"

#: inc/class-wpseo-replace-vars.php:1325
msgid "Archive title"
msgstr "Titolo dell'archivio"

#: languages/wordpress-seojs.php:317 inc/class-wpseo-replace-vars.php:1324
msgid "Parent title"
msgstr "Titolo genitore"

#: languages/wordpress-seojs.php:293 inc/class-wpseo-replace-vars.php:1322
msgid "Date"
msgstr "Data"

#: inc/class-wpseo-replace-vars.php:1058
msgid "Label"
msgstr "Etichetta"

#: admin/watchers/class-slug-change-watcher.php:217
msgid "You should create a redirect to ensure your visitors do not get a 404 error when they click on the no longer working URL."
msgstr "Dovresti creare un re-indirizzamento per assicurarti che i visitatori non incorrano in un errore 404 facendo clic su un URL non più funzionante."

#: admin/watchers/class-slug-change-watcher.php:216
msgid "Search engines and other websites can still send traffic to your deleted post."
msgstr "I motori di ricerca e altri siti web possono ancora inviare il traffico all'articolo eliminato."

#: admin/watchers/class-slug-change-watcher.php:213
msgid "Make sure you don't miss out on traffic!"
msgstr "Assicurati di non perdere traffico!"

#. translators: %1$s expands to the translated name of the post type.
#. translators: 1: term label
#: admin/watchers/class-slug-change-watcher.php:84
#: admin/watchers/class-slug-change-watcher.php:104
msgid "You just deleted a %1$s."
msgstr "Hai appena eliminato %1$s."

#. translators: %1$s expands to the translated name of the post type.
#: admin/watchers/class-slug-change-watcher.php:65
msgid "You just trashed a %1$s."
msgstr "Hai appena spostato nel cestino %1$s."

#. translators: %s is the plural version of the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:62
msgid "Breadcrumb settings for %s archive"
msgstr "Impostazioni dei breadcrumb per l'archivio %s"

#. translators: %s expands to the post type name.
#. translators: %s is the plural version of the post type's name.
#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:23
#: admin/views/tabs/metas/paper-content/post-type-content.php:32
msgid "Settings for %s archive"
msgstr "Impostazioni per l'archivio %s"

#. translators: %s is the singular version of the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:18
msgid "Settings for single %s URLs"
msgstr "Impostazioni per singoli URL di %s "

#: admin/views/tabs/metas/post-types.php:25
msgid "The settings on this page allow you to specify what the default search appearance should be for any type of content you have. You can choose which content types appear in search results and what their default description should be."
msgstr "Le impostazioni in questa pagina ti permettono di specificare quale sarà l'aspetto predefinito della ricerca per ciascuno tipo di contenuto che hai. Puoi scegliere quali tipi di contenuto fare apparire nei risultati di ricerca e quale sarà la loro descrizione predefinita."

#: languages/yoast-components.php:238
msgid "Dismiss this notice"
msgstr "Ignora questo avviso"

#: languages/yoast-components.php:191
msgid "No results"
msgstr "Nessun risultato"

#: languages/yoast-components.php:188
msgid "%d result found, use up and down arrow keys to navigate"
msgid_plural "%d results found, use up and down arrow keys to navigate"
msgstr[0] "È stato trovato %d risultato, usa i tasti freccia su e giù per navigare"
msgstr[1] "Sono stati trovati %d risultati, usa i tasti freccia su e giù per navigare"

#: languages/yoast-components.php:167
msgid "Your site language is set to %s. If this is not correct, contact your site administrator."
msgstr "La lingua del tuo sito è impostata su %s. Se non è corretto, contatta l'amministratore del sito."

#: languages/yoast-components.php:125
msgid "Number of results found: %d"
msgstr "Risultati trovati: %d"

#: admin/help_center/class-template-variables-tab.php:82
msgid "Note that not all variables can be used in every field."
msgstr "Non tutte le variabili possono essere utilizzate in ogni campo."

#: admin/help_center/class-template-variables-tab.php:59
#: admin/help_center/class-template-variables-tab.php:80
msgid "Snippet variables"
msgstr "Variabili dello Snippet"

#. translators: %1$s expands Yoast, %2$s expands to an opening anchor tag, %3$s
#. expands to a closing anchor tag.
#: admin/config-ui/fields/class-field-mailchimp-signup.php:31
msgid "%1$s respects your privacy. Read our %2$sprivacy policy%3$s on how we handle your personal information."
msgstr "%1$s rispetta la tua privacy. Leggi la nostra %2$sprivacy policy%3$s per sapere come vengono gestite le tue informazioni personali."

#. translators: %1$s dismiss link open tag, %2$s closes the link tag.
#: admin/class-admin-media-purge-notification.php:77
msgid "If you know what this means and you do not want to see this message anymore, you can %1$sdismiss this message%2$s."
msgstr "Se sai cosa significa e non vuoi più vedere questo messaggio, puoi %1$snon visualizzarlo più%2$s."

#. translators: %1$s expands to the link to the article, %2$s closes the link
#. tag.
#: admin/class-admin-media-purge-notification.php:69
msgid "Your site's settings currently allow attachment URLs on your site to exist. Please read %1$sthis post about a potential issue%2$s with attachment URLs and check whether you have the correct setting for your site."
msgstr "Attualmente le impostazioni del tuo sito permettono la presenza degli URL degli allegati presenti nel tuo sito. Leggi %1$s questo articolo su un potenziale problema %2$s con gli URL degli allegati e verifica se hai le impostazioni corrette per il tuo sito."

#. translators: %1$s expands to the requested indexable type
#: admin/services/class-indexable.php:92
msgid "Unknown type %1$s"
msgstr "Tipo sconosciuto %1$s"

#: admin/config-ui/fields/class-field-mailchimp-signup.php:41
msgid "Includes a free MyYoast account which gives you access to our free SEO for Beginners course!"
msgstr "Include un account MyYoast gratuito che ti consentirà di accedere al nostro corso SEO gratuito per Principianti!"

#. translators: %1$s expands to Yoast SEO for WordPress, %2$s expands to Yoast
#: admin/config-ui/fields/class-field-mailchimp-signup.php:24
msgid "Sign up for our newsletter if you would like to keep up-to-date about %1$s, other cool plugins by %2$s, and interesting news and tips from the world of SEO."
msgstr "Iscriviti alla nostra newsletter se desideri rimanere aggiornato su %1$s, sugli altri fantastici plugin di %2$s e avere altre notizie interessanti e suggerimenti dal mondo dela strategia SEO."

#. translators: %1$s expands to a link start tag to the Baidu Webmaster Tools
#. site add page, %2$s is the link closing tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:42
msgid "Get your Baidu verification code in %1$sBaidu Webmaster Tools%2$s."
msgstr "Puoi ottenere il codice di verifica Baidu su %1$sBaidu Webmaster Tools%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:38
msgid "Baidu verification code"
msgstr "Codice di verifica di Baidu"

#. translators: %s is replaced with Yoast SEO.
#: admin/import/plugins/class-abstract-plugin-importer.php:254
msgid "The %s importer functionality uses temporary database tables. It seems your WordPress install does not have the capability to do this, please consult your hosting provider."
msgstr "La funzione %s di importazione usa tabelle temporanee di database. Sembra che la tua installazione di WordPress non sia in grado di gestirle, consulta il tuo provider."

#. translators: %s is replaced with the plugin's name.
#: admin/import/plugins/class-abstract-plugin-importer.php:133
msgid "Cleanup of %s data failed."
msgstr "La pulizia dati di %s non è andata a buon fine."

#: admin/class-bulk-editor-list-table.php:984
msgid "Content Type"
msgstr "Tipo di contenuto"

#: admin/class-bulk-editor-list-table.php:401
msgid "Filter by content type"
msgstr "Filtra per tipo di contenuto"

#: admin/class-bulk-editor-list-table.php:384
msgid "Show All Content Types"
msgstr "Mostra tutti i tipi di contenuto"

#. translators: %1$s resolves to the opening tag of the link to the comment
#. setting page, %2$s resolves to the closing tag of the link
#: admin/class-admin-init.php:169
msgid "To fix this uncheck the box in front of the \"Break comments into pages...\" on the %1$sComment settings page%2$s."
msgstr "Per risolvere il problema, deseleziona il box \"Dividi i commenti in pagine...\" nella %1$spagina di impostazione dei commenti%2$s."

#: inc/class-wpseo-replace-vars.php:1325
msgid "Replaced with the normal title for an archive generated by WordPress"
msgstr "Sostituisci con il titolo normale per un archivio generato da WordPress"

#: admin/views/tabs/tool/import-seo.php:108
msgid "Clean"
msgstr "Pulisci"

#: admin/views/tabs/tool/import-seo.php:99
msgid "Once you're certain your site is OK, you can clean up. This will remove all the original data."
msgstr "Una volta che sei sicuro che il tuo sito sia a posto, puoi pulire. Questo rimuoverà tutti i dati originali."

#: admin/views/tabs/tool/import-seo.php:97
msgid "Step 5: Clean up"
msgstr "Passaggio 5: Pulisci"

#: admin/views/tabs/tool/import-seo.php:88
msgid "You should run the configuration wizard, from the SEO &rarr; General &rarr; Dashboard page, to make sure all the settings for your site are correct."
msgstr "Avvia la procedura guidata, dal menu Yoast SEO &rarr; Generale &rarr; Bacheca per essere sicuro che tutte le impostazioni per il tuo sito siano corrette."

#: admin/views/tabs/tool/import-seo.php:84
msgid "Step 4: Run the configuration wizard"
msgstr "Passaggio 4: Esegui la procedura guidata di configurazione"

#: admin/views/tabs/tool/import-seo.php:79
msgid "Please check your posts and pages and see if the metadata was successfully imported."
msgstr "Controlla le tue pagine e articoli e verifica che i metadata sono stati importati con successo."

#: admin/views/tabs/tool/import-seo.php:77
msgid "Step 3: Check your data"
msgstr "Passaggio 3: Verifica i tuoi dati"

#: admin/views/tabs/tool/import-seo.php:60
msgid "This will import the post metadata like SEO titles and descriptions into your Yoast SEO metadata. It will only do this when there is no existing Yoast SEO metadata yet. The original data will remain in place."
msgstr "Questo importerà i metadata degli articoli come i titoli SEO e le descrizioni nel tuo Yoast SEO metadata. Esegui l'azione solo se non ci sono ancora i metadati di Yoast SEO. I dati esistenti non andranno persi."

#: admin/views/tabs/tool/import-seo.php:58
msgid "Step 2: Import"
msgstr "Passaggio 2: Importa"

#: admin/views/tabs/tool/import-seo.php:53
msgid "Please make a backup of your database before starting this process."
msgstr "Fai un backup del database prima di iniziare questo processo."

#: admin/views/tabs/tool/import-seo.php:51
msgid "Step 1: Create a backup"
msgstr "Passaggio 1: Crea un backup"

#: admin/views/tabs/tool/import-seo.php:47
msgid "We've detected data from one or more SEO plugins on your site. Please follow the following steps to import that data:"
msgstr "Abbiamo trovato uno o più dati appartenenti ad altri plugin di SEO nel tuo sito. Segui i seguenti passi per importare i dati."

#: admin/views/tabs/tool/import-seo.php:35
msgid "Plugin: "
msgstr "Plugin:"

#: admin/views/tabs/tool/import-seo.php:20
msgid "Yoast SEO did not detect any plugin data from plugins it can import from."
msgstr "Yoast SEO non ha trovato nessun dato dai plugin che possa essere importato."

#: admin/statistics/class-statistics-service.php:210
msgid "Posts that should not show up in search results"
msgstr "Articoli da non mostrare nei risultati delle ricerche"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:209
msgid "Posts with the SEO score: %1$sgood%2$s"
msgstr "Articoli con punteggio SEO: %1$sBuono%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:207
msgid "Posts with the SEO score: %1$sOK%2$s"
msgstr "Articoli con punteggio SEO: %1$sOK%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:205
msgid "Posts with the SEO score: %1$sneeds improvement%2$s"
msgstr "Articoli con punteggio SEO: %1$sda migliorare%2$s"

#. translators: %s is replaced with the name of the plugin we've found data
#. from.
#: admin/import/class-import-status.php:128
msgid "%s data found."
msgstr "Dati trovati da %s."

#. translators: %s is replaced with the name of the plugin we're removing data
#. from.
#: admin/import/class-import-status.php:124
msgid "%s data successfully removed."
msgstr "%s dati rimossi con successo"

#. translators: %s is replaced with the name of the plugin we're importing data
#. from.
#: admin/import/class-import-status.php:121
msgid "%s data successfully imported."
msgstr "Dati importati con successo da %s."

#. translators: %s is replaced with the name of the plugin we're trying to find
#. data from.
#: admin/import/class-import-status.php:61
msgid "%s data not found."
msgstr "Non sono stati trovati dati da %s."

#: admin/views/sidebar.php:46
msgid "Improve your SEO skills"
msgstr "Migliora le tue competenze SEO."

#: admin/views/sidebar.php:33
msgid "No ads"
msgstr "Nessuna pubblicità"

#: admin/views/sidebar.php:32
msgid "24/7 email support"
msgstr "Supporto 24/7 via email"

#: admin/views/sidebar.php:31
msgid "No more dead links a.k.a. 404 pages"
msgstr "Più nessun errore 404 (pagina non trovata)"

#: admin/views/sidebar.php:30
msgid "Get real-time suggestions for internal links"
msgstr "Ottieni in tempo reale suggerimenti per i link interni"

#: admin/views/sidebar.php:29
msgid "Preview your page in Facebook and Twitter"
msgstr "Anteprima della tua pagina su Facebook e Twitter"

#. translators: %1$s expands to the plugin name
#: admin/views/sidebar.php:24
msgid "Upgrade to %1$s"
msgstr "Passa a %1$s"

#: admin/views/user-profile.php:15
msgid "this author's archives"
msgstr "archivi di questo autore"

#. translators: %s expands to "this author's archives".
#: admin/views/user-profile.php:14
msgid "Do not allow search engines to show %s in search results."
msgstr "Non consentire ai motori di ricerca di mostrare %s nei risultati di ricerca."

#. translators: 1: link open tag; 2: link close tag., 3: the translated label
#. of the button
#: admin/views/tabs/social/facebook.php:55
msgid "Click the \"%3$s\" button to use the meta description already set in the %1$sSearch Appearance Homepage%2$s setting."
msgstr "Fai clic sul pulsante \"%3$s\" per utilizzare la meta descrizione già impostata nelle Impostazioni dell'%1$sAspetto della ricerca per la Homepage%2$s."

#: admin/views/tabs/social/facebook.php:52
msgid "Help on copying the home meta description"
msgstr "Aiuto per scrivere la meta descrizione della home"

#: admin/views/tabs/social/accounts.php:19
msgid "To let search engines know which social profiles are associated to this site, enter your site social profiles data below."
msgstr "Per consentire ai motori di ricerca di sapere quali profili social sono associati a questo sito, inserisci i dati dei profili social del tuo sito qui sotto."

#: admin/views/tabs/social/accounts.php:18
msgid "Learn more about your social profiles settings"
msgstr "Leggi di più sulle impostazioni dei tuoi profili sui social network."

#: admin/views/tabs/metas/taxonomies/category-url.php:24
msgid "Remove the categories prefix"
msgstr "Rimuovi il prefisso delle categorie"

#. translators: %s expands to <code>/category/</code>
#: admin/views/tabs/metas/taxonomies/category-url.php:17
msgid "Category URLs in WordPress contain a prefix, usually %s, this feature removes that prefix, for categories only."
msgstr "Gli URL di categoria all'interno di WordPress usano un prefisso, solitamente %s. Questa funzione rimuove quel prefisso, soltanto dalle categorie."

#: admin/views/tabs/metas/taxonomies/category-url.php:14
msgid "Help on the category prefix setting"
msgstr "Aiuto per le impostazioni del prefisso delle categorie"

#: admin/views/tabs/metas/taxonomies.php:48
msgid "Category URLs"
msgstr "URL delle categorie."

#: admin/views/tabs/metas/paper-content/rss-content.php:19
msgid "Learn more about the available variables"
msgstr "Ulteriori informazioni sulle variabili disponibili"

#: admin/views/tabs/metas/rss.php:20
msgid "Learn more about the RSS feed setting"
msgstr "Ulteriori informazioni sull'impostazione del feed RSS"

#. translators: %s expands to the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:40
msgid "the archive for %s"
msgstr "l'archivio per %s"

#: admin/views/tabs/metas/paper-content/media-content.php:25
msgid "Redirect attachment URLs to the attachment itself?"
msgstr "Reindirizzare gli URL degli allegati all'allegato stesso?"

#: admin/views/tabs/metas/paper-content/media-content.php:15
msgid "We recommend you set this to Yes."
msgstr "Ti consigliamo di impostarlo su Sì."

#: admin/views/tabs/metas/media.php:15
msgid "Media & attachment URLs"
msgstr "URL dei Media e degli allegati"

#: admin/views/tabs/metas/media.php:21
msgid "When you upload media (an image or video for example) to WordPress, it doesn't just save the media, it creates an attachment URL for it. These attachment pages are quite empty: they contain the media item and maybe a title if you entered one. Because of that, if you never use these attachment URLs, it's better to disable them, and redirect them to the media item itself."
msgstr "Quando carichi contenuti multimediali (un'immagine o un video ad esempio), WordPress non salva solo i file multimediali, ma crea un URL di allegato. Queste pagine di allegati sono abbastanza vuote: contengono l'elemento multimediale e forse un titolo, se ne inserisci uno. Per questo motivo, se non usi mai questi URL degli allegati, è meglio disabilitarli e reindirizzarlili all'elemento multimediale stesso."

#: admin/views/tabs/metas/media.php:20
msgid "Learn more about the Media and attachment URLs setting"
msgstr "Ulteriori informazioni sull'impostazione degli URL dei Media e degli allegati"

#: admin/views/tabs/social/facebook.php:34
#: admin/views/tabs/metas/paper-content/general/title-separator.php:12
msgid "Learn more about the title separator setting"
msgstr "Ulteriori informazioni sull'impostazione del separatore del titolo"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:12
msgid "Learn more about the knowledge graph setting"
msgstr "Ulteriori informazioni sull'impostazione del knowledge graph"

#: admin/views/tabs/metas/paper-content/general/homepage.php:17
msgid "This is what shows in the search results when people find your homepage. This means this is probably what they see when they search for your brand name."
msgstr "Questo è ciò che viene mostrato nei risultati della ricerca quando le persone trovano la tua homepage. Ciò significa che probabilmente è quello che vedono quando cercano il tuo marchio."

#: admin/views/tabs/metas/paper-content/general/homepage.php:16
msgid "Learn more about the homepage setting"
msgstr "Ulteriori informazioni sull'impostazione della homepage"

#: admin/views/tabs/metas/paper-content/special-pages.php:12
msgid "Learn more about the special pages setting"
msgstr "Ulteriori informazioni sull'impostazione delle pagine speciali"

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:36
msgid "date archives"
msgstr "archivi per data"

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:24
msgid "Help on the date archives search results setting"
msgstr "Aiuto per le impostazioni dei motori di ricerca degli archivi per data"

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:61
msgid "archives for authors without posts"
msgstr "archivi per gli autori senza articoli"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:52
msgid "Not showing the archives for authors without posts in the search results technically means those will have a %1$s robots meta and will be excluded from XML sitemaps. %2$sMore info on the search results settings%3$s."
msgstr "Non mostrare gli archivi per gli autori senza articoli nei risultati di ricerca tecnicamente significa che quelli avranno una meta robot %1$s e saranno esclusi dalle sitemap XML. %2$sLeggi di più sulle impostazioni dei risultati di ricerca%3$s."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:49
msgid "Help on the authors without posts archive search results setting"
msgstr "Aiuto per le impostazioni dei risultati di ricerca per gli autori senza un archivio degli articoli"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:29
msgid "Not showing the archive for authors in the search results technically means those will have a %1$s robots meta and will be excluded from XML sitemaps. %2$sMore info on the search results settings%3$s."
msgstr "Non mostrare gli archivi per gli autori nei risultati di ricerca tecnicamente significa che quelli avranno una meta robot %1$s e saranno esclusi dalle sitemap XML. %2$sLeggi di più sulle impostazioni dei risultati di ricerca%3$s."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:26
msgid "Help on the author archives search results setting"
msgstr "Aiuto per l'impostazione dei risutati della ricerca per gli archivi degli autori"

#: admin/views/tabs/metas/archives/help.php:31
msgid "Archives settings help"
msgstr "Guida alle impostazioni degli archivi"

#: admin/views/tabs/metas/archives/help.php:26
msgid "Learn more about the archives setting"
msgstr "Ulteriori informazioni sull'impostazione degli archivi"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:77
msgid "Get your Yandex verification code in %1$sYandex Webmaster Tools%2$s."
msgstr "Acquisisci il tuo codice di verifica Yandex da %1$sYandex Webmaster Tools%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:73
msgid "Yandex verification code"
msgstr "Codice di verifica Yandex"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:67
msgid "Get your Google verification code in %1$sGoogle Search Console%2$s."
msgstr "Acquisisci il codice di verifica Google da %1$sGoogle Search Console%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:63
msgid "Google verification code"
msgstr "Codice di verifica Google"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:57
msgid "Get your Bing verification code in %1$sBing Webmaster Tools%2$s."
msgstr "Acquisisci il codice di verifica Bing da %1$sBing Webmaster Tools%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:53
msgid "Bing verification code"
msgstr "Codice di verifica Bing"

#: admin/views/tabs/dashboard/webmaster-tools.php:19
msgid "You can use the boxes below to verify with the different Webmaster Tools. This feature will add a verification meta tag on your home page. Follow the links to the different Webmaster Tools and look for instructions for the meta tag verification method to get the verification code. If your site is already verified, you can just forget about these."
msgstr "Puoi usare i box qui sotto per fare la verifica con i differenti Webmaster Tools. Questa funzione aggiungerà un meta tag di verifica sulla tua home page. Segui i links per i differenti Webmaster Tools e leggi le istruzioni per i metodi di verifica dei meta tag per ottenere il tuo codice di verifica. Se il tuo sito è già stato verificato, puoi lasciare tutto così com'è."

#: admin/views/tabs/dashboard/webmaster-tools.php:18
msgid "Learn more about the Webmaster Tools verification"
msgstr "Leggi di più sulla verifica con i Webmaster Tools"

#: languages/yoast-components.php:173
#: deprecated/class-recalibration-beta.php:61
#: admin/views/tabs/dashboard/features.php:52
msgid "On"
msgstr "On"

#. translators: %s expands to a feature's name
#: admin/views/tabs/dashboard/features.php:45
#: admin/views/tabs/network/features.php:45
msgid "Help on: %s"
msgstr "Aiuto su: %s"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/features.php:24
msgid "%1$s comes with a lot of features. You can enable / disable some of them below. Clicking the question mark gives more information about the feature."
msgstr "%1$s ha molte caratteristiche. Qui sotto, puoi abilitare - disabilitare alcune di queste. Facendo cic sul punto interrogativo, potrai avere più informazioni sulle caratteristiche."

#: languages/yoast-components.php:176
#: deprecated/class-recalibration-beta.php:62
#: admin/views/tabs/dashboard/features.php:53
#: admin/views/class-yoast-feature-toggles.php:138
msgid "Off"
msgstr "Off"

#. translators: 1: Yoast SEO, 2: translated version of "Off"
#: admin/views/class-yoast-feature-toggles.php:136
msgid "The advanced section of the %1$s meta box allows a user to remove posts from the search results or change the canonical. These are things you might not want any author to do. That's why, by default, only editors and administrators can do this. Setting to \"%2$s\" allows all users to change these settings."
msgstr "La sezione avanzata della %1$s meta box consente a un utente di rimuovere gli articoli dai risultati della ricerca o di modificare il canonical. Queste sono cose che potresti volere impedire ad un autore. Ecco perché, per impostazione predefinita, solo gli editor e gli amministratori possono farlo. L'impostazione su \"%2$s\" consente a tutti gli utenti di modificare queste impostazioni."

#: admin/views/class-yoast-feature-toggles.php:132
msgid "Security: no advanced settings for authors"
msgstr "Sicurezza: nessuna impostazione avanzata per l'autore"

#: admin/views/class-yoast-feature-toggles.php:108
msgid "Read why XML Sitemaps are important for your site."
msgstr "Leggi perché gli le sitemap XML sono importanti per il tuo sito."

#. translators: %s: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:107
msgid "Enable the XML sitemaps that %s generates."
msgstr "Abilita le sitemap XML generate da %s."

#: admin/views/class-yoast-feature-toggles.php:67
msgid "See the XML sitemap."
msgstr "Vedi la mappa XML del sito."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/dashboard.php:16
msgid "See who contributed to %1$s."
msgstr "Guarda chi ha contribuito a %1$s."

#: admin/views/class-view-utils.php:54
msgid "Help on this search results setting"
msgstr "Aiuto per il settaggio di questi risultati di ricerca."

#. translators: 1: expands to an indexable object's name, like a post type or
#. taxonomy; 2: expands to <code>noindex</code>; 3: link open tag; 4: link
#. close tag.
#: admin/views/class-view-utils.php:48
msgid "Not showing the archive for %1$s in the search results technically means those will have a %2$s robots meta and will be excluded from XML sitemaps. %3$sMore info on the search results settings%4$s."
msgstr "Non mostrare l'archivio per %1$s nei risultati di ricerca tecnicamente significa che avranno meta robot %2$s e saranno esclusi dalle sitemap XML. %3$sLeggi di più sulle impostazioni dei risultati di ricerca%4$s."

#. translators: 1: expands to an indexable object's name, like a post type or
#. taxonomy; 2: expands to <code>noindex</code>; 3: link open tag; 4: link
#. close tag.
#: admin/views/class-view-utils.php:44
msgid "Not showing %1$s in the search results technically means those will have a %2$s robots meta and will be excluded from XML sitemaps. %3$sMore info on the search results settings%4$s."
msgstr "La mancata visualizzazione di %1$s nei risultati di ricerca significa tecnicamente che avranno un meta  robot %2$s e saranno esclusi dalle sitemap XML. %3$sLeggi di più sulle impostazioni dei risultati di ricerca %4$s."

#. translators: %1$s expands to the taxonomy name %2$s expands to the current
#. taxonomy index value
#: admin/taxonomy/class-taxonomy-settings-fields.php:69
msgid "%2$s (current default for %1$s)"
msgstr "%2$s (impostazione corrente predefinita per %1$s)"

#: admin/pages/metas.php:23
msgid "Media"
msgstr "Media"

#: admin/pages/metas.php:22
msgid "Content Types"
msgstr "Tipi di contenuto"

#: admin/notifiers/class-configuration-notifier.php:111
msgid "Check SEO configuration"
msgstr "Controlla la configurazione SEO"

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. Onboarding Wizard, %3$s is the link closing tag.
#: admin/notifiers/class-configuration-notifier.php:105
msgid "Want to make sure your %1$s settings are still OK? %2$sOpen the configuration wizard again%3$s to validate them."
msgstr "Vuoi essere sicuro che le impostazioni del tuo %1$s siano ancora OK? %2$sApri di nuovo il Wizard della configurazione%3$s per validarlo."

#. translators: %1$s expands to the post type name.
#: admin/metabox/class-metabox.php:80
msgid "Should search engines follow links on this %1$s?"
msgstr "I motori di ricerca dovrebbero seguire i link per questo %1$s?"

#. translators: %1$s expands to Yes or No,  %2$s expands to the post type name.
#: admin/metabox/class-metabox.php:75
msgid "Default for %2$s, currently: %1$s"
msgstr "Predefinito per %2$s, attualmente: %1$s"

#. translators: %s expands to the post type name.
#. translators: %s = taxonomy name.
#: admin/metabox/class-metabox.php:70
#: admin/taxonomy/class-taxonomy-settings-fields.php:38
msgid "Allow search engines to show this %s in search results?"
msgstr "Consenti ai motori di ricerca di mostrare %s nei risultati delle ricerche?"

#: frontend/class-frontend.php:1176 admin/menu/class-admin-menu.php:89
msgid "Search Appearance"
msgstr "Aspetto della ricerca"

#: admin/config-ui/fields/class-field-post-type-visibility.php:19
msgid "Please specify what content types you would like to appear in search engines. If you do not know the differences between these, it's best to choose the default settings."
msgstr "Specifica quali tipi di contenuti desideri visualizzare nei motori di ricerca. Se non conosci le differenze tra questi, è meglio scegliere le impostazioni predefinite."

#. Translators: %1$s expands to the name of the post type. The options given to
#. the user are "visible" and "hidden"
#: admin/config-ui/fields/class-field-choice-post-type.php:32
msgid "Search engines should show \"%1$s\" in search results:"
msgstr "I motori di ricerca dovrebbero mostrare \"%1$s\" nel risultato della ricerca:"

#: admin/config-ui/class-configuration-structure.php:79
msgid "Search engine visibility"
msgstr "Visibilità nei motori di ricerca"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#: admin/class-yoast-form.php:708
msgid "Show %s in search results?"
msgstr "Vuoi mostrare %s nei risultati di ricerca?"

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:157
msgid "Toggle %1$s's XML Sitemap"
msgstr "Attiva la Sitemap XML di %1$s"

#: admin/views/tabs/social/twitterbox.php:22
msgid "Enable this feature if you want Twitter to display a preview with images and a text excerpt when a link to your site is shared."
msgstr "Abilita questa funzione se vuoi che Twitter mostri un'anteprima con le immagini ed un riassunto del testo quando un link del tuo sito viene condiviso."

#: admin/class-help-center.php:260
msgid "Search result"
msgstr "Risultato di ricerca"

#: admin/views/class-yoast-feature-toggles.php:72
msgid "SEO analysis"
msgstr "Analisi SEO"

#: admin/views/class-yoast-feature-toggles.php:98
msgid "The text link counter helps you improve your site structure."
msgstr "Il contatore di link nel testo ti aiuta a migliorare la struttura del tuo sito."

#: admin/class-help-center.php:138
msgid "If you have a problem that you can't solve with our video tutorials or knowledge base, you can send a message to our support team. They can be reached 24/7."
msgstr "Se hai un problema che non riesci a risolvere con i nostri video tutorial o con gli articoli della sezione knowledge base sul sito, puoi inviare un messaggio al nostro team di supporto, che è attivo 24/7."

#: admin/class-help-center.php:142
msgid "Support requests you create here are sent directly into our support system, which is secured with 256 bit SSL, so communication is 100% secure."
msgstr "Le richieste di supporto che crei qui sono inviate direttamente al nostro sistema di supporto, la cui sicurezza è garantita con SSL a 256 bit, pertanto le comunicazioni sono sicure al 100%."

#: admin/class-help-center.php:269
msgid "New support request"
msgstr "Nuova richiesta di supporto"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag.
#: admin/formatter/class-metabox-formatter.php:84
msgid "Readability: %1$sNot available%2$s"
msgstr "Leggibilità: %1$snon disponibile%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag.
#: admin/formatter/class-metabox-formatter.php:90
msgid "Readability: %1$sNeeds improvement%2$s"
msgstr "Leggibilità: %1$snecessita di miglioramento%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag.
#: admin/formatter/class-metabox-formatter.php:96
msgid "Readability: %1$sOK%2$s"
msgstr "Leggibilità: %1$sOK%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag.
#: admin/formatter/class-metabox-formatter.php:102
msgid "Readability: %1$sGood%2$s"
msgstr "Leggibilità: %1$sBuona%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag.
#: admin/formatter/class-metabox-formatter.php:110
msgid "SEO: %1$sNot available%2$s"
msgstr "SEO: %1$snon disponibile%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag.
#: admin/formatter/class-metabox-formatter.php:116
msgid "SEO: %1$sNeeds improvement%2$s"
msgstr "SEO: %1$snecessita di miglioramento%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag.
#: admin/formatter/class-metabox-formatter.php:122
msgid "SEO: %1$sOK%2$s"
msgstr "SEO: %1$sOK%2$s"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag.
#: admin/formatter/class-metabox-formatter.php:128
msgid "SEO: %1$sGood%2$s"
msgstr "SEO: %1$sBuono%2$s"

#: languages/yoast-components.php:152
#: admin/formatter/class-metabox-formatter.php:168
msgid "Good results"
msgstr "Risultati buoni"

#: admin/views/licenses.php:43
msgid "Get better search results in local search"
msgstr "Ottieni risultati migliori nella ricerca locale"

#. translators: %1$s expands to WooCommerce
#: admin/views/licenses.php:46
msgid "Allow customers to pick up their %s order locally"
msgstr "Consenti ai clienti di ritirare il loro ordine %s a livello locale"

#: admin/views/class-yoast-feature-toggles.php:74
msgid "The SEO analysis offers suggestions to improve the SEO of your text."
msgstr "L'analisi SEO offre suggerimenti per migliorare la strategia SEO del tuo testo."

#: admin/views/class-yoast-feature-toggles.php:75
msgid "Learn how the SEO analysis can help you rank."
msgstr "Scopri come l'analisi SEO può aiutare il tuo posizionamento."

#: admin/views/class-yoast-feature-toggles.php:82
msgid "The readability analysis offers suggestions to improve the structure and style of your text."
msgstr "L'analisi di leggibilità offre suggerimenti per migliorare la struttura e lo stile del testo."

#: admin/views/class-yoast-feature-toggles.php:83
msgid "Discover why readability is important for SEO."
msgstr "Scopri perché la leggibilità è importante per la strategia SEO."

#: admin/views/class-yoast-feature-toggles.php:90
msgid "The cornerstone content feature lets you to mark and filter cornerstone content on your website."
msgstr "La funzione del contenuto cornerstone ti consente di contrassegnare e filtrare i contenuti cornerstone sul tuo sito web."

#: admin/views/class-yoast-feature-toggles.php:91
msgid "Find out how cornerstone content can help you improve your site structure."
msgstr "Scopri come i contenuti cornerstone possono aiutarti a migliorare la struttura del tuo sito."

#: admin/views/class-yoast-feature-toggles.php:99
msgid "Find out how the text link counter can enhance your SEO."
msgstr "Scopri come il contatore di link di testo può migliorare la stategia SEO."

#. translators: %s: Ryte
#: admin/views/class-yoast-feature-toggles.php:115
msgid "%s integration"
msgstr "%s integrazione"

#. translators: %s: Ryte
#: admin/views/class-yoast-feature-toggles.php:120
msgid "Read more about how %s works."
msgstr "Ulteriori informazioni su come funziona %s."

#: admin/views/tabs/social/facebook.php:23
msgid "Enable this feature if you want Facebook and other social media to display a preview with images and a text excerpt when a link to your site is shared."
msgstr "Abilita questa funzione se vuoi che Facebook e altri social media visualizzino un'anteprima con immagini e un riassunto di testo quando viene condiviso un link al tuo sito."

#. translators: %s expands to Yoast SEO Premium.
#: admin/class-social-admin.php:186
msgid "Find out why you should upgrade to %s"
msgstr "Scopri perché dovresti passare a %s"

#. Author URI of the plugin
msgid "https://yoa.st/1uk"
msgstr "https://yoa.st/1uk"

#. Plugin URI of the plugin
msgid "https://yoa.st/1uj"
msgstr "https://yoa.st/1uj"

#: admin/metabox/class-metabox.php:275
#: admin/taxonomy/class-taxonomy-metabox.php:71
msgid "Meta box"
msgstr "Meta box"

#. translators: %1$s resolves to Yoast.com
#: admin/class-yoast-dashboard-widget.php:110
msgid "Latest blog posts on %1$s"
msgstr "Post recenti sul blog %1$s"

#: languages/yoast-components.php:158 admin/class-help-center.php:270
msgid "Need help?"
msgstr "Ti serve aiuto?"

#: languages/yoast-components.php:116 admin/class-help-center.php:253
msgid "Type here to search..."
msgstr "Inserisci qui il termine da ricercare..."

#: languages/yoast-components.php:113 admin/class-help-center.php:252
msgid "Search the Yoast Knowledge Base for answers to your questions:"
msgstr "Cerca risposte alle tue domande nella Yoast Knowledge Base:"

#: languages/yoast-components.php:134
#: admin/formatter/class-metabox-formatter.php:172
msgid "Remove highlight from the text"
msgstr "Rimuovi evidenziazione nel testo"

#: admin/formatter/class-metabox-formatter.php:169
msgid "Your site language is set to {language}."
msgstr "La lingua del tuo sito è {language}."

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. Onboarding Wizard, %3$s is the link closing tag.
#: admin/notifiers/class-configuration-notifier.php:122
msgid "Get started quickly with the %1$s %2$sconfiguration wizard%3$s!"
msgstr "Inizia subito con la %2$sconfigurazione guidata%3$s di %1$s!"

#: admin/notifiers/class-configuration-notifier.php:128
msgid "First-time SEO configuration"
msgstr "Configurazione SEO iniziale"

#: admin/formatter/class-metabox-formatter.php:170
msgid "Your site language is set to {language}. If this is not correct, contact your site administrator."
msgstr "La lingua del tuo sito è {language}. Se ciò non è corretto, contatta l'amministratore del tuo sito."

#: languages/yoast-components.php:164
msgid "Your site language is set to %s. "
msgstr "La lingua del tuo sito è impostata su %s."

#: languages/yoast-components.php:137
#: admin/formatter/class-metabox-formatter.php:171
msgid "Highlight this result in the text"
msgstr "Evidenzia questo risultato nel testo"

#: languages/yoast-components.php:149
#: admin/formatter/class-metabox-formatter.php:167
msgid "Considerations"
msgstr "Considerazioni"

#: languages/yoast-components.php:140
#: admin/formatter/class-metabox-formatter.php:164
msgid "Errors"
msgstr "Errori"

#: languages/yoast-components.php:170
#: admin/formatter/class-metabox-formatter.php:163
msgid "Change language"
msgstr "Cambia lingua"

#. translators: %s expands to the number of results found .
#: admin/class-help-center.php:264
msgid "Number of results found: %s"
msgstr "Numero di risultati trovati: %s"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:22
msgid "You cannot create a %s file."
msgstr "Non è possibile creare un file %s."

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:43 admin/views/tool-file-editor.php:70
msgid "You cannot edit the %s file."
msgstr "Non è possibile modificare il file %s."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:59
msgid "Updated %s"
msgstr "%s aggiornato"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:131
msgid "Create %s file"
msgstr "Crea file %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:171 admin/views/tool-file-editor.php:221
msgid "Edit the content of your %s:"
msgstr "Modifica il contenuto del tuo %s:"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:180 admin/views/tool-file-editor.php:230
msgid "Save changes to %s"
msgstr "Salva le modifiche al %s"

#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:192
msgid "%s file"
msgstr "File %s"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the dependency name,
#. %3$s expands to the install link, %4$s expands to the more info link.
#: admin/class-suggested-plugins.php:109
msgid "%1$s and %2$s can work together a lot better by adding a helper plugin. Please install %3$s to make your life better. %4$s."
msgstr "%1$s e %2$s possono lavorare insieme molto meglio con l'aggiunta di un plugin helper. Installa %3$s per rendere la tua vita migliore. %4$s."

#: admin/class-suggested-plugins.php:135
msgid "More information"
msgstr "Ulteriori informazioni"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the dependency name,
#. %3$s expands to activation link.
#: admin/class-suggested-plugins.php:149
msgid "%1$s and %2$s can work together a lot better by adding a helper plugin. Please activate %3$s to make your life better."
msgstr "%1$s e %2$s possono lavorare insieme molto meglio con l'aggiunta di un plugin helper. Attiva %3$s per rendere la tua vita migliore."

#. translators: %1$s expands to the dependency name.
#: admin/class-suggested-plugins.php:134
msgid "More information about %1$s"
msgstr "Ulteriori informazioni su %1$s"

#: inc/class-wpseo-admin-bar-menu.php:210
msgid "Configuration Wizard"
msgstr "Configurazione guidata"

#: admin/config-ui/fields/class-field-success-message.php:25
msgid "You've done it!"
msgstr "Hai fatto!"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:118
msgid "Seamlessly integrate %1$s into your AMP pages!"
msgstr "Integra al meglio %1$s nelle tue pagine AMP!"

#. translators: %1$s expands to Yoast SEO, %2$s expands to Advanced Custom
#. Fields
#: admin/class-plugin-availability.php:99
msgid "Seamlessly integrate %2$s with %1$s for the content analysis!"
msgstr "Integra al meglio %2$s con %1$s per l'analisi del contenuto!"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the plugin version,
#. %3$s expands to the plugin name
#: admin/class-admin-init.php:326
msgid "%1$s and %2$s can work together a lot better by adding a helper plugin. Please install %3$s to make your life better."
msgstr "%1$s e %2$s possono lavorare insieme molto meglio con l'aggiunta di un plugin helper. Installa %3$s per rendere la tua vita migliore."

#. translators: %1$s expands to Yoast SEO.
#: admin/help_center/class-template-variables-tab.php:74
msgid "The search appearance settings for %1$s are made up of variables that are replaced by specific values from the page when the page is displayed. The table below contains a list of the available variables."
msgstr "Le impostazioni dell'Aspetto della ricerca di %1$s è costituita da variabili che vengono sostituite da altri valori quando la pagina viene visualizzata. La tabella seguente contiene un elenco delle variabili disponibili."

#. translators: %1$s expands to the posttype label, %2$s expands anchor to blog
#. post about cornerstone content, %3$s expands to </a>
#: admin/filters/class-cornerstone-filter.php:104
msgid "Mark the most important %1$s as 'cornerstone content' to improve your site structure. %2$sLearn more about cornerstone content%3$s."
msgstr "Contrassegna gli (le) %1$s più importanti come 'cornerstone content' (contenuti centrali) per migliorare la struttura del tuo sito. %2$sLeggi di più sui contenuti cornerstone%3$s."

#: admin/class-help-center.php:122
msgid "Enroll in the Yoast SEO for WordPress training »"
msgstr "Iscriviti al corso Yoast SEO for WordPress »"

#: admin/class-help-center.php:193
msgid "Loading help center."
msgstr "Caricamento help center in corso."

#: admin/class-help-center.php:228 admin/class-help-center.php:249
msgid "Get support"
msgstr "Assistenza"

#: admin/class-help-center.php:255
msgid "View in KB"
msgstr "Vai alla KB"

#: admin/class-help-center.php:257
msgid "Go back"
msgstr "Torna indietro"

#: admin/class-help-center.php:258
msgid "Go back to the search results"
msgstr "Torna indietro ai risultati della ricerca"

#: languages/yoast-components.php:241
#: admin/class-multiple-keywords-modal.php:40
#: admin/formatter/class-metabox-formatter.php:174
#: admin/formatter/class-metabox-formatter.php:207
#: admin/class-admin-utils.php:78 admin/class-keyword-synonyms-modal.php:40
#: admin/class-premium-upsell-admin-block.php:73
#: admin/class-premium-popup.php:82 admin/class-help-center.php:268
#: admin/class-add-keyword-modal.php:40
#: admin/watchers/class-slug-change-watcher.php:224
#: admin/views/licenses.php:112
msgid "(Opens in a new browser tab)"
msgstr "(Si apre in una nuova scheda del browser)"

#: admin/class-help-center.php:120
msgid "Follow our Yoast SEO for WordPress training and become a certified Yoast SEO Expert!"
msgstr "Segui il nostro corso Yoast SEO for WordPress e diventa Yoast SEO Expert certificato!"

#: admin/class-help-center.php:119
msgid "Want to be a Yoast SEO Expert?"
msgstr "Vuoi essere un esperto di Yoast SEO?"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:203
msgid "Posts %1$swithout%2$s a focus keyphrase"
msgstr "Articoli %1$ssenza%2$s frase chiave"

#: admin/statistics/class-statistics-service.php:71
msgid "Hey, your SEO is doing pretty well! Check out the stats:"
msgstr "Ehi, la tua SEO sta andando abbastanza bene! Guarda le statistiche:"

#: admin/statistics/class-statistics-service.php:67
msgid "You don't have any published posts, your SEO scores will appear here once you make your first post!"
msgstr "Non hai pubblicato nessun post, i tuoi punteggi SEO appariranno qui appena scriverari il tuo primo post!"

#: admin/config-ui/fields/class-field-title-intro.php:19
msgid "On this page, you can change the name of your site and choose which separator to use. The separator will display, for instance, between your post title and site name. Symbols are shown in the size they'll appear in the search results. Choose the one that fits your brand best or takes up the least space in the snippets."
msgstr "In questa pagina, puoi cambiare il nome del tuo sito e scegliere quale separatore utilizzare. Il separatore sarà mostrato, ad esempio, fra il titolo dell'articolo e il nome del sito. I simboli dei separatori qui sotto sono visualizzati nelle dimensioni con le quali appariranno nei risultati della ricerca. Scegli quello più adatto al tuo brand o quello che occupa meno spazio negli snippet."

#: admin/config-ui/fields/class-field-site-type.php:28
msgid "Something else"
msgstr "Altro"

#: admin/config-ui/fields/class-field-site-type.php:27
msgid "A portfolio"
msgstr "Un portfolio"

#: admin/config-ui/fields/class-field-site-type.php:26
msgid "A corporation"
msgstr "Una grande azienda"

#: admin/config-ui/fields/class-field-site-type.php:25
msgid "A small offline business"
msgstr "Una piccola attività offline"

#: admin/config-ui/fields/class-field-site-type.php:24
msgid "A news channel"
msgstr "Un canale di news"

#: admin/config-ui/fields/class-field-site-type.php:23
msgid "An online shop"
msgstr "Un negozio online"

#: admin/config-ui/fields/class-field-site-type.php:22
msgid "A blog"
msgstr "Un blog"

#. translators: %1$s resolves to the home_url of the blog.
#: admin/config-ui/fields/class-field-site-type.php:20
msgid "What does the site %1$s represent?"
msgstr "Che tipo di sito è %1$s?"

#: admin/config-ui/fields/class-field-multiple-authors.php:21
msgid "If you choose no, your author archives will be deactivated to prevent  duplicate content issues."
msgstr "Se scegli no, i tuoi archivi autore saranno disattivati per prevenire problemi con contenuti duplicati."

#: admin/config-ui/fields/class-field-google-search-console-intro.php:28
msgid "Note: we don't store your data in any way and don't have full access to your account. Your privacy is safe with us."
msgstr "Nota: non conserviamo i tuoi dati in nessun modo e non abbiamo un accesso completo al tuo account. La tua privacy è al sicuro con noi."

#. translators: %1$s is the plugin name. %2$s is a link start tag to a Yoast
#. help page, %3$s is the link closing tag.
#: admin/config-ui/fields/class-field-google-search-console-intro.php:22
msgid "%1$s integrates with Google Search Console, a must-have tool for site owners. It provides you with information about the health of your site. Don't have a Google account or is your site not activated yet? Find out %2$show to connect Google Search Console to your site%3$s."
msgstr "%1$s si integra con la Console di Google Search, un tool irrinunciabile per i proprietari di siti. Ti fornisce informazioni sulla salute del tuo sito.  Non hai ancora un account Google o il tuo sito non è ancora attivato Scopri %2$scome collegare la Console di Google Search Console al tuo sito%3$s."

#: admin/config-ui/fields/class-field-environment.php:24
msgid "Option B: My site is under construction and should not be indexed"
msgstr "Opzione B: il mio sito è in costruzione e non deve essere indicizzato"

#: admin/config-ui/fields/class-field-environment.php:23
msgid "Option A: My site is live and ready to be indexed"
msgstr "Opzione A: il mio sito è online e pronto per essere indicizzato"

#: admin/config-ui/fields/class-field-environment.php:21
msgid "Choose under construction if you want to keep the site out of the index of search engines. Don't forget to activate it once you're ready to publish your site."
msgstr "Scegli la modalità in costruzione se vuoi tenere il tuo sito fuori dall'indice dei motori di ricerca. Non dimenticare di cambiare questa impostazione quando il tuo sito è pronto per essere pubblicato."

#: admin/config-ui/fields/class-field-environment.php:19
msgid "Please specify if your site is under construction or already active."
msgstr "Indica se il tuo sito è ancora in costruzione o già online."

#: admin/config-ui/fields/class-field-company-or-person.php:21
msgid "This information will be used in Google's Knowledge Graph Card, the big block of information you see on the right side of the search results."
msgstr "Questa informazione sarà utilizzata nella Knowledge Graph Card di Google, il blocco di informazioni che vedi nella parte destra dei risultati di ricerca."

#: admin/config-ui/class-configuration-page.php:141
msgid "Close wizard"
msgstr "Chiudi la procedura guidata"

#: admin/class-yoast-dashboard-widget.php:113
msgid "Read more like this on our SEO blog"
msgstr "Leggi più articoli di questo tipo nel nostro blog SEO."

#: inc/class-wpseo-rank.php:160
msgid "Readability: Good"
msgstr "Leggibilità: Buona"

#: inc/class-wpseo-rank.php:159
msgid "Readability: OK"
msgstr "Leggibilità: OK"

#: inc/class-wpseo-rank.php:158
msgid "Readability: Needs improvement"
msgstr "Leggibilità: Da migliorare"

#: inc/class-wpseo-rank.php:142
msgid "SEO: Needs improvement"
msgstr "SEO: Da migliorare"

#: admin/views/licenses.php:167 admin/views/licenses.php:249
msgid "Not activated"
msgstr "Non attivato"

#: admin/views/licenses.php:158 admin/views/licenses.php:239
msgid "Activated"
msgstr "Attivato"

#. translators: %1$s expands to Yoast
#: admin/views/sidebar.php:17
msgid "%1$s recommendations for you"
msgstr "Raccomandazioni per te da %1s"

#: admin/class-meta-columns.php:231
msgid "All Readability Scores"
msgstr "Tutti i punteggi di leggibilità"

#: admin/class-meta-columns.php:228
msgid "Filter by Readability Score"
msgstr "Filtra per punteggio di leggibilità"

#. translators: %1$s expands to the product name. %2$s expands to a link to My
#. Yoast
#: admin/class-license-page-manager.php:206
msgid "You are not receiving updates or support! Fix this problem by adding this site and enabling %1$s for it in %2$s."
msgstr "Non stai ricevendo aggiornamenti o assistenza! Risolvi il problema aggiungendo questo sito e abilitando %1$s in %2$s."

#. translators: %1$s expands to the request method
#: admin/class-remote-request.php:84
msgid "Request method %1$s is not valid."
msgstr "Il metodo di richiesta %1$s non è valido."

#: admin/views/partial-alerts-warnings.php:16
msgid "Muted notifications:"
msgstr "Notifiche nascoste:"

#: admin/views/partial-alerts-errors.php:16
msgid "Muted problems:"
msgstr "Problemi nascosti:"

#. translators: %1$s expands to the social network's name, %2$s to Yoast SEO
#. Premium.
#: admin/class-social-admin.php:179
msgid "Do you want to preview what it will look like if people share this post on %1$s? You can, with %2$s."
msgstr "Vuoi vedere un'anteprima di come questo articolo apparirà quando viene condiviso su %1$s? Ora puoi, con %2$s."

#: admin/links/class-link-reindex-dashboard.php:169
msgid "Good job! All the links in your texts have been counted."
msgstr "Ottimo lavoro! Tutti i link nei tuoi testi sono stati contati."

#: admin/links/class-link-reindex-dashboard.php:147
msgid "Calculation completed."
msgstr "Calcolo completato."

#: admin/links/class-link-reindex-dashboard.php:146
msgid "Calculation in progress..."
msgstr "Calcolo in corso..."

#: admin/links/class-link-reindex-dashboard.php:121
msgid "Stop counting"
msgstr "Interrompi il conteggio"

#: admin/links/class-link-reindex-dashboard.php:114
msgid "Counting links in your texts"
msgstr "Conteggio dei link nei tuoi testi"

#. translators: 1: expands to a <span> containing the number of items
#. recalculated. 2: expands to a <strong> containing the total number of items.
#: admin/links/class-link-reindex-dashboard.php:103
msgid "Text %1$s of %2$s processed."
msgstr "Testo %1$s di %2$s processati."

#: admin/links/class-link-reindex-dashboard.php:96
msgid "All your texts are already counted, there is no need to count them again."
msgstr "Tutti i tuoi testi sono già stati contati, non c'è bisogno di contarli di nuovo. "

#: admin/links/class-link-reindex-dashboard.php:199
msgid "Count links in your texts"
msgstr "Conta i link nei tuoi testi"

#. translators: 1: link to yoast.com post about internal linking suggestion. 4:
#. is Yoast.com 3: is anchor closing.
#: admin/links/class-link-reindex-dashboard.php:220
msgid "The links in all your public texts need to be counted. This will provide insights of which texts need more links to them. If you want to know more about the why and how of internal linking, check out %1$sthe article about internal linking on %2$s%3$s."
msgstr "I link in tutti i tuoi testi pubblici hanno bisogno di essere contati. Ciò fornirà informazioni  su quali testi hanno bisogno di più link. Se vuoi conoscere di più su come l'internal linking funziona, leggi %1$sl'articolo sull'internal linking su %2$s%3$s."

#: admin/links/class-link-reindex-dashboard.php:65
#: admin/links/class-link-reindex-dashboard.php:217
#: admin/views/class-yoast-feature-toggles.php:96
msgid "Text link counter"
msgstr "Contatore di link nei testi"

#: admin/links/class-link-columns.php:192
msgid "Number of internal links linking to this post. See \"Yoast Columns\" text in the help tab for more info."
msgstr "Numero di link interni che puntano a questo articolo. Vai nella sezione aiuto alla voce \"Colonne Yoast\" per maggiori informazioni."

#: admin/links/class-link-columns.php:185
msgid "Number of outgoing internal links in this post. See \"Yoast Columns\" text in the help tab for more info."
msgstr "Numero di link interni in questo articolo. Vai nella sezione aiuto alla voce \"Colonne Yoast\" per maggiori informazioni."

#. translators: %1$s: Yoast SEO, %2$s: Link to article about content analysis,
#. %3$s: Anchor closing, %4$s: Link to article about text links, %5$s: Emphasis
#. open tag, %6$s: Emphasis close tag
#: admin/class-yoast-columns.php:32
msgid "%1$s adds several columns to this page. We've written an article about %2$show to use the SEO score and Readability score%3$s. The links columns show the number of articles on this site linking %5$sto%6$s this article and the number of URLs linked %5$sfrom%6$s this article. Learn more about %4$show to use these features to improve your internal linking%3$s, which greatly enhances your SEO."
msgstr "%1$s aggiunge più colonne a questa pagina. Abbiamo scritto un articolo su %2$scome usare il punteggio SEO e il punteggio di leggibilità%3$s. Le colonne dei link mostrano il numero di articoli di questo sito collegati %5$sa%6$s questo articolo e il numero di URL linkati %5$sfrom%6$s questo articolo. Leggi di più su %4$scome usare  queste caratteristiche per migliorare il tuo sistema di linkinterni%3$s, e migliorare così la tua stategia SEO."

#. translators: %s expands to Yoast
#: admin/class-yoast-columns.php:28
msgid "%s Columns"
msgstr "Colonne %s"

#: admin/taxonomy/class-taxonomy-columns.php:68 admin/class-meta-columns.php:71
msgid "Readability score"
msgstr "Punteggio di leggibilità"

#. translators: %s expands to 'Yoast SEO Premium'.
#: languages/wordpress-seojs.php:125 admin/class-multiple-keywords-modal.php:30
#: admin/formatter/class-metabox-formatter.php:197
#: admin/class-keyword-synonyms-modal.php:30
#: admin/class-add-keyword-modal.php:30
msgid "Other benefits of %s for you:"
msgstr "Altri benefici da %s per te:"

#. translators: %1$s expands to a 'strong' start tag, %2$s to a 'strong' end
#. tag.
#: admin/class-help-center.php:217
msgid "%1$sSocial media preview%2$s: Facebook &amp; Twitter"
msgstr "%1$sAnteprima social media %2$s: Facebook &amp; Twitter"

#: languages/yoast-seo-js.php:539 languages/yoast-components.php:214
msgid "Scroll to see the preview content."
msgstr "Scorri per vedere l'anteprima del contenuto."

#: languages/wordpress-seojs.php:11
#: admin/filters/class-cornerstone-filter.php:87
#: admin/views/class-yoast-feature-toggles.php:88
msgid "Cornerstone content"
msgstr "Contenuto Cornerstone (contenuto centrale)"

#. translators: %1$s expands to Yoast SEO.
#: admin/config-ui/fields/class-field-success-message.php:21
msgid "%1$s will now take care of all the needed technical optimization of your site. To really improve your site's performance in the search results, it's important to start creating content that ranks well for keyphrases you care about. Check out this video in which we explain how to use the %1$s metabox when you edit posts or pages."
msgstr "%1$s si prenderà cura di tutte le ottimizzazioni tecniche necessarie per il tuo sito. Per migliorare davvero la posizione del tuo sito nei risultati di ricerca, è importante iniziare a creare contenuto che si posizioni bene in base alle frasi chiave che ti interessano. Guarda questo video in cui spieghiamo come usare la %1$s metabox quando scrivi articoli o pagine."

#. translators: %1$s resolves to Local SEO
#: admin/config-ui/components/class-component-suggestions.php:100
msgid "If you want to outrank the competition in a specific town or region, check out our %1$s plugin! You’ll be able to easily insert Google maps, opening hours, contact information and a store locator. Besides that %1$s helps you to improve the usability of your contact page."
msgstr "Se vuoi sorpassare la concorrenza in una città o regione specifica, prova il nostro plugin %1$s! Sarai in grado di inserire facilmente mappe di Google, orari di apertura, informazioni di contatto e un localizzatore dei punti vendita. Inoltre %1$s ti aiuta a migliorare l'usabilità della tua pagina contatti."

#: admin/config-ui/components/class-component-suggestions.php:98
msgid "Attract more customers near you"
msgstr "Attrai più clienti vicino a te"

#. translators: %1$s resolves to Yoast SEO
#: admin/config-ui/components/class-component-suggestions.php:79
msgid "Do you want to know all the ins and outs of the %1$s plugin? Do you want to learn all about our advanced settings? Want to be able to really get the most out of the %1$s plugin? Check out our %1$s plugin training and start outranking the competition!"
msgstr "Vuoi conoscere tutte le sfaccettature di %1$s? Vuoi imparare tutto riguardo le nostre impostazioni avanzate? Vuoi essere in grado di ottenere il massimo da %1$s? Dai un'occhiata alle nostre guide sul plugin %1$s e inizia subito a superare i tuoi concorrenti!"

#. translators: %1$s resolves to Yoast SEO, %2$s resolves to Yoast SEO plugin
#. training
#: admin/config-ui/components/class-component-suggestions.php:77
msgid "Get the most out of %1$s with the %2$s"
msgstr "Ottieni il massimo da %1$s con %2$s"

#: admin/config-ui/components/class-component-suggestions.php:38
msgid "Upgrade to Premium"
msgstr "Passa a Premium"

#. translators: %1$s resolves to Yoast SEO Premium
#: admin/config-ui/components/class-component-suggestions.php:36
msgid "Do you want to outrank your competition? %1$s gives you awesome additional features that'll help you to set up your SEO strategy like a professional. Add synonyms and related keywords, use our Premium SEO analysis, the redirect manager and our internal linking tool. %1$s will also give you access to premium support."
msgstr "Vuoi superare i tuoi concorrenti? %1$s ti offre altre eccezionali funzionalità che ti aiuteranno a elaborare una strategia SEO come un professionista. Aggiungi sinonimi e parole chiave correlate, usa la nostra analisi SEO Premium, il redirect manager e il nostro tool per i link interni. %1$s ti permette di accedere anche all'assistenza premium."

#. translators: %s resolves to Yoast SEO Premium
#: admin/config-ui/components/class-component-suggestions.php:34
msgid "Outrank the competition with %s"
msgstr "Supera la concorrenza con %s"

#: admin/config-ui/class-configuration-structure.php:94
msgid "You might like"
msgstr "Ti potrebbe interessare"

#: admin/config-ui/class-configuration-structure.php:93
msgid "Newsletter"
msgstr "Newsletter"

#. translators: %1$s resolves to Yoast SEO, %2$s resolves to the starting tag
#. of the link to the wizard, %3$s resolves to the closing link tag
#: admin/config-ui/class-configuration-page.php:223
msgid "We have detected that you have not finished this wizard yet, so we recommend you to %2$sstart the configuration wizard to configure %1$s%3$s."
msgstr "Abbiamo rilevato che non hai ancora completato la configurazione guidata, ti raccomandiamo di %2$siniziare la procedura guidata di configurazione per %1$s%3$s."

#: admin/config-ui/class-configuration-page.php:219
msgid "The configuration wizard helps you to easily configure your site to have the optimal SEO settings."
msgstr "La procedura guidata di configurazione ti aiuta a configurare facilmente il tuo sito per avere impostazioni SEO ottimali."

#: admin/class-help-center.php:113 admin/class-help-center.php:202
msgid "Go Premium and our experts will be there for you to answer any questions you might have about the setup and use of the plugin."
msgstr "Passa a Premium e i nostri esperti saranno lì per rispondere a tutte le domande che potresti avere sulla configurazione e sull'utilizzo del plugin!"

#: languages/wordpress-seojs.php:117
#: admin/class-premium-upsell-admin-block.php:60
#: admin/class-help-center.php:221
msgid "24/7 support"
msgstr "Assistenza 24/7"

#: admin/class-premium-upsell-admin-block.php:58
msgid "Superfast internal linking suggestions"
msgstr "Suggerimenti super veloci di link interni"

#. translators: %1$s expands to a 'Yoast SEO Premium' text linked to the
#. yoast.com website.
#: languages/wordpress-seojs.php:136 admin/class-multiple-keywords-modal.php:24
#: admin/formatter/class-metabox-formatter.php:191
#: admin/class-keyword-synonyms-modal.php:24
#: admin/class-add-keyword-modal.php:24
msgid "Great news: you can, with %1$s!"
msgstr "Novità importante: puoi con %1$s! "

#: admin/class-yoast-form.php:152
msgid "Save changes"
msgstr "Salva le modifiche"

#: languages/wordpress-seojs.php:130 admin/class-multiple-keywords-modal.php:39
#: admin/formatter/class-metabox-formatter.php:206
#: admin/class-keyword-synonyms-modal.php:39 admin/class-premium-popup.php:88
#: admin/class-add-keyword-modal.php:39
msgid "1 year free updates and upgrades included!"
msgstr "1 anno di aggiornamenti e upgrade gratuiti incluso!"

#. translators: %2$s expands to 'RS Head Cleaner' plugin name of possibly
#. conflicting plugin with regard to differentiating output between search
#. engines and normal users.
#: admin/class-plugin-conflict.php:162
msgid "The plugin %2$s changes your site's output and in doing that differentiates between search engines and normal users, a process that's called cloaking. We highly recommend that you disable it."
msgstr "Il plugin %2$s modifica l'aspetto del tuo sito differenziando il contenuto per i motori di ricerca da quello per gli utenti \"normali\". Questo è un processo chiamato cloaking, che potrebbe portarti a brutte penalizzazioni. Ti suggeriamo di disabilitarlo."

#: languages/wordpress-seojs.php:113
msgid "%1$sSocial media preview%2$s: Facebook & Twitter"
msgstr "%1$sAnteprima dei social media%2$s: Facebook & Twitter"

#: languages/wordpress-seojs.php:109 admin/class-help-center.php:213
msgid "Superfast internal links suggestions"
msgstr "Suggerimenti super veloci di link interni"

#. translators: %1$s expands to a 'strong' start tag, %2$s to a 'strong' end
#. tag.
#: languages/wordpress-seojs.php:105 admin/class-help-center.php:209
msgid "%1$sNo more dead links%2$s: easy redirect manager"
msgstr "%1$sNiente più link non funzionanti%2$s: easy redirect manager"

#. translators: %1$s: expands to 'Yoast SEO Premium'.
#: admin/class-help-center.php:204
msgid "Other benefits of %1$s for you:"
msgstr "Altri vantaggi che %1$s ti offre:"

#: languages/wordpress-seojs.php:121
#: admin/class-premium-upsell-admin-block.php:61
#: admin/class-help-center.php:222
msgid "No ads!"
msgstr "Nessuna pubblicità!"

#: admin/class-premium-upsell-admin-block.php:59
msgid "Facebook & Twitter"
msgstr "Facebook e Twitter"

#: admin/class-premium-upsell-admin-block.php:59
msgid "Social media preview"
msgstr "Anteprima social media"

#: admin/class-premium-upsell-admin-block.php:57
msgid "Easy redirect manager"
msgstr "Facile gestore per il reindirizzamento"

#: admin/class-premium-upsell-admin-block.php:57
msgid "No more dead links"
msgstr "Niente più link non funzionanti"

#: admin/class-premium-upsell-admin-block.php:56
msgid "Increase your SEO reach"
msgstr "Aumenta la tua visibilità SEO"

#: admin/views/tabs/metas/paper-content/rss-content.php:30
#: inc/class-wpseo-replace-vars.php:1059
msgid "Variable"
msgstr "Variabile"

#: admin/views/tabs/metas/paper-content/rss-content.php:24
msgid "Available variables"
msgstr "Variabili disponibili"

#. translators: %1$s will be a link to a review explanation page. Text between
#. %2$s and %3$s will be a link to an SEO copywriting course page.
#: admin/config-ui/fields/class-field-upsell-site-review.php:33
msgid "If you want more help creating awesome content, check out our %2$sSEO copywriting course%3$s. Do you want to know all about the features of the plugin, consider doing our %1$s!"
msgstr "Se vuoi aiuto per creare contenuti fantastici, dai un'occhiata al nostro %2$scorso di SEO copywriting%3$s. Se vuoi sapere tutto sulle funzionalità del plugin, valuta di seguire il nostro %1$s!"

#: admin/class-admin.php:304
msgid "Scroll to see the table content."
msgstr "Scorri per vedere il contenuto della tabella."

#. Translators: %1$s expands to the name of the assessment.
#: languages/yoast-seo-js.php:446
msgid "An error occurred in the '%1$s' assessment"
msgstr "Si è verificato un errore nella valutazione di '%1$s'"

#. Translators: %1$s expands to the percentage of complex words, %2$s expands
#. to a link on yoast.com, %3$d expands to the recommended maximum number of
#. syllables, %4$s expands to the anchor end tag, %5$s expands to the
#. recommended maximum number of syllables.
#: languages/yoast-seo-js.php:152
msgid "%1$s of the words contain %2$sover %3$s syllables%4$s, which is more than the recommended maximum of %5$s."
msgstr "%1$s parole contengono %2$soltre  %3$s  sillabe%4$s, che è più del massimo raccomandato di  %5$s."

#. Translators: %1$s expands to the percentage of complex words, %2$s expands
#. to a link on yoast.com, %3$d expands to the recommended maximum number of
#. syllables, %4$s expands to the anchor end tag, %5$s expands to the
#. recommended maximum number of syllables.
#: languages/yoast-seo-js.php:146
msgid "%1$s of the words contain %2$sover %3$s syllables%4$s, which is less than or equal to the recommended maximum of %5$s."
msgstr "%1$s parole contengono %2$s oltre %3$s sillabe%4$s, che è meno o uguale al massimo raccomandato di %5$s."

#. Translators: The preceding sentence is "Text length: The text contains x
#. words.", %3$s expands to a link on yoast.com, %4$s expands to the anchor end
#. tag, %5$d expands to the recommended minimum of words.
#: languages/yoast-seo-js.php:347
msgid "This is slightly below the recommended minimum of %5$d word. %3$sAdd a bit more copy%4$s."
msgid_plural "This is slightly below the recommended minimum of %5$d words. %3$sAdd a bit more copy%4$s."
msgstr[0] "Questo è leggermente inferiore al numero minimo di %5$d parola. %3$sAggiungi ancora un po' di contenuto%4$s."
msgstr[1] "Questo è leggermente inferiore al numero minimo di %5$d parole. %3$sAggiungi ancora un po' di contenuto%4$s."

#. Translators: %1$d expands to number of sentences, %2$s expands to a link on
#. yoast.com, %3$s expands to the recommended maximum sentence length, %4$s
#. expands to the anchor end tag.
#: languages/yoast-seo-js.php:88
msgid "The meta description contains %1$d sentence %2$sover %3$s words%4$s. Try to shorten this sentence."
msgid_plural "The meta description contains %1$d sentences %2$sover %3$s words%4$s. Try to shorten these sentences."
msgstr[0] "La meta descrizione contiene %1$d frase con %2$spiù di %3$sparole%4$s. Prova ad accorciarla."
msgstr[1] "La meta descrizione contiene %1$d frasi con %2$spiù di %3$sparole%4$s. Prova ad accorciarle."

#. Translators: %1$s expands to a link on yoast.com, %2$s expands to the
#. recommended maximum sentence length, %3$s expands to the anchor end tag.
#: languages/yoast-seo-js.php:83
msgid "The meta description contains no sentences %1$sover %2$s words%3$s."
msgstr "La meta descrizione non contiene frasi con %1$spiù di %2$s parole%3$s."

#: languages/yoast-components.php:50
msgid "Step %1$d: %2$s"
msgstr "Passo %1$d: %2$s"

#: admin/views/partial-alerts-warnings.php:15
msgid "No new notifications."
msgstr "Nessuna nuova notifica."

#. translators: %1$s expands anchor to knowledge base article, %2$s expands to
#. </a>
#: admin/google_search_console/views/gsc-display.php:142
msgid "Please refer to %1$sour article about how to connect your website to Google Search Console%2$s if you need assistance."
msgstr "Se hai bisogno di aiuto, consulta %1$sil nostro articolo su come collegare il tuo sito web alla Google Search Console%2$s."

#: admin/class-bulk-editor-list-table.php:874
msgid "Save all"
msgstr "Salva tutto"

#: admin/class-bulk-editor-list-table.php:873
msgid "Save"
msgstr "Salva"

#. translators: 1: Author name; 2: Site name.
#: inc/options/class-wpseo-option-titles.php:205
msgid "%1$s, Author at %2$s"
msgstr "%1$s, Autore presso %2$s"

#: languages/yoast-seo-js.php:536 languages/yoast-components.php:179
msgid "Mobile preview"
msgstr "Anteprima in modalità mobile"

#: languages/yoast-seo-js.php:533 languages/yoast-components.php:182
msgid "Desktop preview"
msgstr "Anteprima in modalità desktop"

#: admin/google_search_console/class-gsc-table.php:162
msgid "Select redirect"
msgstr "Seleziona reindirizzamento"

#: languages/yoast-seo-js.php:542
msgid "Please provide an SEO title by editing the snippet below."
msgstr "Inserisci un titolo SEO modificando lo snippet sottostante."

#: languages/yoast-seo-js.php:527
msgid "Meta description preview:"
msgstr "Anteprima della meta descrizione:"

#: languages/yoast-seo-js.php:524
msgid "Slug preview:"
msgstr "Anteprima dello slug:"

#: languages/yoast-seo-js.php:521
msgid "SEO title preview:"
msgstr "Anteprima titolo SEO:"

#: languages/yoast-seo-js.php:515 languages/yoast-components.php:205
msgid "Close snippet editor"
msgstr "Chiudi editor snippet"

#: languages/yoast-seo-js.php:509 languages/yoast-components.php:211
msgid "Slug"
msgstr "Slug"

#: languages/yoast-seo-js.php:500
msgid "Remove marks in the text"
msgstr "Rimuovi l'evidenziazione nel testo"

#: languages/yoast-seo-js.php:497
msgid "Mark this result in the text"
msgstr "Segnare questo risultato nel testo"

#: languages/yoast-seo-js.php:494 languages/yoast-components.php:131
#: admin/formatter/class-metabox-formatter.php:173
msgid "Marks are disabled in current view"
msgstr "L'evidenziazione è disabilitata"

#: languages/yoast-seo-js.php:488
msgid "Content optimization: Good SEO score"
msgstr "Ottimizzazione dei contenuti: punteggio SEO Buono"

#: languages/yoast-seo-js.php:485
msgid "Good SEO score"
msgstr "Punteggio SEO Buono"

#: languages/yoast-seo-js.php:479
msgid "Content optimization: OK SEO score"
msgstr "Ottimizzazione dei contenuti: Punteggio SEO OK"

#: languages/yoast-seo-js.php:476
msgid "OK SEO score"
msgstr "Punteggio SEO Ok"

#: languages/yoast-seo-js.php:466
msgid "Content optimization: Has feedback"
msgstr "Ottimizzazione dei contenuti: c'è un feedback"

#: languages/yoast-seo-js.php:463
msgid "Feedback"
msgstr "Feedback"

#: languages/yoast-seo-js.php:17
msgid "ok"
msgstr "ok"

#: languages/yoast-components.php:101
msgid "Choose an image"
msgstr "Seleziona un'immagine"

#: languages/yoast-components.php:98
msgid "Remove the image"
msgstr "Rimuovi l'immagine"

#: languages/yoast-components.php:92
msgid "Choose image"
msgstr "Scegli immagine"

#: languages/yoast-components.php:89
msgid "MailChimp signup failed:"
msgstr "Iscrizione a MailChimp fallita:"

#: languages/yoast-components.php:86
msgid "Sign Up!"
msgstr "Iscriviti!"

#: inc/class-wpseo-replace-vars.php:1355
msgid "Name"
msgstr "Nome"

#: languages/yoast-components.php:80
msgid "There is an error with the request."
msgstr "C'è un errore con questa richiesta."

#: languages/yoast-components.php:74
msgid "Select profile"
msgstr "Seleziona il profilo"

#: languages/yoast-components.php:71
msgid "Choose a profile"
msgstr "Scegli un profilo"

#: languages/yoast-components.php:65
msgid "Authorization code"
msgstr "Codice di autorizzazione"

#: languages/yoast-components.php:59
#: admin/google_search_console/views/gsc-display.php:64
msgid "Reauthenticate with Google"
msgstr "Autenticati nuovamente con Google"

#: languages/yoast-components.php:56
msgid "To allow %s to fetch your Google Search Console information, please enter your Google Authorization Code. Clicking the button below will open a new window."
msgstr "Per consentire a %s di recuperare le tue informazioni dalla Google Search Console, inserisci il Codice di autorizzazione di Google. Facendo clic sul link sottostante si aprirà una nuova finestra."

#: languages/wordpress-seojs.php:148 languages/yoast-seo-js.php:545
msgid "Please provide a meta description by editing the snippet below."
msgstr "Inserisci una meta descrizione modificando lo snippet sottostante."

#: languages/yoast-seo-js.php:503 languages/yoast-components.php:208
msgid "Edit snippet"
msgstr "Modifica snippet"

#: languages/yoast-seo-js.php:530
msgid "You can click on each element in the preview to jump to the Snippet Editor."
msgstr "Puoi fare clic su ogni elemento nell'anteprima per passare all'editor dello snippet."

#: languages/yoast-components.php:220
msgid "SEO title preview"
msgstr "Anteprima del titolo SEO"

#: languages/yoast-components.php:226
msgid "Meta description preview"
msgstr "Anteprima della meta descrizione"

#: languages/yoast-components.php:47
msgid "A problem occurred when saving the current step, {{link}}please file a bug report{{/link}} describing what step you are on and which changes you want to make (if any)."
msgstr "Si è verificato un problema nel salvataggio di questo passaggio, {{link}}compila una segnalazione{{/link}} descrivendo in quale passaggio ti trovavi e quali modifiche volevi effettuare (se ne stavi facendo qualcuna)."

#: languages/yoast-components.php:41
msgid "Close the Wizard"
msgstr "Chiudi la procedura guidata"

#: admin/views/tool-import-export.php:87
msgid "Export settings"
msgstr "Esporta le impostazioni"

#. translators: %1$s expands to Yoast SEO.
#: admin/config-ui/fields/class-field-success-message.php:29
msgid "%1$s video tutorial"
msgstr "Video lezione %1$s"

#. translators: %1$s is the notification dismissal link start tag, %2$s is the
#. link closing tag.
#: admin/class-product-upsell-notice.php:162
msgid "%1$sPlease don't show me this notification anymore%2$s"
msgstr "%1$sNon mostrarmi più questa notifica%2$s"

#. translators: %1$s is a link start tag to the bugreport guidelines on the
#. Yoast knowledge base, %2$s is the link closing tag.
#: admin/class-product-upsell-notice.php:153
msgid "If you are experiencing issues, %1$splease file a bug report%2$s and we'll do our best to help you out."
msgstr "Se stai riscontrando problemi, %1$sinviaci un bug report%2$s e faremo del nostro meglio per aiutarti."

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. plugin page on WordPress.org, %3$s is the link closing tag.
#: admin/class-product-upsell-notice.php:145
msgid "We've noticed you've been using %1$s for some time now; we hope you love it! We'd be thrilled if you could %2$sgive us a 5 stars rating on WordPress.org%3$s!"
msgstr "Abbiamo notato che stai utilizzando %1$s da un po' di tempo; ci auguriamo che ti stia piacendo! Saremmo entusiasti se tu volessi %2$sdarci un punteggio di 5 stelle su WordPress.org%3$s!"

#. translators: %s expands to Yoast SEO.
#: admin/config-ui/class-configuration-page.php:118
msgid "%s &rsaquo; Configuration Wizard"
msgstr "%s &rsaquo; Configurazione guidata"

#: admin/menu/class-admin-menu.php:99
msgid "Courses"
msgstr "Corsi"

#: admin/views/tabs/metas/archives.php:24
msgid "Date archives settings"
msgstr "Impostazioni archivi per data"

#: admin/views/tabs/metas/archives.php:19
msgid "Author archives settings"
msgstr "Impostazioni archivi autore"

#. translators: %1$s expands to the image recommended width, %2$s to its
#. height.
#: admin/taxonomy/class-taxonomy-social-fields.php:108
#: admin/taxonomy/class-taxonomy-social-fields.php:115
#: admin/class-social-admin.php:49 admin/class-social-admin.php:52
msgid "%1$s by %2$s"
msgstr "%1$s per %2$s"

#. translators: %s: '%%term_title%%' variable used in titles and meta's
#. template that's not compatible with the given template
#: admin/class-admin.php:300
msgid "Warning: the variable %s cannot be used in this template. See the help center for more info."
msgstr "Attenzione: la variabile %s non può essere utilizzata in questo template. Vedi il centro di supporto per maggiori informazioni."

#. translators: %1$s expands anchor to premium plugin page, %2$s expands to
#. </a>
#: admin/class-product-upsell-notice.php:128
msgid "By the way, did you know we also have a %1$sPremium plugin%2$s? It offers advanced features, like a redirect manager and support for multiple keyphrases. It also comes with 24/7 personal support."
msgstr "A proposito, sapevi che abbiamo anche un %1$sPlugin Premium%2$s? Fornisce funzionalità più avanzate, quali un gestore di redirect ed il supporto per frasi chiave multiple. Inoltre dispone di una assistenza personalizzata 24/7."

#: admin/class-bulk-editor-list-table.php:781
msgid "(no title)"
msgstr "(senza titolo)"

#: languages/yoast-components.php:32
msgid "%s installation wizard"
msgstr "Procedura di installazione guidata %s"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:128
msgid "The %1$s admin bar menu contains useful links to third-party tools for analyzing pages and makes it easy to see if you have new notifications."
msgstr "La barra di amministrazione di %1$s contiene dei link utili a strumenti di terze parti per analizzare le pagine e rendere semplice verificare se si hanno nuove notifiche."

#: admin/views/class-yoast-feature-toggles.php:125
msgid "Admin bar menu"
msgstr "Menu della barra di amministrazione"

#. translators: 1: Ryte
#: admin/views/class-yoast-feature-toggles.php:118
msgid "%1$s will check weekly if your site is still indexable by search engines and Yoast SEO will notify you when this is not the case."
msgstr "L'integrazione con %1$s verifica giornalmente che il tuo sito sia indicizzabile dai motori di ricerca e ti notifica quando ciò non si verifica."

#: admin/pages/network.php:19 admin/pages/dashboard.php:49
#: admin/views/tabs/dashboard/features.php:19
#: admin/views/tabs/network/features.php:19
msgid "Features"
msgstr "Funzionalità"

#: admin/class-help-center.php:115
msgid "Get Yoast SEO Premium now »"
msgstr "Acquista ora Yoast SEO Premium »"

#: languages/yoast-seo-js.php:506 languages/yoast-components.php:195
#: admin/metabox/class-metabox.php:65
msgid "SEO title"
msgstr "Titolo SEO"

#. Translators: %1$s expands to Yoast SEO, %2$s expands to Yoast SEO Premium,
#. %3$s opens the link, %4$s closes the link.
#: admin/config-ui/fields/class-field-upsell-configuration-service.php:39
msgid "While we strive to make setting up %1$s as easy as possible, we understand it can be daunting. If you’d rather have us set up %1$s for you (and get a copy of %2$s in the process), order our %3$s%1$s configuration service%4$s here!"
msgstr "Anche se ci sforziamo di rendere la configurazione di %1$s più facile possibile, ci rendiamo conto che può essere complicata. Se preferisci che provvediamo noi alla configurazione di %1$s (e nel frattempo ottenere anche una copia di %2$s), ordina il nostro %3$sServizio di configurazione di %1$s%4$s!"

#. translators: %1$s expands to Yoast SEO.
#: admin/config-ui/fields/class-field-upsell-configuration-service.php:33
msgid "Welcome to the %1$s configuration wizard. In a few simple steps we'll help you configure your SEO settings to match your website's needs!"
msgstr "Benvenuti alla procedura guidata di installazione di %1$s. In pochi semplici passi ti aiuteremo a configurare le tue impostazioni SEO affinché rispondano alle necessità del tuo sito!"

#: admin/config-ui/fields/class-field-site-name.php:21
msgid "Google shows your website's name in the search results, if you want to change it, you can do that here."
msgstr "Google mostra il nome del tuo sito nei risultati della ricerca, se vuoi cambiarlo, puoi farlo da qui."

#: inc/options/class-wpseo-option-titles.php:860
msgid "Greater than sign"
msgstr "Simbolo di maggiore"

#: inc/options/class-wpseo-option-titles.php:856
msgid "Less than sign"
msgstr "Simboli di minore"

#: inc/options/class-wpseo-option-titles.php:852
msgid "Right angle quotation mark"
msgstr "Virgoletta bassa destra"

#: inc/options/class-wpseo-option-titles.php:848
msgid "Left angle quotation mark"
msgstr "Virgoletta bassa sinistra"

#: inc/options/class-wpseo-option-titles.php:844
msgid "Small tilde"
msgstr "Tilde piccola"

#: inc/options/class-wpseo-option-titles.php:840
msgid "Vertical bar"
msgstr "Barra verticale"

#: inc/options/class-wpseo-option-titles.php:836
msgid "Low asterisk"
msgstr "Asterisco basso"

#: inc/options/class-wpseo-option-titles.php:832
msgid "Asterisk"
msgstr "Asterisco"

#: inc/options/class-wpseo-option-titles.php:828
msgid "Bullet"
msgstr "Punto"

#: inc/options/class-wpseo-option-titles.php:824
msgid "Middle dot"
msgstr "punto medio"

#: inc/options/class-wpseo-option-titles.php:816
msgid "Em dash"
msgstr "trattino em"

#: inc/options/class-wpseo-option-titles.php:812
msgid "En dash"
msgstr "trattino en"

#: inc/options/class-wpseo-option-titles.php:808
msgid "Dash"
msgstr "Trattino"

#: admin/config-ui/fields/class-field-separator.php:20
#: admin/views/tabs/metas/paper-content/general/title-separator.php:13
msgid "Choose the symbol to use as your title separator. This will display, for instance, between your post title and site name. Symbols are shown in the size they'll appear in the search results."
msgstr "Scegli il simbolo da utilizzare come separatore del titolo. Apparirà, ad esempio, fra il titolo del tuo articolo ed il nome del sito. I simboli sono visualizzati nella dimensione con cui appariranno nei risultati delle ricerche."

#: languages/wordpress-seojs.php:45
msgid "The name of the person"
msgstr "Il nome della persona"

#: admin/metabox/class-metabox.php:77 admin/metabox/class-metabox.php:82
#: admin/config-ui/fields/class-field-choice-post-type.php:35
#: admin/config-ui/fields/class-field-multiple-authors.php:24
#: admin/views/tabs/metas/paper-content/media-content.php:20
#: admin/taxonomy/class-taxonomy-settings-fields.php:71
#: admin/class-yoast-form.php:700
msgid "No"
msgstr "No"

#: admin/metabox/class-metabox.php:76 admin/metabox/class-metabox.php:81
#: admin/config-ui/fields/class-field-choice-post-type.php:34
#: admin/config-ui/fields/class-field-multiple-authors.php:23
#: admin/views/tabs/metas/paper-content/media-content.php:19
#: admin/taxonomy/class-taxonomy-settings-fields.php:70
#: admin/class-yoast-form.php:699
msgid "Yes"
msgstr "Si"

#: admin/config-ui/fields/class-field-multiple-authors.php:19
msgid "Does, or will, your site have multiple authors?"
msgstr "Il tuo è un sito o sarà un sito con autori multipli?"

#: admin/config-ui/factories/class-factory-post-type.php:57
msgid "WordPress automatically generates an URL for each media item in the library. Enabling this will allow for google to index the generated URL."
msgstr "WordPress genera automaticamente un URL per ciascun elemento della libreria media. Abilitando questa opzione si permetterà a google di indicizzare l'URL generata."

#: admin/config-ui/class-configuration-structure.php:95
msgid "Success!"
msgstr "Fine!"

#: admin/config-ui/class-configuration-structure.php:91
msgid "Title settings"
msgstr "Impostazioni del Titolo"

#: admin/google_search_console/views/gsc-display.php:35
#: admin/config-ui/class-configuration-structure.php:88
msgid "Google Search Console"
msgstr "Google Search Console"

#: admin/config-ui/class-configuration-structure.php:83
msgid "Multiple authors"
msgstr "Autori multipli"

#: admin/config-ui/class-configuration-structure.php:66
msgid "Site type"
msgstr "Tipo di sito"

#: admin/config-ui/class-configuration-structure.php:65
msgid "Environment"
msgstr "Ambiente"

#. translators: %1$s resolves to the option name passed to the lookup
#. registration
#: admin/config-ui/class-configuration-options-adapter.php:69
msgid "Yoast option %1$s not found."
msgstr "L'opzione Yoast %1$s non è stata trovata."

#: inc/class-wpseo-admin-bar-menu.php:328
msgid "Google Structured Data Test"
msgstr "Test per i Structured Data di Google"

#: admin/views/tool-bulk-editor.php:63
msgid "Posts list"
msgstr "Elenco articoli"

#: admin/views/tool-bulk-editor.php:62
msgid "Posts list navigation"
msgstr "Navigazione elenco articoli"

#: admin/views/tool-bulk-editor.php:61
msgid "Filter posts list"
msgstr "Filtro elenco articoli"

#: admin/views/tabs/metas/archives/help.php:20
msgid "Note that links to archives might be still output by your theme and you would need to remove them separately."
msgstr "Si noti che i link agli archivi possono continuare ad essere visualizzati dal tema e occorrerà rimuoverli separatamente,"

#. translators: %1$s expands to Yoast SEO extensions
#: admin/views/licenses.php:220
msgid "%1$s to optimize your site even further"
msgstr "%1$s per ottimizzare ulteriormente il tuo sito"

#. translators: %1$s expands to Yoast SEO
#: admin/views/licenses.php:216
msgid "%1$s extensions"
msgstr "Estensioni %1$s"

#: admin/views/licenses.php:206
msgid "Comes with our 30-day no questions asked money back guarantee"
msgstr "Include la nostra garanzia di restituzione dei soldi entro 30 giorni senza nessuna domanda sul motivo della richiesta"

#. translators: Text between %1$s and %2$s will only be shown to screen
#. readers. %3$s expands to the product name.
#: admin/views/licenses.php:195 admin/views/licenses.php:275
msgid "More information %1$sabout %3$s%2$s"
msgstr "Ulteriori informazioni %1$ssu %3$s%2$s"

#. translators: $1$s expands to Yoast SEO Premium
#: admin/views/licenses.php:184
msgid "Buy %1$s"
msgstr "Acquista %1$s"

#: admin/views/licenses.php:155 admin/views/licenses.php:236
msgid "Installed"
msgstr "Installato"

#: admin/views/licenses.php:150
msgid "gain access to our 24/7 support team."
msgstr "Ottieni accesso al nostro team di assistenza 24/7."

#: admin/views/licenses.php:149
msgid "Premium support"
msgstr "Assistenza premium"

#: admin/views/licenses.php:146
msgid "check what your Facebook or Twitter post will look like."
msgstr "verifica come appaiono i tuoi articoli su Facebook o Twitter."

#: admin/views/licenses.php:145
msgid "Social previews"
msgstr "Antreprime social"

#: admin/views/licenses.php:138
msgid "create and manage redirects from within your WordPress install."
msgstr "crea e gestisci i redirect direttamente dalla tua installazione WordPress."

#: admin/views/licenses.php:137
msgid "Redirect manager"
msgstr "Gestore dei redirect"

#. translators: %1$s expands to Yoast SEO Premium
#: admin/views/licenses.php:126
msgid "%1$s, take your optimization to the next level!"
msgstr "%1$s, porta la tua ottimizzazione ad un livello superiore!"

#. translators: %1$s expands to Yoast SEO, %2$s expands to WooCommerce
#: admin/views/licenses.php:99
msgid "A seamless integration between %1$s and %2$s"
msgstr "Una integrazione senza interruzioni fra  %1$s e %2$s"

#. translators: %1$s expands to Yoast, %2$s expands to WooCommerce
#: admin/views/licenses.php:97
msgid "Use %1$s breadcrumbs instead of %2$s ones"
msgstr "Utilizza le bricole di pane %1$s al posto di %2$s"

#: admin/views/licenses.php:44
msgid "Easily insert Google Maps, a store locator, opening hours and more"
msgstr "Inserisci facilmente Google Maps, un localizzatore di punti vendita, gli orari di apertura e molto altro"

#: admin/views/licenses.php:76
msgid "Creates XML News Sitemaps"
msgstr "Crea sitemap XML per le news"

#: admin/views/licenses.php:75
msgid "Immediately pings Google on the publication of a new post"
msgstr "Fai un ping a Google alla pubblicazione di un nuovo articolo"

#: admin/views/licenses.php:74
msgid "Optimize your site for Google News"
msgstr "Ottimizza il tuo sito per Google News"

#: admin/views/licenses.php:61
msgid "Make videos responsive through enabling fitvids.js"
msgstr "Rendi i video responsive tramite l'abilitazione di fitvids.js"

#: admin/views/licenses.php:60
msgid "Enhance the experience of sharing posts with videos"
msgstr "Migliora l'esperienza di condivisione di articoli contenenti video"

#: admin/views/licenses.php:59
msgid "Show your videos in Google Videos"
msgstr "Visualizza i tuoi video su Google Videos"

#: admin/google_search_console/views/gsc-display.php:114
msgid "Crawl issues list"
msgstr "Scorri l'elenco dei problemi"

#: admin/google_search_console/views/gsc-display.php:113
msgid "Crawl issues list navigation"
msgstr "Scorri l'elenco dei problemi di navigazione"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:795
msgid "Edit &#8220;%s&#8221;"
msgstr "Modifica &#8220;%s&#8221;"

#: admin/menu/class-base-menu.php:257
msgid "Premium"
msgstr "Premium"

#: admin/metabox/class-metabox.php:396
#: admin/taxonomy/class-taxonomy-metabox.php:240
msgid "Go Premium"
msgstr "Passa a Premium"

#: inc/class-wpseo-admin-bar-menu.php:258
msgid "Google Trends"
msgstr "Google Trends"

#: admin/views/partial-alerts-warnings.php:13
#: inc/class-wpseo-admin-bar-menu.php:199
msgid "Notifications"
msgstr "Notifiche"

#: admin/views/user-profile.php:48
msgid "Removes the focus keyphrase section from the metabox and disables all SEO-related suggestions."
msgstr "Rimuovi la scheda delle frasi chiave dal meta box e disabilita tutti i suggerimenti relativi alla SEO."

#: admin/views/user-profile.php:45
msgid "Disable SEO analysis"
msgstr "Disabilita l'analisi SEO"

#: admin/views/tabs/social/twitterbox.php:17
msgid "Twitter settings"
msgstr "Impostazioni Twitter"

#: admin/views/tabs/social/pinterest.php:16
msgid "Pinterest settings"
msgstr "Impostazioni Pintertest"

#: admin/views/tabs/social/facebook.php:16
msgid "Facebook settings"
msgstr "Impostazioni Facebook"

#: admin/views/tabs/metas/paper-content/general/title-separator.php:21
msgid "Title separator symbol"
msgstr "Simbolo separatore del titolo"

#: admin/views/tabs/metas/rss.php:15
msgid "RSS feed settings"
msgstr "Impostazioni feed RSS"

#: admin/views/tabs/metas/breadcrumbs.php:15
msgid "Breadcrumbs settings"
msgstr "Impostazioni dei Breadcrumbs"

#: admin/views/js-templates-primary-term.php:28
msgid "Make primary"
msgstr "Rendi primario"

#: admin/metabox/class-metabox.php:352 admin/metabox/class-metabox.php:355
#: admin/taxonomy/class-taxonomy-metabox.php:129
#: admin/taxonomy/class-taxonomy-metabox.php:132
msgid "Content optimization"
msgstr "Ottimizzazione dei contenuti"

#: languages/yoast-components.php:68
#: admin/google_search_console/views/gsc-display.php:56
msgid "Enter your Google Authorization Code and press the Authenticate button."
msgstr "inserisci il tuo Codice di Autorizzazione di Google e premi il pulsante di Autenticazione."

#. Translators: %1$s: expands to Yoast SEO, %2$s expands to Google Search
#. Console.
#: admin/google_search_console/views/gsc-display.php:48
msgid "To allow %1$s to fetch your %2$s information, please enter your Google Authorization Code. Clicking the button below will open a new window."
msgstr "Per consentire a %1$s di recuperare le tue informazioni su %2$s inserisci il tuo Codice di Autorizzazione di Google. Facendo clic sul pulsante sottostante si aprirà una nuova finestra."

#: admin/class-help-center.php:259
msgid "Knowledge base article"
msgstr "Articolo della knowledge base"

#: admin/class-help-center.php:256
msgid "Open the knowledge base article in a new window or read it in the iframe below"
msgstr "Apri l'articolo della knowledge base in una nuova finestra o leggilo nell'iframe sottostante"

#: languages/yoast-components.php:128 admin/class-help-center.php:267
msgid "Search results"
msgstr "Risultati della ricerca"

#: admin/class-admin.php:303
msgid "Yoast SEO video tutorial"
msgstr "Tutorial video Yoast SEO"

#. translators: %s: number of notifications
#: admin/menu/class-admin-menu.php:121 inc/class-wpseo-admin-bar-menu.php:636
msgid "%s notification"
msgid_plural "%s notifications"
msgstr[0] "%s notifica"
msgstr[1] "%s notifiche"

#: admin/views/user-profile.php:57
msgid "Disable readability analysis"
msgstr "Disabilita l'analisi della leggibilità"

#: admin/views/user-profile.php:60
msgid "Removes the readability analysis section from the metabox and disables all readability-related suggestions."
msgstr "Rimuovi la scheda dell'analisi di leggibilità dal meta box e disabilita tutti i suggerimenti sulla leggibilità."

#: languages/wordpress-seojs.php:60
#: admin/views/class-yoast-feature-toggles.php:80
msgid "Readability analysis"
msgstr "Analisi leggibilità"

#: languages/yoast-seo-js.php:470 inc/class-wpseo-rank.php:126
msgid "Needs improvement"
msgstr "Da migliorare"

#: admin/formatter/class-metabox-formatter.php:56
msgid "Readability"
msgstr "Leggibilità"

#. translators: %1$s resolves to https://github.com/Yoast/wordpress-seo
#: admin/class-admin-asset-seo-location.php:50
msgid "You are trying to load non-minified files. These are only available in our development package. Check out %1$s to see all the source files."
msgstr "Stai cercando di caricare dei file non minimizzati, questi sono disponibili solamente nel nostro pacchetto sviluppo. Guarda %1$s per vedere tutti i file sorgente."

#: languages/yoast-components.php:146
#: admin/formatter/class-metabox-formatter.php:166
msgid "Improvements"
msgstr "Miglioramenti"

#: admin/views/partial-alerts-template.php:35
msgid "Restore this item."
msgstr "Ripristina questo elemento."

#: admin/views/partial-alerts-template.php:31
#: admin/notifiers/class-configuration-notifier.php:160
msgid "Dismiss this item."
msgstr "Scarta questo elemento."

#: admin/views/partial-alerts-errors.php:15
msgid "Good job! We could detect no serious SEO problems."
msgstr "Buon lavoro! Non siamo in grado di individuare nessun problema SEO."

#: admin/views/partial-alerts-errors.php:14
msgid "We have detected the following issues that affect the SEO of your site."
msgstr "Abbiamo individuato i seguenti problemi che riguardano la SEO del tuo sito."

#: languages/yoast-components.php:143
#: admin/formatter/class-metabox-formatter.php:165
#: admin/views/partial-alerts-errors.php:13
msgid "Problems"
msgstr "Problemi"

#: admin/formatter/class-metabox-formatter.php:136
msgid "Analysis"
msgstr "Analisi"

#: inc/class-wpseo-rank.php:124
msgid "Not available"
msgstr "Non disponibile"

#: admin/class-meta-columns.php:202
msgid "Filter by SEO Score"
msgstr "Filtra per punteggio SEO"

#: admin/class-meta-columns.php:119
msgid "Meta description not set."
msgstr "Meta descrizione non impostata."

#: admin/class-help-center.php:247
msgid "Video tutorial"
msgstr "Tutorial Video"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: admin/class-help-center.php:201
msgid "Email support is a %s feature"
msgstr "L'assistenza tramite email è una funzionalità %s"

#: admin/class-help-center.php:248
msgid "Knowledge base"
msgstr "Knowledge base"

#: languages/wordpress-seojs.php:133
msgid "Open"
msgstr "Apri"

#: languages/yoast-components.php:110 admin/class-help-center.php:250
msgid "Loading..."
msgstr "Caricamento in corso..."

#: languages/yoast-components.php:107 admin/class-help-center.php:251
msgid "Something went wrong. Please try again later."
msgstr "Qualcosa non ha funzionato. Riprova più tardi."

#: languages/yoast-components.php:122 admin/class-help-center.php:261
msgid "No results found."
msgstr "Nessun risultato."

#: admin/menu/class-admin-menu.php:37 admin/pages/dashboard.php:39
msgid "Dashboard"
msgstr "Bacheca"

#. translators: %1$s resolves to the starting tag of the link to the permalink
#. settings page, %2$s resolves to the closing tag of the link
#: admin/class-admin-init.php:216
msgid "You can fix this on the %1$sPermalink settings page%2$s."
msgstr "Puoi correggerlo nella %1$sPagina delle impostazioni dei permalink%2$s."

#: admin/class-admin-init.php:164
msgid "Paging comments is enabled, this is not needed in 999 out of 1000 cases, we recommend to disable it."
msgstr "La paginazione dei commenti è abilitata, ciò non è necessario in 999 casi su 1000, noi raccomandiamo di disabilitarla."

#: inc/class-wpseo-replace-vars.php:1332
msgid "Replaced with the primary category of the post/page"
msgstr "Sostituito con la categoria principale nell'articolo/pagina"

#: admin/views/tabs/social/pinterest.php:36
msgid "Pinterest confirmation"
msgstr "Conferma Pinterest"

#: admin/views/tabs/social/pinterest.php:24
msgid "If you have already confirmed your website with Pinterest, you can skip the step below."
msgstr "Se hai già confermato il tuo sito in Pinterest, puoi saltare il passaggio successivo."

#: admin/views/tabs/metas/paper-content/taxonomy-content.php:18
msgid "Format-based archives"
msgstr "Archivio Format-based"

#: admin/views/tabs/dashboard/webmaster-tools.php:23
msgid "Webmaster Tools verification"
msgstr "Verifica per gli strumenti per webmaster"

#: admin/class-help-center.php:112
msgid "Need some help?"
msgstr "Ti serve aiuto?"

#. translators: %s expands to the product name
#: admin/views/licenses.php:264
msgid "Buy %s"
msgstr "Compra %s"

#: admin/google_search_console/class-gsc-category-filters.php:124
msgid "Show information about errors in category 'Soft 404'"
msgstr "Mostra informazioni sugli errori legati alla categoria \"Soft 404\""

#: admin/google_search_console/class-gsc-category-filters.php:123
msgid "Show information about errors in category 'Server'"
msgstr "Mostra informazioni sugli errori legati alla categoria \"Server\""

#. Translators: %1$s: expands to '<code>robots.txt</code>'.
#: admin/google_search_console/class-gsc-category-filters.php:122
msgid "Show information about errors in category 'Blocked'"
msgstr "Mostra informazioni sugli errori legati alla categoria \"Bloccato\""

#: admin/google_search_console/class-gsc-category-filters.php:120
msgid "Show information about errors in category 'Other'"
msgstr "Mostra informazioni sugli errori legati alla categoria \"Altro\""

#: admin/google_search_console/class-gsc-category-filters.php:119
msgid "Show information about errors in category 'Not Found'"
msgstr "Mostra informazioni sugli errori legati alla categoria \"Non Trovato\""

#: admin/google_search_console/class-gsc-category-filters.php:116
msgid "Show information about errors in category 'Access Denied'"
msgstr "Mostra informazioni  sugli errori legati alla categoria \"Accesso Negato\""

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:51
msgid "New %1$s Title"
msgstr "Nuovo %1$s Titolo"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:49
msgid "Existing %1$s Title"
msgstr "Titolo %1$s Esistente"

#: inc/sitemaps/class-sitemaps-cache-validator.php:295
msgid "Expected an integer as input."
msgstr "L'input dev'essere un intero"

#: inc/sitemaps/class-sitemaps-cache-validator.php:111
msgid "Trying to build the sitemap cache key, but the postfix and prefix combination leaves too little room to do this. You are probably requesting a page that is way out of the expected range."
msgstr "Sto provando a costruire una chiave sicura della cache della sitemap, ma la combinazione di suffisso e prefisso lascia poco spazio di azione. Probabilmente stai cercando di elaborare una pagina con molti caratteri."

#: admin/views/tabs/metas/taxonomies/category-url.php:10
msgid "Remove"
msgstr "Rimuovi"

#: admin/views/tabs/metas/taxonomies/category-url.php:10
msgid "Keep"
msgstr "Mantieni"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:36
msgid "Bold the last page"
msgstr "Evidenzia in grassetto l'ultima pagina"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:31
msgid "Regular"
msgstr "Standard"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:30
msgid "Bold"
msgstr "Grassetto"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:26
msgid "Show Blog page"
msgstr "Vedi la pagina del Blog"

#. translators: %s is the taxonomy title. This will be shown to screenreaders
#: admin/views/js-templates-primary-term.php:39
msgid "Primary %s"
msgstr "%s Primario"

#: admin/views/js-templates-primary-term.php:32
msgid "Primary"
msgstr "Primario"

#. translators: accessibility text. %1$s expands to the term title, %2$s to the
#. taxonomy title.
#: admin/views/js-templates-primary-term.php:18
msgid "Make %1$s primary %2$s"
msgstr "Rendi %1$s primario %2$s"

#: admin/taxonomy/class-taxonomy-columns.php:133
msgid "Term is set to noindex."
msgstr "Il termine è impostato su noidex."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:16
msgid "Author archives"
msgstr "Archivi autore"

#: admin/class-yoast-form.php:730
msgid "Show"
msgstr "Visualizza"

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:13
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:19
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:13
#: admin/class-yoast-form.php:311
msgid "Enabled"
msgstr "Abilitato"

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:14
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:19
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:14
#: admin/class-yoast-form.php:311
msgid "Disabled"
msgstr "Disabilitato"

#. translators: %1$s: expands to Yoast SEO, %2$s: expands to Ryte.
#: admin/onpage/class-ryte-service.php:98
msgid "%1$s has not fetched your site's indexability status yet from %2$s"
msgstr "%1$s non ha ancora calcolato lo stato di indicizzabilità del tuo sito da %2$s"

#. translators: %s: wp_title() function.
#: inc/class-wpseo-replace-vars.php:1314
msgid "The separator defined in your theme's %s tag."
msgstr "Il separatore definito nel tag %s del tuo tema."

#: inc/class-wpseo-rank.php:125
msgid "No index"
msgstr "Noindex"

#: admin/class-yoast-dashboard-widget.php:121
msgid "Analyze entire site"
msgstr "Analizza tutto il sito"

#: admin/class-yoast-dashboard-widget.php:120
msgid "Fetch the current status"
msgstr "Carica lo status attuale"

#. translators: %1$s: opens a link to a related knowledge base article, %2$s:
#. expands to Yoast SEO, %3$s: closes the link, %4$s: expands to Ryte.
#: admin/onpage/class-ryte-service.php:80
msgid "%1$s%2$s has not been able to fetch your site's indexability status%3$s from %4$s"
msgstr "%1$s%2$s non è stato in grado di calcolare lo stato di indicizzabilità del tuo sito%3$s da %4$s"

#: admin/onpage/class-ryte-service.php:57
msgid "Your homepage can be indexed by search engines."
msgstr "La tua home page può essere indicizzata dai motori di ricerca."

#. translators: %1$s expands to Ryte.
#: admin/class-yoast-dashboard-widget.php:116
msgid "Indexability check by %1$s"
msgstr "Controllo indicizzazione di %1$s"

#. translators: %1$s expands to the social network name
#: admin/taxonomy/class-taxonomy-social-fields.php:70
msgid "If you want to use an image for sharing on %1$s, you can upload / choose an image or add the image URL here."
msgstr "Se vuoi usare un'immagine per la condivisione su %1$s, puoi caricare / scegliere un'immagine o aggiungere l'url dell'immagine qui."

#. translators: %1$s expands to the social network name
#: admin/taxonomy/class-taxonomy-social-fields.php:63
msgid "If you don't want to use the meta description for sharing on %1$s but want another description there, write it here."
msgstr "Se non desideri utilizzare la meta descrizione per la condivisione su %1$s ma vuoi un'altra descrizione, scrivila qui."

#. translators: %1$s expands to the social network name
#: admin/taxonomy/class-taxonomy-social-fields.php:55
msgid "If you don't want to use the title for sharing on %1$s but instead want another title there, write it here."
msgstr "Se non vuoi utilizzare il titolo dell'articolo per condividerlo su %1$s  ma preferisci avere un titolo differente, scrivilo qui."

#. translators: %1$s: opens a link to a related knowledge base article. %2$s:
#. closes the link.
#. translators: 1: opens a link to a related knowledge base article. 2: closes
#. the link
#: admin/onpage/class-ryte-service.php:64 admin/onpage/class-onpage.php:172
msgid "%1$sYour homepage cannot be indexed by search engines%2$s. This is very bad for SEO and should be fixed."
msgstr "%1$sLa tua homepage non può essere indicizzata dai motori di ricerca%2$s. Questo è molto grave per la SEO e deve essere corretto."

#: admin/onpage/class-onpage.php:116
msgid "Once Weekly"
msgstr "Una volta alla settimana"

#: admin/metabox/class-metabox.php:410 admin/metabox/class-metabox.php:413
msgid "Add-ons"
msgstr "Add-ons"

#: admin/taxonomy/class-taxonomy-columns.php:64 admin/class-meta-columns.php:67
msgid "SEO score"
msgstr "Punteggio SEO"

#: admin/taxonomy/class-taxonomy-metabox.php:178
#: admin/class-social-admin.php:108 admin/class-social-admin.php:110
msgid "Twitter metadata"
msgstr "Metadati di Twitter"

#: admin/taxonomy/class-taxonomy-metabox.php:177
#: admin/class-social-admin.php:95 admin/class-social-admin.php:97
msgid "Facebook / Open Graph metadata"
msgstr "Metadati Facebook / Open Graph"

#: admin/class-recalculate-scores.php:47
msgid "Recalculating SEO scores for all pieces of content with a focus keyphrase."
msgstr "Ricalcolo dei punteggi SEO per i contenuti con una frase chiave in corso."

#. translators: 1: expands to a <span> containing the number of posts
#. recalculated. 2: expands to a <strong> containing the total number of posts.
#: admin/class-recalculate-scores.php:40
msgid "%1$s of %2$s done."
msgstr "eseguito %1$s di %2$s."

#. translators: 1: is a link to 'admin_url /
#. admin.php?page=wpseo_tools&recalculate=1' 2: closing link tag
#: admin/class-admin-init.php:416
msgid "We've updated our SEO score algorithm. %1$sRecalculate the SEO scores%2$s for all posts and pages."
msgstr "Abbiamo aggiornato i nostri algoritmi per il punteggio SEO. %1$sRicalcola il punteggio SEO%2$s per tutti gli articoli e le pagine."

#. Author of the plugin
msgid "Team Yoast"
msgstr "Team Yoast"

#. Description of the plugin
msgid "The first true all-in-one SEO solution for WordPress, including on-page content analysis, XML sitemaps and much more."
msgstr "La prima vera soluzione SEO tutto-in-uno per WordPress, compresa l&#8217;analisi dei contenuti su ogni pagina, sitemap XML e molto altro."

#. Plugin Name of the plugin
#: admin/capabilities/class-capability-manager-integration.php:72
msgid "Yoast SEO"
msgstr "Yoast SEO"

#: wp-seo-main.php:639
msgid "Activation failed:"
msgstr "Attivazione non riuscita:"

#: wp-seo-main.php:629
msgid "The filter extension seem to be unavailable. Please ask your web host to enable it."
msgstr "L'estensione del filtro sembra non essere disponibile. Suggeriamo di richiedere l'abilitazione al proprio gestore hosting."

#. translators: %1$s expands to Yoast SEO, %2$s / %3$s: links to the
#. installation manual in the Readme for the Yoast SEO code repository on
#. GitHub
#: wp-seo-main.php:605
msgid "The %1$s plugin installation is incomplete. Please refer to %2$sinstallation instructions%3$s."
msgstr "L'installazione del plugin %1$s è incompleta. Consulta le %2$sistruzioni per l'installazione%3$s."

#: wp-seo-main.php:583
msgid "The Standard PHP Library (SPL) extension seem to be unavailable. Please ask your web host to enable it."
msgstr "L'estensione Standard PHP Library (SPL) non pare disponibile. Richiedi al tuo host di abilitarla."

#: inc/class-wpseo-admin-bar-menu.php:393
#: inc/class-wpseo-admin-bar-menu.php:436
msgid "SEO Settings"
msgstr "Impostazioni SEO"

#: inc/class-wpseo-admin-bar-menu.php:358
msgid "Mobile-Friendly Test"
msgstr "Test Mobile-Friendly"

#: inc/class-wpseo-admin-bar-menu.php:353
msgid "Google Page Speed Test"
msgstr "Test Google Page Speed"

#: inc/class-wpseo-admin-bar-menu.php:348
msgid "CSS Validator"
msgstr "Validazione CSS"

#: inc/class-wpseo-admin-bar-menu.php:343
msgid "HTML Validator"
msgstr "Validazione HTML"

#: inc/class-wpseo-admin-bar-menu.php:338
msgid "Pinterest Rich Pins Validator"
msgstr "Pinterest Rich Pins Validator"

#: inc/class-wpseo-admin-bar-menu.php:333
msgid "Facebook Debugger"
msgstr "Debugger Facebook"

#: inc/class-wpseo-admin-bar-menu.php:323
msgid "Check Headers"
msgstr "Controlla gli headers"

#: inc/class-wpseo-admin-bar-menu.php:318
msgid "Check Google Cache"
msgstr "Controlla la Cache di Google"

#: inc/class-wpseo-admin-bar-menu.php:298
msgid "Analyze this page"
msgstr "Analizza questa pagina"

#: inc/class-wpseo-admin-bar-menu.php:240
msgid "Keyword Research"
msgstr "Ricerca Parole chiave"

#. translators: %s expands to an invalid URL.
#: inc/options/class-wpseo-option.php:347
msgid "%s does not seem to be a valid url. Please correct."
msgstr "%s non sembra essere una url valida. Correggi e riprova."

#. translators: 1: Verification string from user input; 2: Service name.
#: inc/options/class-wpseo-option.php:312
msgid "%1$s does not seem to be a valid %2$s verification string. Please correct."
msgstr "%s non sembra essere una string di verifica valida %s. Correggi e riprova."

#. translators: %s expands to the name of a post type (plural).
#: inc/options/class-wpseo-option-titles.php:244
msgid "%s Archive"
msgstr "Archivi %s"

#. translators: %s expands to the search phrase.
#: inc/options/class-wpseo-option-titles.php:207
msgid "You searched for %s"
msgstr "Hai cercato %s"

#. translators: %s expands to a twitter user name.
#: inc/options/class-wpseo-option-social.php:178
msgid "%s does not seem to be a valid Twitter user-id. Please correct."
msgstr "%s non sembra essere uno user-id valido di Twitter. Correggi e riprova."

#: inc/options/class-wpseo-option-social.php:93
msgid "Summary with large image"
msgstr "Sommario con immagini grandi"

#: inc/options/class-wpseo-option-social.php:92
msgid "Summary"
msgstr "Sommario"

#. translators: 1: link to post; 2: link to blog.
#: inc/options/class-wpseo-option-titles.php:210
msgid "The post %1$s appeared first on %2$s."
msgstr "L'articolo %1$s proviene da %2$s."

#: inc/options/class-wpseo-option-ms.php:195
msgid "No numeric value was received."
msgstr "Nessun valore numerico inserito."

#. translators: %s is the ID number of a blog.
#: inc/options/class-wpseo-option-ms.php:183
msgid "This must be an existing blog. Blog %s does not exist or has been marked as deleted."
msgstr "Questo deve essere un blog esistente. Il blog %s non esiste o è stato segnato come cancellato."

#: inc/options/class-wpseo-option-ms.php:179
#: inc/options/class-wpseo-option-ms.php:195
msgid "The default blog setting must be the numeric blog id of the blog you want to use as default."
msgstr "L'impostazione di default del blog deve essere l'id numerico del blog che vuoi usare come default."

#. translators: %1$s expands to the option name and %2$sexpands to Yoast SEO
#: inc/options/class-wpseo-option-ms.php:159
msgid "%1$s is not a valid choice for who should be allowed access to the %2$s settings. Value reset to the default."
msgstr "%1$snon è una scelta valida per chi dovrebbe essere autorizzato ad accedere alle impostazioni  di %2$s. Valori ripristinati al default."

#. translators: %s expands to a taxonomy slug.
#: inc/options/class-wpseo-option-titles.php:452
msgid "Please select a valid post type for taxonomy \"%s\""
msgstr "Seleziona un tipo di contenuto valido per la tassonomia \"%s\""

#. translators: %s expands to a post type.
#: inc/options/class-wpseo-option-titles.php:415
msgid "Please select a valid taxonomy for post type \"%s\""
msgstr "Seleziona una tassonomia valida per i contenuti di tipo \"%s\""

#: inc/options/class-wpseo-option-titles.php:215
msgid "You searched for"
msgstr "Hai cercato"

#: inc/options/class-wpseo-option-titles.php:214
msgid "Home"
msgstr "Home"

#: inc/options/class-wpseo-option-titles.php:213
msgid "Archives for"
msgstr "Archivi per"

#: inc/options/class-wpseo-option-titles.php:212
msgid "Error 404: Page not found"
msgstr "Errore 404: Pagina non trovata"

#: languages/yoast-seo-js.php:491 inc/class-wpseo-rank.php:128
msgid "Good"
msgstr "Buona"

#: inc/class-wpseo-replace-vars.php:1365
msgid "Replaced with a custom taxonomies description"
msgstr "Sostituito con la descrizione di una tassonomia custom"

#: inc/class-wpseo-replace-vars.php:1364
msgid "Replaced with a posts custom taxonomies, comma separated."
msgstr "Sostituito con una tassonomia personalizzata per gli articoli, separata da virgole."

#: inc/class-wpseo-replace-vars.php:1363
msgid "Replaced with a posts custom field value"
msgstr "Sostituita con un valore di un campo personalizzato"

#: inc/class-wpseo-replace-vars.php:1362
msgid "Replaced with the slug which caused the 404"
msgstr "Sostituito con lo slug che ha causato l'errore 404"

#: inc/class-wpseo-replace-vars.php:1361
msgid "Replaced with the posts focus keyphrase"
msgstr "Sostituito dalla frase chiave principale dell'articolo"

#: inc/class-wpseo-replace-vars.php:1360
msgid "Attachment caption"
msgstr "Didascalia Allegato"

#: inc/class-wpseo-replace-vars.php:1359
msgid "Replaced with the current page number"
msgstr "Sostituito con il numero di pagina corrente"

#: inc/class-wpseo-replace-vars.php:1358
msgid "Replaced with the current page total"
msgstr "Sostituito con il numero di pagine totale"

#: inc/class-wpseo-replace-vars.php:1357
msgid "Replaced with the current page number with context (i.e. page 2 of 4)"
msgstr "Sostituito con il numero di pagina corrente e relativo contesto(es. pagina 2 di 4)"

#: inc/class-wpseo-replace-vars.php:1356
msgid "Replaced with the post/page author's 'Biographical Info'"
msgstr "Rimpiazzato con \"Informazioni biografiche\" dell'autore del post/pagina."

#: inc/class-wpseo-replace-vars.php:1355
msgid "Replaced with the post/page author's 'nicename'"
msgstr "Sostituito con il 'nicename' dell'autore del post/pagina"

#: inc/class-wpseo-replace-vars.php:1354
msgid "Replaced with the post/page ID"
msgstr "Sostituito con l'ID del post/pagina"

#: inc/class-wpseo-replace-vars.php:1353
msgid "Replaced with the post/page modified time"
msgstr "Sostituito con l'ora di modifica del post/pagina "

#: inc/class-wpseo-replace-vars.php:1352
msgid "Replaced with the content type plural label"
msgstr "Sostituito con l'etichetta plurima per il tipo di articolo"

#: inc/class-wpseo-replace-vars.php:1351
msgid "Replaced with the content type single label"
msgstr "Sostituito con l'etichetta singola del tipo di articolo"

#: inc/class-wpseo-replace-vars.php:1337
msgid "Replaced with the current search phrase"
msgstr "Sostituito con la frase di ricerca corrente"

#: inc/class-wpseo-replace-vars.php:1336
msgid "Replaced with the term name"
msgstr "Sostituito dal nome del termine"

#: inc/class-wpseo-replace-vars.php:1335
msgid "Replaced with the term description"
msgstr "Sostituito dalla descrizione del termine"

#: inc/class-wpseo-replace-vars.php:1334
msgid "Replaced with the tag description"
msgstr "Sostituito dalla descrizione del tag"

#: inc/class-wpseo-replace-vars.php:1333
msgid "Replaced with the category description"
msgstr "Sostituito dalla descrizione della categoria"

#: inc/class-wpseo-replace-vars.php:1331
msgid "Replaced with the post categories (comma separated)"
msgstr "Sostituito dalle categorie dell'articolo (separate da una virgola)"

#: inc/class-wpseo-replace-vars.php:1330
msgid "Replaced with the current tag/tags"
msgstr "Rimpiazzato dagli attuali tag"

#: inc/class-wpseo-replace-vars.php:1329
msgid "Replaced with the post/page excerpt (without auto-generation)"
msgstr "Sostituito dal riassunto articolo/pagina (senza autogenerazione)"

#: inc/class-wpseo-replace-vars.php:1328
msgid "Replaced with the post/page excerpt (or auto-generated if it does not exist)"
msgstr "Sostituito dal riassunto articolo/pagina (autogenerato se non esiste)"

#: inc/class-wpseo-replace-vars.php:1326
msgid "The site's name"
msgstr "Il nome del sito"

#: inc/class-wpseo-replace-vars.php:1324
msgid "Replaced with the title of the parent page of the current page"
msgstr "Sostituito con il titolo della pagina padre della pagina corrente"

#: inc/class-wpseo-replace-vars.php:1323
msgid "Replaced with the title of the post/page"
msgstr "Sostituito dal titolo del post/pagina"

#: inc/class-wpseo-replace-vars.php:1322
msgid "Replaced with the date of the post/page"
msgstr "Rimpiazzato dalla data articolo/pagina"

#. translators: 1: current page number, 2: total number of pages.
#: inc/class-wpseo-replace-vars.php:932
msgid "Page %1$d of %2$d"
msgstr "Pagina %1$d di %2$d"

#: inc/class-wpseo-replace-vars.php:122
msgid "You cannot overrule a WPSEO standard variable replacement by registering a variable with the same name. Use the \"wpseo_replacements\" filter instead to adjust the replacement value."
msgstr "Non puoi sovrascrivere una variabile di sostituzione standard di WPSEO registrando una variabile con lo stesso nome. Utilizza piuttosto il filtro \"wpseo_replacements\" per aggiustare il valore di sostituzione."

#: inc/class-wpseo-replace-vars.php:118
msgid "A replacement variable with the same name has already been registered. Try making your variable name unique."
msgstr "Una variabile di sostituzione con questo nome è già stata registrata. Prova a rendere il nome di questa variabile ancora più univoco."

#: inc/class-wpseo-replace-vars.php:108
msgid "A replacement variable can not start with \"%%cf_\" or \"%%ct_\" as these are reserved for the WPSEO standard variable variables for custom fields and custom taxonomies. Try making your variable name unique."
msgstr "Una variabile di sostituzione non può iniziare con \"%%cf_\" o \"%%ct_\" perchè sono riservate per le variabili di variabili standard di WPSEO per i campi personalizzati e per le tassonomie personalizzate. Prova a rendere il nome di questa variabile univoco."

#: inc/class-wpseo-replace-vars.php:105
msgid "A replacement variable can only contain alphanumeric characters, an underscore or a dash. Try renaming your variable."
msgstr "Una variabile di sostituzione può contenere solo caratteri alfanumerici, il carattere di sottolineatura o il trattino. Prova a rinoinare la tua variabile."

#. Translators: %1$s resolves to the SEO menu item, %2$s resolves to the Search
#. Appearance submenu item.
#: frontend/class-frontend.php:1174
msgid "Admin only notice: this page does not show a meta description because it does not have one, either write it for this page specifically or go into the [%1$s - %2$s] menu and set up a template."
msgstr "Avviso per l'amministratore: questa pagina non mostra una meta descrizione perché non ne ha una, è necessario scriverla per questa pagina o andare nel menu SEO -> Titoli & Metadati e impostare un template."

#: frontend/class-frontend.php:532
#: inc/options/class-wpseo-option-titles.php:208
msgid "Page not found"
msgstr "Pagina non trovata"

#. translators: %s expands to a time period, i.e. month name, year or specific
#. date.
#. translators: %s expands to the variable used for term title.
#: frontend/class-frontend.php:509 frontend/class-frontend.php:513
#: frontend/class-frontend.php:517
#: inc/options/class-wpseo-option-titles.php:267
msgid "%s Archives"
msgstr "%s Archivi"

#. translators: %s expands to the search phrase.
#: frontend/class-frontend.php:468
msgid "Search for \"%s\""
msgstr "Ricerca per \"%s\""

#: admin/views/user-profile.php:28
msgid "Meta description to use for Author page"
msgstr "Meta descrizione da usare per la pagina autore "

#: admin/views/user-profile.php:24
msgid "Title to use for Author page"
msgstr "Titolo da usare per la pagina autore"

#. translators: %1$s expands to Yoast SEO
#: admin/views/user-profile.php:11
msgid "%1$s settings"
msgstr "Impostazioni %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:15
msgid "Export your %1$s settings"
msgstr "Esporta le tue impostazioni %1$s"

#: admin/class-export.php:60 admin/views/tool-import-export.php:83
#: admin/views/tabs/tool/wpseo-import.php:24
#: admin/views/tabs/tool/wpseo-import.php:36
msgid "Import settings"
msgstr "Importa impostazioni"

#: admin/views/tool-import-export.php:91
#: admin/views/tabs/tool/import-seo.php:18
#: admin/views/tabs/tool/import-seo.php:45
msgid "Import from other SEO plugins"
msgstr "Importa da altri plugin SEO"

#: admin/views/tabs/tool/import-seo.php:72
msgid "Import"
msgstr "Importa"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:141 admin/views/tool-file-editor.php:241
msgid "If you had a %s file and it was editable, you could edit it from here."
msgstr "Se tu avessi un file %s e questo fosse modificabile, potresti modificarlo da qui."

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:159 admin/views/tool-file-editor.php:209
msgid "If your %s were writable, you could edit it from here."
msgstr "Se il tuo %s fosse modificabile, potresti modificarlo da qui."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:122
msgid "You don't have a %s file, create one here:"
msgstr "Non hai un file %s, creane uno qui:"

#: admin/views/tabs/metas/paper-content/rss-content.php:49
msgid "A link to your site, with your site's name and description as anchor text."
msgstr "Un link al tuo sito, con il nome e la descrizione del tuo sito come testo di ancoraggio."

#: admin/views/tabs/metas/paper-content/rss-content.php:45
msgid "A link to your site, with your site's name as anchor text."
msgstr "Un link al tuo sito, con il nome del tuo sito come testo di ancoraggio."

#: admin/views/tabs/metas/paper-content/rss-content.php:41
msgid "A link to the post, with the title as anchor text."
msgstr "Un link all'articolo, con il titolo come testo di ancoraggio."

#: admin/views/tabs/metas/paper-content/rss-content.php:37
msgid "A link to the archive for the post author, with the authors name as anchor text."
msgstr "Un link all'archivio dell'autore dell'articolo, con il nome dell'autore come testo di ancoraggio."

#: admin/views/tabs/metas/paper-content/rss-content.php:20
msgid "You can use the following variables within the content, they will be replaced by the value on the right."
msgstr "È possibile utilizzare le seguenti variabili all'interno del contenuto, verranno sostituite dal valore mostrato qui a destra."

#: admin/views/tabs/metas/paper-content/rss-content.php:15
msgid "Content to put after each post in the feed"
msgstr "Contenuto da inserire dopo qualsiasi post nel feed"

#: admin/views/tabs/metas/paper-content/rss-content.php:14
msgid "Content to put before each post in the feed"
msgstr "Contenuto da inserire prima di ogni post nel feed"

#: admin/views/tabs/metas/rss.php:21
msgid "This feature is used to automatically add content to your RSS, more specifically, it's meant to add links back to your blog and your blog posts, so dumb scrapers will automatically add these links too, helping search engines identify you as the original source of the content."
msgstr "Questa funzionalità è utilizzata per aggiungere automaticamente contenuti al tuo RSS. Più nello specifico, è progettata per aggiungere link che rimandino al tuo blog e ai tuoi articoli, in modo che gli scrapers aggiungeranno a loro volta automaticamente questi links, aiutando i motori di ricerca ad identificarti come la fonte originale dei contenuti."

#. translators: %1$s / %2$s: links to the breadcrumbs implementation page on
#. the Yoast knowledgebase
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:108
msgid "Usage of this breadcrumbs feature is explained in %1$sour knowledge-base article on breadcrumbs implementation%2$s."
msgstr "L'utilizzo della funzione breadcrumbs è spiegata nel %1$snostro articolo nella knowledge-base sull'implementazione dei breadcrumbs%2$s."

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:103
msgid "How to insert breadcrumbs in your theme"
msgstr "Come inserire i breadcrumbs nel tuo tema"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:82
msgid "Blog"
msgstr "Blog"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:78
msgid "Content type archive to show in breadcrumbs for taxonomies"
msgstr "Archivio dei post type da mostrare nei breadcrumbs per le tassonomie"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:47
msgid "Taxonomy to show in breadcrumbs for content types"
msgstr "Tassonomia da mostrare nei breadcrumbs per i post type"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:21
msgid "Breadcrumb for 404 Page"
msgstr "Breadcrumb per la pagina 404"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:20
msgid "Prefix for Search Page breadcrumbs"
msgstr "Prefisso nel breadcrumbs delle pagina di ricerca"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:19
msgid "Prefix for Archive breadcrumbs"
msgstr "Prefisso per gli archivi dei Breadcrumbs"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:18
msgid "Prefix for the breadcrumb path"
msgstr "Prefisso per il percorso dei Breadcrumbs"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:17
msgid "Anchor text for the Homepage"
msgstr "Testo del link per la Home Page"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:16
msgid "Separator between breadcrumbs"
msgstr "Separatore tra i breadcrumbs"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:11
msgid "Enable Breadcrumbs"
msgstr "Abilita i Breadcrumbs"

#: admin/statistics/class-statistics-service.php:74
msgid "Below are your published posts' SEO scores. Now is as good a time as any to start improving some of your posts!"
msgstr "Di seguito sono elencati i punteggi SEO dei tuoi articoli pubblicati. Ora è il momento giusto per iniziare a migliorare alcuni dei tuoi post!"

#: admin/views/tabs/dashboard/dashboard.php:39
msgid "Credits"
msgstr "Crediti"

#: admin/pages/tools.php:73
msgid "&laquo; Back to Tools page"
msgstr "&laquo; Torna alla pagina degli strumenti"

#. translators: %1$s expands to Yoast SEO
#: admin/pages/tools.php:43
msgid "%1$s comes with some very powerful built-in tools:"
msgstr "%1$s viene fornito con alcuni strumenti integrati molto potenti:"

#: admin/pages/tools.php:31
msgid "This tool allows you to quickly change important files for your SEO, like your robots.txt and, if you have one, your .htaccess file."
msgstr "Questo strumento consente di modificare rapidamente file importanti per la SEO, come il vostro robots.txt e, se ne avete uno, il file .htaccess."

#: admin/pages/tools.php:30
msgid "File editor"
msgstr "Modifica file"

#: admin/pages/tools.php:25
msgid "Import settings from other SEO plugins and export your settings for re-use on (another) blog."
msgstr "Importa le impostazioni da altri plugin per la SEO ed esportale per riutilizzarle su un altro blog."

#: admin/pages/tools.php:24
msgid "Import and Export"
msgstr "Importa ed esporta"

#: admin/pages/tools.php:37
msgid "This tool allows you to quickly change titles and descriptions of your posts and pages without having to go into the editor for each page."
msgstr "Questo strumento permette di cambiare rapidamente i titoli e le descrizioni dei tuoi post e pagine senza dover andare nell'editor per ognuno di essi."

#: admin/pages/tools.php:36 admin/views/tool-bulk-editor.php:87
#: admin/views/tool-bulk-editor.php:94 admin/views/tool-file-editor.php:103
msgid "Bulk editor"
msgstr "Editor di massa"

#. translators: %1$s / %2$s expands to a link to pinterest.com's help page.
#: admin/views/tabs/social/pinterest.php:30
msgid "To %1$sconfirm your site with Pinterest%2$s, add the meta tag here:"
msgstr "Per %1$sconvalidare il tuo sito con Pinterest%2$s, aggiungi qui il meta tag:"

#: admin/views/tabs/social/pinterest.php:20
msgid "Pinterest uses Open Graph metadata just like Facebook, so be sure to keep the Open Graph checkbox on the Facebook tab checked if you want to optimize your site for Pinterest."
msgstr "Pinterest usa i metadata Open Graph come Facebook, per questo assicurati di aver spuntato la casella di verifica sulla scheda Facebook se vuoi ottimizzare il tuo sito per Pinterest."

#: admin/views/tabs/social/twitterbox.php:27
msgid "The default card type to use"
msgstr "Il tipo di card di default da usare"

#: admin/views/tabs/social/twitterbox.php:19
msgid "Add Twitter card meta data"
msgstr "Aggiungi i metadati della Twitter card"

#: admin/views/tabs/social/facebook.php:77
msgid "This image is used if the post/page being shared does not contain any images."
msgstr "Questa immagine viene utilizzata se un/a articolo/pagina che vengono condivisi non contengono alcuna immagine."

#: admin/views/tabs/social/facebook.php:71
msgid "Default settings"
msgstr "Impostazioni standard"

#: admin/views/tabs/social/facebook.php:45
msgid "Copy home meta description"
msgstr "Copia la meta descrizione della home"

#: admin/views/tabs/social/facebook.php:43
#: admin/views/tabs/metas/paper-content/rss-content.php:31
#: admin/views/tool-bulk-editor.php:135 inc/class-wpseo-replace-vars.php:1060
msgid "Description"
msgstr "Descrizione"

#: admin/views/tabs/social/facebook.php:41
#: admin/views/tabs/social/facebook.php:73
msgid "Image URL"
msgstr "URL Immagine"

#: admin/views/tabs/social/facebook.php:35
msgid "These are the title, description and image used in the Open Graph meta tags on the front page of your site."
msgstr "Questi sono il titolo, la descrizione e l'immagine utilizzata nei meta tag Open Graph nella pagina principale del tuo sito."

#: admin/views/tabs/social/facebook.php:38
msgid "Frontpage settings"
msgstr "Impostazioni della pagina iniziale"

#: admin/views/tabs/social/facebook.php:18
msgid "Add Open Graph meta data"
msgstr "Aggiungi meta dati Open Graph"

#: admin/config-ui/fields/class-field-profile-url-youtube.php:19
#: admin/views/tabs/social/accounts.php:53
msgid "YouTube URL"
msgstr "URL di YouTube"

#: admin/config-ui/fields/class-field-profile-url-pinterest.php:19
#: admin/views/tabs/social/accounts.php:49
msgid "Pinterest URL"
msgstr "URL di Pinterest"

#: admin/config-ui/fields/class-field-profile-url-myspace.php:19
#: admin/views/tabs/social/accounts.php:45
msgid "MySpace URL"
msgstr "URL di MySpace"

#: admin/config-ui/fields/class-field-profile-url-linkedin.php:19
#: admin/views/tabs/social/accounts.php:41
msgid "LinkedIn URL"
msgstr "URL di LinkedIn"

#: admin/config-ui/fields/class-field-profile-url-instagram.php:19
#: admin/views/tabs/social/accounts.php:37
msgid "Instagram URL"
msgstr "URL di Instagram"

#: admin/config-ui/fields/class-field-profile-url-twitter.php:19
#: admin/views/tabs/social/accounts.php:33
msgid "Twitter Username"
msgstr "Nome utente Twitter"

#: admin/config-ui/fields/class-field-profile-url-facebook.php:19
#: admin/views/tabs/social/accounts.php:29
msgid "Facebook Page URL"
msgstr "URL della Pagina Facebook"

#: admin/pages/social.php:18
msgid "Accounts"
msgstr "Account"

#: admin/views/tabs/network/restore-site.php:32
msgid "Restore site to defaults"
msgstr "Reimposta il sito"

#: admin/views/tabs/network/restore-site.php:23
#: admin/views/tabs/network/restore-site.php:28
msgid "Site ID"
msgstr "ID Sito"

#: admin/views/tabs/network/restore-site.php:16
msgid "Using this form you can reset a site to the default SEO settings."
msgstr "Usando questo modulo sarà possibile impostare il sito con i valori predefiniti di SEO."

#: admin/views/tabs/network/general.php:52
msgid "Privacy sensitive (FB admins and such), theme specific (title rewrite) and a few very site specific settings will not be imported to new sites."
msgstr "I dati sensibili della privacy (amministratori FB e similari), specifici per il tema (riscrittura titolo) ed alcune altre impostazioni specifichennon verranno importate nei nuovi siti."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/network/general.php:45
msgid "Enter the %1$sSite ID%2$s for the site whose settings you want to use as default for all sites that are added to your network. Leave empty for none (i.e. the normal plugin defaults will be used)."
msgstr "Inserisci lo %1$sID del sito%2$s per il sito dal quale ricavare le impostazioni che desideri utilizzare come predefinite per tutti i siti già aggiunti al tuo network. Lascia vuoto per nessun sito (verranno utilizzati i valori predefiniti del plugin)."

#: admin/views/tabs/network/general.php:38
msgid "Choose the site whose settings you want to use as default for all sites that are added to your network. If you choose 'None', the normal plugin defaults will be used."
msgstr "Scegli il sito le cui impostazioni verranno utilizzate come standard per tutti i siti che verranno aggiunti al network. Se scegli 'Nessuno', verranno utilizzati i valori standard del plugin."

#: admin/views/tabs/network/general.php:35
#: admin/views/tabs/network/general.php:41
msgid "New sites in the network inherit their SEO settings from this site"
msgstr "I nuovi siti del network erediteranno le impostazioni SEO da questo sito"

#: admin/views/tabs/network/general.php:26
msgid "Super Admins only"
msgstr "Solo super amministratori"

#: admin/views/tabs/network/general.php:25
msgid "Site Admins (default)"
msgstr "Amministratori del sito (predefinito)"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/general.php:23
msgid "Who should have access to the %1$s settings"
msgstr "Chi dovrebbe avere accesso alle impostazioni di %1$s"

#: admin/class-yoast-network-admin.php:80
msgid "spam"
msgstr "spam"

#: admin/class-yoast-network-admin.php:79
msgid "mature"
msgstr "maturo"

#: admin/class-yoast-network-admin.php:78
msgid "archived"
msgstr "archiviato"

#: admin/class-yoast-network-admin.php:77
msgid "public"
msgstr "pubblico"

#. translators: %s expands to the name of a site within a multisite network.
#: admin/class-yoast-network-admin.php:161
msgid "%s restored to default SEO settings."
msgstr "%s ripristinato ai valori di default SEO."

#: admin/class-yoast-network-admin.php:128
msgid "Settings Updated."
msgstr "Impostazioni aggiornate."

#: admin/views/tabs/metas/paper-content/special-pages.php:26
msgid "404 pages"
msgstr "Pagine 404"

#: admin/views/tabs/metas/paper-content/special-pages.php:21
msgid "Search pages"
msgstr "Pagine di ricerca"

#. translators: %s expands to <code>noindex, follow</code>.
#: admin/views/tabs/metas/paper-content/special-pages.php:15
msgid "These pages will be %s by default, so they will never show up in search results."
msgstr "Queste pagine saranno %s di default, quindi non appariranno mai nei risultati delle ricerche."

#: admin/views/tabs/metas/archives.php:29
msgid "Special Pages"
msgstr "Pagine speciali"

#: admin/views/tabs/metas/archives/help.php:21
msgid "Date-based archives could in some cases also be seen as duplicate content."
msgstr "Gli archivi per data potrebbero in alcuni casi essere rilevati come contenuti duplicati."

#. translators: %s expands to <code>noindex, follow</code>
#: admin/views/tabs/metas/archives/help.php:17
msgid "If this is the case on your site, you can choose to either disable it (which makes it redirect to the homepage), or to add %s to it so it doesn't show up in the search results."
msgstr "Se questo è il caso sul tuo sito, è possibile disabilitarlo  (questo lo reindirizza alla home page), o aggiungergli %s in modo da non mostrarlo nei risultati della ricerca."

#. translators: %1$s / %2$s: links to an article about duplicate content on
#. yoast.com
#: admin/views/tabs/metas/archives/help.php:11
msgid "If you're running a one author blog, the author archive will be exactly the same as your homepage. This is what's called a %1$sduplicate content problem%2$s."
msgstr "Se il tuo blog è curato solo da un autore l'archivio autori sarà esattamente uguale alla tua homepage. Questo è ciò che si intende quando si parla di %1$scontenuti duplicati%2$s."

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:16
msgid "Date archives"
msgstr "Archivi per data"

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:38
msgid "author archives"
msgstr "Archivi autori"

#: languages/wordpress-seojs.php:314 admin/views/tabs/social/facebook.php:42
#: admin/views/tool-bulk-editor.php:133 inc/class-wpseo-replace-vars.php:1323
msgid "Title"
msgstr "Titolo"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:52
#: admin/views/tabs/metas/paper-content/post_type/post-type.php:32
#: admin/views/class-view-utils.php:97
msgid "%1$s Meta Box"
msgstr "Meta Box %1$s"

#: admin/class-yoast-form.php:731
msgid "Hide"
msgstr "Nascondi"

#: admin/views/tabs/metas/paper-content/post_type/post-type.php:26
#: admin/views/class-view-utils.php:91
msgid "Date in Snippet Preview"
msgstr "Data nell'anteprima dello snippet"

#: admin/views/tabs/network/general.php:52
msgid "Take note:"
msgstr "Prendi nota:"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/metas/paper-content/general/homepage.php:50
msgid "You can determine the title and description for the blog page by %1$sediting the blog page itself &raquo;%2$s"
msgstr "Puoi impostare il titolo e la descrizione della pagina blog %1$smodificando la pagina stessa &raquo;%2$s"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/metas/paper-content/general/homepage.php:41
msgid "You can determine the title and description for the front page by %1$sediting the front page itself &raquo;%2$s"
msgstr "Puoi impostare il titolo e la descrizione per la prima pagina %1$smodificando direttamente la prima pagina &raquo;%2$s"

#: admin/views/tabs/metas/paper-content/general/homepage.php:37
msgid "Homepage &amp; Front page"
msgstr "Homepage & prima pagina"

#: admin/config-ui/fields/class-field-separator.php:19
#: admin/views/tabs/metas/paper-content/general/title-separator.php:18
msgid "Title Separator"
msgstr "Separatore del titolo"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/metas/paper-content/general/force-rewrite-title.php:18
msgid "%1$s has auto-detected whether it needs to force rewrite the titles for your pages, if you think it's wrong and you know what you're doing, you can change the setting here."
msgstr "%1$S determina automaticamente quando è necessario forzare la riscrittura dei titoli delle pagine, se pensate sia sbagliato e sapete quello che state facendo potete cambiare le impostazioni da qui."

#: admin/views/tabs/metas/paper-content/general/force-rewrite-title.php:14
msgid "Force rewrite titles"
msgstr "Forza la riscrittura dei titoli"

#: admin/pages/metas.php:24
msgid "Taxonomies"
msgstr "Tassonomie"

#: admin/views/tabs/metas/paper-content/general/homepage.php:21
msgid "Homepage"
msgstr "Homepage"

#. translators: %1$s expands to Yoast SEO.
#: admin/views/licenses.php:111
msgid "%1$s Extensions"
msgstr "Estensioni %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:80 admin/views/licenses.php:92
msgid "Seamlessly integrate WooCommerce with %1$s and get extra features!"
msgstr "Integra al meglio WooCommerce con %1$s ed ottieni funzionalità extra!"

#: admin/class-plugin-availability.php:68 admin/views/licenses.php:40
msgid "Rank better locally and in Google Maps, without breaking a sweat!"
msgstr "Migliora il posizionamento locale e su Google Maps, senza fare nessuno sforzo!"

#: admin/class-plugin-availability.php:58 admin/views/licenses.php:71
msgid "Are you in Google News? Increase your traffic from Google News by optimizing for it!"
msgstr "Sei su Google News? Aumenta il tuo traffico da Google News ottimizzandoti per esso!"

#: admin/class-plugin-availability.php:48 admin/views/licenses.php:56
msgid "Optimize your videos to show them off in search results and get more clicks!"
msgstr "Ottimizza i tuoi video per visualizzarli nei risultati di ricerca ed avere più click!"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:36 admin/views/licenses.php:27
msgid "The premium version of %1$s with more features & support."
msgstr "La versione premium di  %1$s con molte altre funzionalità e l'assistenza."

#: admin/config-ui/fields/class-field-company-or-person.php:24
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:43
msgid "Person"
msgstr "Persona"

#. translators: %1$s opens the link to the Yoast.com article about Google's
#. Knowledge Graph, %2$s closes the link,
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:15
msgid "This data is shown as metadata in your site. It is intended to appear in %1$sGoogle's Knowledge Graph%2$s. You can be either an organization, or a person."
msgstr "Questo dato viene mostrato come dato meta del tuo sito. &Egrave; destinato a comparire nel %1$sKnowledge Graph di Google%2$s. Puoi scegliere se essere una Organizzazione o una Persona."

#: admin/config-ui/fields/class-field-site-name.php:19
msgid "Website name"
msgstr "Nome sito web"

#: admin/pages/dashboard.php:56
msgid "Webmaster Tools"
msgstr "Strumenti per Webmaster"

#: admin/class-admin-init.php:212
msgid "You do not have your postname in the URL of your posts and pages, it is highly recommended that you do. Consider setting your permalink structure to <strong>/%postname%/</strong>."
msgstr "Non appare il titolo dell'articolo nella URL dei tuoi articoli e pagine, si consiglia di inserirlo. Imposta la struttura del tuo permalink con <strong>/%postname%/</strong>."

#: admin/pages/metas.php:27
msgid "RSS"
msgstr "RSS"

#: admin/pages/metas.php:26
msgid "Breadcrumbs"
msgstr "Breadcrumbs"

#: admin/google_search_console/views/gsc-display.php:98
msgid "or"
msgstr "o"

#: admin/google_search_console/views/gsc-display.php:98
msgid "Save Profile"
msgstr "Salva profilo"

#: languages/yoast-components.php:77
#: admin/google_search_console/views/gsc-display.php:91
msgid "There were no profiles found"
msgstr "Nessun profilo trovato"

#: admin/google_search_console/views/gsc-display.php:87
msgid "Profile"
msgstr "Profilo"

#: admin/google_search_console/views/gsc-display.php:65
msgid "Current profile"
msgstr "Profilo corrente"

#: languages/yoast-components.php:62
#: admin/google_search_console/views/gsc-display.php:60
msgid "Authenticate"
msgstr "Autenticati"

#: languages/yoast-components.php:53
#: admin/google_search_console/views/gsc-display.php:54
msgid "Get Google Authorization Code"
msgstr "Ricevi Codice di Autorizzazione di Google."

#: admin/google_search_console/views/gsc-display.php:17
msgid "Reload crawl issues"
msgstr "Ricarica i problemi di indicizzazione"

#. Translators: %1$s: expands to 'Yoast SEO Premium', %2$s: links to Yoast SEO
#. Premium plugin page.
#: admin/google_search_console/views/gsc-redirect-nopremium.php:22
msgid "To be able to create a redirect and fix this issue, you need %1$s. You can buy the plugin, including one year of support and updates, on %2$s."
msgstr "Per essere in grado di creare un redirect e risolvere questo problema, hai bisogno di %1$s. È possibile acquistare il plugin, compreso un anno di assistenza e aggiornamenti, su %2$s."

#. Translators: %s: expands to Yoast SEO Premium
#: languages/wordpress-seojs.php:145
#: admin/google_search_console/views/gsc-redirect-nopremium.php:15
msgid "Creating redirects is a %s feature"
msgstr "Creare redirect è una caratteristica di %s"

#: admin/google_search_console/class-gsc.php:336
msgid "Errors that only occurred when your site was crawled by Googlebot for feature phones (errors didn't appear for desktop)."
msgstr "Errori che si sono verificati quando il tuo sito è stato scansionato da Googlebot per feature phone (gli errori non sono comparsi per la versione desktop)."

#: admin/google_search_console/class-gsc.php:335
msgid "Errors that occurred only when your site was crawled by Googlebot-Mobile (errors didn't appear for desktop)."
msgstr "Errori che si sono verificati solo quando il tuo sito è stato scansionato da Googlebot-Mobile (gli errori non sono comparsi per la versione desktop)."

#: admin/google_search_console/class-gsc.php:334
msgid "Errors that occurred when your site was crawled by Googlebot."
msgstr "Errori che si sono verificati quando il tuo sito è stato scansionato da Googlebot."

#: admin/google_search_console/class-gsc.php:333
msgid "Issue categories"
msgstr "Categorie di problemi"

#: admin/google_search_console/class-gsc.php:266
msgid "Incorrect Google Authorization Code."
msgstr "Google Authorization Code Errato."

#: admin/google_search_console/class-gsc.php:221
msgid "The issues have been successfully reloaded!"
msgstr "I problemi sono stati ricaricati con successo!"

#. Translators: %1$s: expands to Google Search Console.
#: admin/google_search_console/class-gsc.php:208
msgid "The %1$s data has been removed. You will have to reauthenticate if you want to retrieve the data again."
msgstr "I dati di %1$s sono stato rimossi. Sarà necessario ripetere l'autenticazione se si desidera recuperare i dati nuovamente."

#: admin/google_search_console/class-gsc.php:164
msgid "Crawl errors per page"
msgstr "Errori di scansione per pagina"

#: languages/yoast-components.php:119
#: admin/google_search_console/class-gsc.php:147
#: admin/class-help-center.php:254
msgid "Search"
msgstr "Ricerca"

#. translators: 1: link open tag; 2: link close tag.
#: admin/google_search_console/class-gsc.php:101
msgid "Don't miss your crawl errors: %1$sconnect with Google Search Console here%2$s."
msgstr "Non perderti gli errori del crawl: %1$scollegati alla Google Search Console qui%2$s."

#: admin/google_search_console/class-gsc-table.php:361
#: admin/google_search_console/class-gsc-table.php:370
msgid "Create redirect"
msgstr "Crea un Redirect"

#: admin/google_search_console/class-gsc-table.php:134
#: admin/google_search_console/class-gsc-table.php:203
msgid "Mark as fixed"
msgstr "Segna come risolto"

#: admin/google_search_console/class-gsc-table.php:105
msgid "Response code"
msgstr "Codice di risposta"

#: admin/google_search_console/class-gsc-table.php:104
msgid "First detected"
msgstr "Scoperto per la prima volta"

#: admin/google_search_console/class-gsc-table.php:103
msgid "Last crawled"
msgstr "Ultima scansione"

#: admin/google_search_console/class-gsc-table.php:102
msgid "URL"
msgstr "URL"

#. translators: %1$s expands to Yoast SEO, %2$s expands to Google Analytics by
#. Yoast
#: admin/google_search_console/class-gsc-service.php:149
msgid "%1$s detected you’re using a version of %2$s which is not compatible with %1$s. Please update %2$s to the latest version to use this feature."
msgstr "%1$s ha scoperto che stai utilizzando la versione %2$s che non è compatibile con %1$s. Aggiorna %2$s all'ultima versione per per utilizzare questa funzionalità. "

#: admin/google_search_console/class-gsc-service.php:140
msgid "Yoast plugins share some code between them to make your site faster. As a result of that, we need all Yoast plugins to be up to date. We've detected this isn't the case, so please update the Yoast plugins that aren't up to date yet."
msgstr "I plugin di Yoast condividono parte del codice tra di loro per rendere il sito più veloce. Per questo motivo abbiamo bisogno che tutti i plugin di Yoast siano aggiornati. Abbiamo rilevato che in questo caso non è così, quindi si prega di aggiornare i plugin di Yoast che non sono ancora aggiornati."

#: admin/google_search_console/class-gsc-platform-tabs.php:47
#: admin/google_search_console/class-gsc.php:336
msgid "Feature phone"
msgstr "Feature phone"

#: admin/google_search_console/class-gsc-platform-tabs.php:46
#: admin/google_search_console/class-gsc.php:335
msgid "Smartphone"
msgstr "Smartphone"

#: admin/google_search_console/class-gsc-platform-tabs.php:45
#: admin/google_search_console/class-gsc.php:334
msgid "Desktop"
msgstr "Desktop"

#: admin/google_search_console/class-gsc-category-filters.php:124
msgid "The target URL doesn't exist, but your server is not returning a 404 (file not found) error."
msgstr "L'URL di destinazione non esiste, ma il server non restituisce un errore 404 (file non trovato)."

#: admin/google_search_console/class-gsc-category-filters.php:124
msgid "Soft 404"
msgstr "Soft 404"

#: admin/google_search_console/class-gsc-category-filters.php:123
msgid "Request timed out or site is blocking Google."
msgstr "La richiesta è scaduta o il sito sta bloccando Google."

#: admin/google_search_console/class-gsc-category-filters.php:123
msgid "Server Error"
msgstr "Errore del server"

#. Translators: %1$s: expands to '<code>robots.txt</code>'.
#: admin/google_search_console/class-gsc-category-filters.php:122
msgid "Googlebot could access your site, but certain URLs are blocked for Googlebot in your %1$s file. This block could either be for all Googlebots or even specifically for Googlebot-mobile."
msgstr "Googlebot può accedere al tuo sito, ma alcuni URL sono bloccati per il Googlebot nel tuo file %1$s. Questo blocco potrebbe essere sia per tutti i Googlebots o anche specifico per Googlebot-mobile."

#. Translators: %1$s: expands to '<code>robots.txt</code>'.
#: admin/google_search_console/class-gsc-category-filters.php:122
msgid "Blocked"
msgstr "Bloccato"

#: admin/google_search_console/class-gsc-category-filters.php:120
msgid "Google was unable to crawl this URL due to an undetermined issue."
msgstr "Google non è stato in grado di controllare questo URL a causa di un problema sconosciuto."

#: admin/google_search_console/class-gsc-category-filters.php:119
msgid "URL points to a non-existent page."
msgstr "URL punta a una pagina che non esiste"

#: admin/google_search_console/class-gsc-category-filters.php:119
msgid "Not found"
msgstr "Non trovato"

#: admin/google_search_console/class-gsc-category-filters.php:118
msgid "Not followed"
msgstr "Non seguito"

#: admin/google_search_console/class-gsc-category-filters.php:117
msgid "Faulty redirects"
msgstr "Redirect difettosi"

#: admin/google_search_console/class-gsc-category-filters.php:116
msgid "Server requires authentication or is blocking Googlebot from accessing the site."
msgstr "Il server richiede una autenticazione o sta bloccando l'accesso al sito del googlebot."

#: admin/google_search_console/class-gsc-category-filters.php:116
msgid "Access denied"
msgstr "Accesso negato"

#. translators: %s: 'Facebook' plugin name of possibly conflicting plugin
#: admin/class-yoast-plugin-conflict.php:206
msgid "Deactivate %s"
msgstr "Disattivare %s"

#. translators: %1$s: 'Facebook & Open Graph' plugin name(s) of possibly
#. conflicting plugin(s), %2$s to Yoast SEO
#: admin/class-yoast-plugin-conflict.php:202
msgid "The %1$s plugin might cause issues when used in conjunction with %2$s."
msgstr "I plugin %1$s potrebbero causare problemi quando utilizzati in combinazione con %2$s."

#: admin/metabox/class-metabox.php:566
#: admin/taxonomy/class-taxonomy-fields-presenter.php:119
#: admin/class-yoast-form.php:557
msgid "Upload Image"
msgstr "Carica immagine"

#: admin/views/sidebar.php:128
msgid "Remove these ads?"
msgstr "Rimuovi queste pubblicità?"

#. translators: %s is the plugin name
#: admin/class-yoast-dashboard-widget.php:62
msgid "%s Posts Overview"
msgstr "Panoramica degli articoli %s"

#: admin/taxonomy/class-taxonomy-settings-fields.php:45
msgid "The Breadcrumbs Title is used in the breadcrumbs where this taxonomy appears."
msgstr "Il Titolo dei breadcrumbs è usato quando appare questa tassonomia."

#: admin/views/tabs/metas/paper-content/post-type-content.php:63
msgid "Breadcrumbs title"
msgstr "Titolo dei breadcrumbs"

#: admin/taxonomy/class-taxonomy-settings-fields.php:52
msgid "The canonical link is shown on the archive page for this term."
msgstr "Il link alla URL canonical è mostrato nella pagina archivio per questo termine."

#: admin/views/tabs/social/facebook.php:29
msgid "Facebook App ID"
msgstr "Facebook App ID"

#: admin/pages/social.php:21
msgid "Pinterest"
msgstr "Pinterest"

#: admin/google_search_console/class-gsc-category-filters.php:120
msgid "Other"
msgstr "Altro"

#: frontend/class-frontend.php:520 admin/pages/metas.php:25
msgid "Archives"
msgstr "Archivi"

#: languages/yoast-components.php:83
msgid "Email"
msgstr "Email"

#: languages/yoast-components.php:38
msgid "Previous"
msgstr "Precedente"

#: languages/yoast-components.php:35
msgid "Next"
msgstr "Successivo"

#: languages/wordpress-seojs.php:39 languages/yoast-components.php:44
#: admin/google_search_console/views/gsc-redirect-nopremium.php:27
msgid "Close"
msgstr "Chiudi"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Google XML Sitemaps' plugin
#. name of possibly conflicting plugin with regard to the creation of sitemaps.
#: admin/class-plugin-conflict.php:153
msgid "Both %1$s and %2$s can create XML sitemaps. Having two XML sitemaps is not beneficial for search engines and might slow down your site."
msgstr "Sia %1$s che %2$s generano una mappa del sito XML. Avere due file XML non porta benefici per i motori di ricerca e potrebbe rallentare il sito."

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:146
msgid "Configure %1$s's OpenGraph settings"
msgstr "Configura le impostazioni OpenGraph di %1$s"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Facebook' plugin name of
#. possibly conflicting plugin with regard to creating OpenGraph output.
#: admin/class-plugin-conflict.php:142
msgid "Both %1$s and %2$s create OpenGraph output, which might make Facebook, Twitter, LinkedIn and other social networks use the wrong texts and images when your pages are being shared."
msgstr "Sia  %1$s che  %2$s generano dei meta tag OpenGraph, che potrebbero causare un'aspetto indesiderato dei vostri contenuti qualora fossero condivisi su Facebook, Twitter, LinkedIn ed altri social."

#. translators: %s expands to the social network name
#. translators: %s expands to the name of a social network.
#: admin/taxonomy/class-taxonomy-social-fields.php:68
#: admin/class-social-admin.php:66
msgid "%s Image"
msgstr "Immagine per %s"

#. translators: %s expands to the social network name
#. translators: %s expands to the name of a social network.
#: admin/taxonomy/class-taxonomy-social-fields.php:61
#: admin/class-social-admin.php:62
msgid "%s Description"
msgstr "Descrizione per %s"

#. translators: %s expands to the social network name
#. translators: %s expands to the name of a social network.
#: admin/taxonomy/class-taxonomy-social-fields.php:53
#: admin/class-social-admin.php:58
msgid "%s Title"
msgstr "Titolo per %s"

#: admin/pages/social.php:20
#: admin/taxonomy/class-taxonomy-social-fields.php:122
#: admin/class-social-admin.php:43 admin/class-social-admin.php:169
msgid "Twitter"
msgstr "Twitter"

#: admin/pages/social.php:19
#: admin/taxonomy/class-taxonomy-social-fields.php:121
#: admin/class-social-admin.php:42 admin/class-social-admin.php:166
msgid "Facebook"
msgstr "Facebook"

#. translators: %1$s expands to the social network name, %2$s expands to the
#. image size
#. translators: %1$s expands to the social network, %2$s to the recommended
#. image size.
#: admin/taxonomy/class-taxonomy-social-fields.php:72
#: admin/class-social-admin.php:39
msgid "The recommended image size for %1$s is %2$s pixels."
msgstr "La dimensione dell'immagine consigliata per %1$s è di %2$s pixel."

#. translators: %s expands to the social network's name.
#: admin/class-social-admin.php:36
msgid "If you want to override the image used on %s for this post, upload / choose an image here."
msgstr "Se vuoi sovrascrivere l'immagine usata su %s per questo articolo, carica / scegli un&#8217;immagine qui."

#. translators: %s expands to the social network's name.
#: admin/class-social-admin.php:33
msgid "If you don't want to use the meta description for sharing the post on %s but want another description there, write it here."
msgstr "Se non vuoi usare la meta descrizione per la condivisione dell'articolo su %s, e vuoi usare un'altra descrizione, inseriscila qui."

#. translators: %s expands to the social network's name.
#: admin/class-social-admin.php:30
msgid "If you don't want to use the post title for sharing the post on %s but instead want another title there, write it here."
msgstr "Se non vuoi utilizzare il titolo del post nella condivisione su %s, ma desideri un altro titolo, scrivilo qui."

#: languages/yoast-seo-js.php:33
msgid "very difficult"
msgstr "molto difficile"

#: languages/yoast-seo-js.php:30
msgid "Try to make shorter sentences, using less difficult words to improve readability"
msgstr "Prova a scrivere frasi più brevi, usando parole meno difficili per migliorare la leggibilità. "

#: languages/yoast-seo-js.php:26
msgid "difficult"
msgstr "difficile"

#: languages/yoast-seo-js.php:23
msgid "Try to make shorter sentences to improve readability"
msgstr "Prova a creare frasi più brevi per migliorare la leggibilità."

#: languages/yoast-seo-js.php:20
msgid "fairly difficult"
msgstr "abbastanza difficile"

#: languages/yoast-seo-js.php:482 inc/class-wpseo-rank.php:127
msgid "OK"
msgstr "OK"

#: languages/yoast-seo-js.php:14
msgid "fairly easy"
msgstr "abbastanza facile"

#: languages/yoast-seo-js.php:11
msgid "easy"
msgstr "facile"

#: languages/yoast-seo-js.php:8
msgid "very easy"
msgstr "molto facile"

#: admin/class-meta-columns.php:75
msgid "Meta Desc."
msgstr "Desc. meta"

#: admin/class-meta-columns.php:205
msgid "All SEO Scores"
msgstr "Tutti i punteggi SEO"

#: inc/class-wpseo-rank.php:145
msgid "SEO: Post Noindexed"
msgstr "SEO: Articolo in noindex"

#: inc/class-wpseo-rank.php:144
msgid "SEO: Good"
msgstr "SEO: Buono"

#: inc/class-wpseo-rank.php:143
msgid "SEO: OK"
msgstr "SEO: OK"

#: admin/metabox/class-metabox.php:805
msgid "SEO issue: The featured image should be at least 200 by 200 pixels to be picked up by Facebook and other social media sites."
msgstr "Problema SEO: l'immagine in evidenza deve avere una dimensione di almeno 200 per 200 pixel per poter essere utilizzata da Facebook e altri social media."

#: admin/metabox/class-metabox.php:205 admin/taxonomy/class-taxonomy.php:246
msgid "(no parent)"
msgstr "(nessun genitore)"

#: admin/class-meta-columns.php:607
msgid "Post is set to noindex."
msgstr "Articolo impostato come da non indicizzare"

#: admin/metabox/class-metabox.php:106
msgid "The URL that this page should redirect to."
msgstr "La URL alla quale questa pagina deve reindirizzare."

#: admin/metabox/class-metabox.php:105
msgid "301 Redirect"
msgstr "Redirect 301"

#. translators: 1: link open tag; 2: link close tag.
#: admin/metabox/class-metabox.php:100
msgid "The canonical URL that this page should point to. Leave empty to default to permalink. %1$sCross domain canonical%2$s supported too."
msgstr "La URL canonica a cui questa pagina dovrebbe puntare. Lascia in bianco per utilizzare il permalink predefinito. Sono supportate anche le %1$sURL canoniche cross domain%2$s."

#: admin/metabox/class-metabox.php:96
#: admin/taxonomy/class-taxonomy-settings-fields.php:51
msgid "Canonical URL"
msgstr "Canonical URL"

#: admin/metabox/class-metabox.php:94
msgid "Title to use for this page in breadcrumb paths"
msgstr "Titolo da usare nel percorso del breadcrumb in questa pagina"

#: admin/metabox/class-metabox.php:93
#: admin/taxonomy/class-taxonomy-settings-fields.php:44
msgid "Breadcrumbs Title"
msgstr "Titolo dei breadcrumbs"

#: admin/metabox/class-metabox.php:91
msgid "No Snippet"
msgstr "Nessuno snippet"

#: admin/metabox/class-metabox.php:90
msgid "No Archive"
msgstr "Nessun Archivio"

#: admin/metabox/class-metabox.php:89
msgid "No Image Index"
msgstr "Nessuna immagine indice"

#: admin/metabox/class-metabox.php:88 admin/class-yoast-network-admin.php:43
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:51
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:80
#: inc/class-wpseo-meta.php:363
msgid "None"
msgstr "Nessuno"

#. translators: %s expands to the advanced robots settings default as set in
#. the site-wide settings.
#: admin/metabox/class-metabox.php:87
msgid "Site-wide default: %s"
msgstr "Default di sistema: %s"

#: admin/metabox/class-metabox.php:85
msgid "Advanced <code>meta</code> robots settings for this page."
msgstr "Impostazioni Avanzate <code>meta</code> robots per questa pagina."

#: admin/metabox/class-metabox.php:84
msgid "Meta robots advanced"
msgstr "Meta Robots avanzate"

#: admin/metabox/class-metabox.php:72
msgid "Warning: even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect."
msgstr "Attenzione: anche se da qui puoi impostare i valori meta per le impostazioni per i robot, l'intero sito è impostato come da non indicizzare nelle impostazioni generali di privacy, quindi queste impostazioni non avranno alcun effetto."

#: languages/yoast-seo-js.php:512 languages/yoast-components.php:199
#: admin/metabox/class-metabox.php:67
msgid "Meta description"
msgstr "Meta descrizione"

#: admin/class-meta-columns.php:74
msgid "SEO Title"
msgstr "Titolo SEO"

#: inc/class-wpseo-replace-vars.php:1361
msgid "Focus keyword"
msgstr "Parola chiave principale"

#: languages/wordpress-seojs.php:35 languages/yoast-seo-js.php:518
msgid "Snippet preview"
msgstr "Anteprima dello snippet"

#: admin/import/class-import-settings.php:113
msgid "Settings successfully imported."
msgstr "Impostazioni importate con successo."

#: admin/import/class-import-settings.php:81
msgid "Settings could not be imported:"
msgstr "Le impostazioni non possono essere importate:"

#. translators: %1$s expands to Yoast SEO
#: admin/class-export.php:83
msgid "Error creating %1$s export: "
msgstr "Errore nella creazione dell'export di %1$s: "

#: admin/class-customizer.php:191
msgid "Breadcrumb for 404 pages:"
msgstr "Breadcrumb per le pagine 404:"

#: admin/class-customizer.php:179
msgid "Prefix for search result pages:"
msgstr "Prefisso per le pagine dei risultati della ricerca:"

#: admin/class-customizer.php:167
msgid "Prefix for archive pages:"
msgstr "Prefisso per le pagine archivio:"

#: admin/class-customizer.php:155
msgid "Prefix for breadcrumbs:"
msgstr "Prefisso per i breadcrumb:"

#: admin/class-customizer.php:143
msgid "Anchor text for the homepage:"
msgstr "Anchor text per la homepage:"

#: admin/class-customizer.php:130
msgid "Breadcrumbs separator:"
msgstr "Separatore dei Breadcrumb:"

#: admin/class-customizer.php:107
msgid "Remove blog page from breadcrumbs"
msgstr "Rimuovi la pagina del blog dai breadcrumb"

#. translators: %s is the name of the plugin
#: admin/class-customizer.php:84
msgid "%s Breadcrumbs"
msgstr "%s Breadcrumb"

#: admin/metabox/class-metabox.php:817 admin/class-config.php:114
#: admin/taxonomy/class-taxonomy.php:146
msgid "Use Image"
msgstr "Usa immagine"

#: admin/class-bulk-editor-list-table.php:992
msgid "Action"
msgstr "Azione"

#: admin/class-bulk-editor-list-table.php:987
msgid "Page URL/Slug"
msgstr "URL/Slug pagina"

#: admin/class-bulk-editor-list-table.php:986
msgid "Publication date"
msgstr "Data di pubblicazione"

#: admin/class-bulk-editor-list-table.php:985
msgid "Post Status"
msgstr "Stato articolo"

#: admin/class-bulk-editor-list-table.php:983
msgid "WP Page Title"
msgstr "Titolo pagina WP"

#: admin/class-bulk-editor-list-table.php:818
#: admin/google_search_console/class-gsc-table.php:202
msgid "View"
msgstr "Visualizza"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:817
msgid "View &#8220;%s&#8221;"
msgstr "Visualizza &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:808
msgid "Preview"
msgstr "Anteprima"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:807
msgid "Preview &#8220;%s&#8221;"
msgstr "Anteprima &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:796
msgid "Edit"
msgstr "Modifica"

#: admin/class-bulk-editor-list-table.php:409
msgid "Filter"
msgstr "Filtro"

#. translators: %s expands to the number of trashed posts in localized format.
#: admin/class-bulk-editor-list-table.php:334
msgctxt "posts"
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] "Cestinato <span class=\"count\">(%s)</span>"
msgstr[1] "Cestinati <span class=\"count\">(%s)</span>"

#. translators: %s expands to the number of posts in localized format.
#: admin/class-bulk-editor-list-table.php:284
msgctxt "posts"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] "Tutto <span class=\"count\">(%s)</span>"
msgstr[1] "Tutto <span class=\"count\">(%s)</span>"

#: admin/class-bulk-description-editor-list-table.php:47
msgid "New Yoast Meta Description"
msgstr "Nuova meta descrizione Yoast"

#: admin/class-bulk-description-editor-list-table.php:46
msgid "Existing Yoast Meta Description"
msgstr "Meta descrizione Yoast esistente"

#: admin/class-admin.php:266
msgid "Facebook profile URL"
msgstr "URL profilo Facebook"

#: admin/class-admin.php:273
msgid "Twitter username (without @)"
msgstr "Username Twitter (senza @)"

#: languages/wordpress-seojs.php:157 admin/class-admin.php:230
msgid "FAQ"
msgstr "FAQ"

#: admin/class-admin.php:226
msgid "Premium Support"
msgstr "Supporto Premium"

#: languages/wordpress-seojs.php:227 admin/class-admin.php:216
#: admin/google_search_console/class-gsc-platform-tabs.php:42
#: admin/taxonomy/class-taxonomy-metabox.php:150
#: admin/taxonomy/class-taxonomy-metabox.php:158
#: admin/taxonomy/class-taxonomy-metabox.php:161
msgid "Settings"
msgstr "Impostazioni"

#. translators: %1$s resolves to the opening tag of the link to the reading
#. settings, %1$s resolves to the closing tag for the link
#: admin/class-admin-init.php:136
msgid "You must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr "Devi %1$sandare nelle Impostazioni Lettura%2$s e rimuovere la casella di spunta per la Visibilità ai motori di ricerca."

#: admin/class-admin-init.php:133
msgid "Huge SEO Issue: You're blocking access to robots."
msgstr "Grande problema SEO: stai bloccando l'accesso ai robot."

#: admin/class-admin.php:180
msgid "Posts"
msgstr "Articoli"

#: admin/menu/class-network-admin-menu.php:63
msgid "Edit Files"
msgstr "Modifica Files"

#: admin/help_center/class-template-variables-tab.php:93
msgid "Advanced Variables"
msgstr "Variabile Avanzate"

#: admin/help_center/class-template-variables-tab.php:87
msgid "Basic Variables"
msgstr "Variabili di base"

#: admin/menu/class-admin-menu.php:88
#: admin/menu/class-network-admin-menu.php:56 admin/pages/metas.php:21
#: admin/pages/network.php:18
msgid "General"
msgstr "Generale"

#: admin/menu/class-network-admin-menu.php:66
msgid "Extensions"
msgstr "Estensioni"

#: admin/menu/class-admin-menu.php:91
msgid "Search Console"
msgstr "Console di ricerca"

#: admin/menu/class-admin-menu.php:97
msgid "Tools"
msgstr "Strumenti"

#: admin/metabox/class-metabox.php:372 admin/metabox/class-metabox.php:378
#: admin/metabox/class-metabox.php:381
msgid "Advanced"
msgstr "Avanzate"

#: admin/views/class-yoast-feature-toggles.php:104
msgid "XML sitemaps"
msgstr "Sitemaps XML"

#: admin/menu/class-admin-menu.php:96
#: admin/taxonomy/class-taxonomy-metabox.php:182
#: admin/taxonomy/class-taxonomy-metabox.php:185
#: admin/class-social-admin.php:119 admin/class-social-admin.php:122
msgid "Social"
msgstr "Social"

#: frontend/class-frontend.php:1175 admin/menu/class-admin-menu.php:38
#: admin/menu/class-network-admin-menu.php:35
#: inc/class-wpseo-admin-bar-menu.php:467
msgid "SEO"
msgstr "SEO"

#. translators: %1$s expands to Yoast SEO, %2$s expands to 5.4.3, %3$s expands
#. to Google Analytics by Yoast
#. translators: %1$s expands to Yoast SEO, %2$s expands to the plugin version,
#. %3$s expands to the plugin name
#. translators: %1$s expands to Yoast SEO, %2$s expands to the installed
#. version, %3$s expands to Gutenberg
#: admin/class-admin-init.php:273 admin/class-admin-init.php:376
#: admin/class-admin-gutenberg-compatibility-notification.php:72
msgid "%1$s detected you are using version %2$s of %3$s, please update to the latest version to prevent compatibility issues."
msgstr "%1$s ha scoperto che stai utilizzando la versione %2$s di %3$s, aggiorna all'ultima versione per evitare problemi di compatibilità."

#: admin/class-admin-init.php:202
msgid "Just another WordPress site"
msgstr "Solo un altro sito WordPress"

#. translators: 1: link open tag; 2: link close tag.
#: admin/class-admin-init.php:106
msgid "You still have the default WordPress tagline, even an empty one is probably better. %1$sYou can fix this in the customizer%2$s."
msgstr "Hai ancora il motto standard di WordPress, persino un motto vuoto è probabilmente migliore. %1$sPuoi correggerlo in personalizza%2$s."

#: admin/ajax.php:217
msgid "You have used HTML in your value which is not allowed."
msgstr "Non è consentito utilizzare HTML."

#. translators: %s expands to the name of a post type (plural).
#: admin/ajax.php:207
msgid "You can't edit %s that aren't yours."
msgstr "Non puoi modificare i %s che non sono tuoi."

#. translators: %s expands to post type name.
#: admin/ajax.php:195
msgid "You can't edit %s."
msgstr "Non puoi modificare %s."

#. translators: %s expands to post type.
#: admin/ajax.php:183
msgid "Post has an invalid Content Type: %s."
msgstr "L'articolo ha impostato un tipo invalido: %s."

#: admin/ajax.php:172
msgid "Post doesn't exist."
msgstr "L'articolo non esiste."