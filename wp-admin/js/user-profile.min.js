!function(a){function b(){return"function"!=typeof zxcvbn?void setTimeout(b,50):(k.val()?(g(),h()):(k.val(k.data("pw")),k.trigger("pwupdate"),h()),1!==parseInt(q.data("start-masked"),10)?j.addClass("show-password"):q.trigger("click"),void a("#pw-weak-text-label").html(userProfileL10n.warnWeak))}function c(){t=k.val(),j=k.parent(),l=a('<input type="text"/>').attr({id:"pass1-text",name:"pass1-text",autocomplete:"off"}).addClass(k[0].className).data("pw",k.data("pw")).val(k.val()).on(u,function(){l.val()!==t&&(n.val(l.val()),k.val(l.val()).trigger("pwupdate"),t=l.val())}),k.after(l),1===parseInt(k.data("reveal"),10)&&b(),k.on(u+" pwupdate",function(){k.val()!==t&&(t=k.val(),l.val()!==t&&l.val(t),k.add(l).removeClass("short bad good strong"),h())})}function d(){q.data("toggle",0).attr({"aria-label":userProfileL10n.ariaHide}).find(".text").text(userProfileL10n.hide).end().find(".dashicons").removeClass("dashicons-visibility").addClass("dashicons-hidden"),l.focus(),m.attr("for","pass1-text")}function e(){q=i.find(".wp-hide-pw"),q.show().on("click",function(){1===parseInt(q.data("toggle"),10)?(j.addClass("show-password"),d(),_.isUndefined(l[0].setSelectionRange)||l[0].setSelectionRange(0,100)):(j.removeClass("show-password"),q.data("toggle",1).attr({"aria-label":userProfileL10n.ariaShow}).find(".text").text(userProfileL10n.show).end().find(".dashicons").removeClass("dashicons-hidden").addClass("dashicons-visibility"),k.focus(),m.attr("for","pass1"),_.isUndefined(k[0].setSelectionRange)||k[0].setSelectionRange(0,100))})}function f(){var f,g,h;i=a(".user-pass1-wrap"),m=i.find("th label").attr("for","pass1-text"),a(".user-pass2-wrap").hide(),s=a("#submit, #wp-submit").on("click",function(){v=!1}),r=s.add(" #createusersub"),o=a(".pw-weak"),p=o.find(".pw-checkbox"),p.change(function(){r.prop("disabled",!p.prop("checked"))}),k=a("#pass1"),k.length&&c(),n=a("#pass2").on(u,function(){n.val().length>0&&(k.val(n.val()),n.val(""),t="",k.trigger("pwupdate"))}),k.is(":hidden")&&(k.prop("disabled",!0),n.prop("disabled",!0),l.prop("disabled",!0)),f=i.find(".wp-pwd"),g=i.find("button.wp-generate-pw"),e(),g.length&&f.hide(),g.show(),g.on("click",function(){v=!0,g.hide(),f.show(),k.attr("disabled",!1),n.attr("disabled",!1),l.attr("disabled",!1),0===l.val().length&&b(),_.defer(function(){l.focus(),_.isUndefined(l[0].setSelectionRange)||l[0].setSelectionRange(0,100)},0)}),h=i.find("button.wp-cancel-pw"),h.on("click",function(){v=!1,l.val(""),wp.ajax.post("generate-password").done(function(a){k.data("pw",a)}),g.show(),f.hide(),o.hide(0,function(){p.removeProp("checked")}),k.prop("disabled",!0),n.prop("disabled",!0),l.prop("disabled",!0),d(),i.closest("form").is("#your-profile")&&(k.val("").trigger("pwupdate"),r.prop("disabled",!1))}),i.closest("form").on("submit",function(){v=!1,k.prop("disabled",!1),n.prop("disabled",!1),n.val(k.val()),j.removeClass("show-password")})}function g(){var b,c=a("#pass1").val();if(a("#pass-strength-result").removeClass("short bad good strong"),!c)return void a("#pass-strength-result").html("&nbsp;");switch(b=wp.passwordStrength.meter(c,wp.passwordStrength.userInputBlacklist(),c)){case-1:a("#pass-strength-result").addClass("bad").html(pwsL10n.unknown);break;case 2:a("#pass-strength-result").addClass("bad").html(pwsL10n.bad);break;case 3:a("#pass-strength-result").addClass("good").html(pwsL10n.good);break;case 4:a("#pass-strength-result").addClass("strong").html(pwsL10n.strong);break;case 5:a("#pass-strength-result").addClass("short").html(pwsL10n.mismatch);break;default:a("#pass-strength-result").addClass("short").html(pwsL10n["short"])}}function h(){var b=a("#pass-strength-result")[0];b.className&&(k.add(l).addClass(b.className),a(b).is(".short, .bad")?(p.prop("checked")||r.prop("disabled",!0),o.show()):(r.prop("disabled",!1),o.hide()))}var i,j,k,l,m,n,o,p,q,r,s,t,u,v=!1;u="oninput"in document.createElement("input")?"input":"keyup",a(document).ready(function(){var b,c,d,e,h=a("#display_name"),i=h.val(),j=a("#wp-admin-bar-my-account").find(".display-name");a("#pass1").val("").on(u+" pwupdate",g),a("#pass-strength-result").show(),a(".color-palette").click(function(){a(this).siblings('input[name="admin_color"]').prop("checked",!0)}),h.length&&(a("#first_name, #last_name, #nickname").bind("blur.user_profile",function(){var b=[],c={display_nickname:a("#nickname").val()||"",display_username:a("#user_login").val()||"",display_firstname:a("#first_name").val()||"",display_lastname:a("#last_name").val()||""};c.display_firstname&&c.display_lastname&&(c.display_firstlast=c.display_firstname+" "+c.display_lastname,c.display_lastfirst=c.display_lastname+" "+c.display_firstname),a.each(a("option",h),function(a,c){b.push(c.value)}),a.each(c,function(d,e){if(e){var f=e.replace(/<\/?[a-z][^>]*>/gi,"");c[d].length&&a.inArray(f,b)===-1&&(b.push(f),a("<option />",{text:f}).appendTo(h))}})}),h.on("change",function(){if(d===e){var b=a.trim(this.value)||i;j.text(b)}})),b=a("#color-picker"),c=a("#colors-css"),d=a("input#user_id").val(),e=a('input[name="checkuser_id"]').val(),b.on("click.colorpicker",".color-option",function(){var b,f=a(this);if(!f.hasClass("selected")&&(f.siblings(".selected").removeClass("selected"),f.addClass("selected").find('input[type="radio"]').prop("checked",!0),d===e)){if(0===c.length&&(c=a('<link rel="stylesheet" />').appendTo("head")),c.attr("href",f.children(".css_url").val()),"undefined"!=typeof wp&&wp.svgPainter){try{b=a.parseJSON(f.children(".icon_colors").val())}catch(g){}b&&(wp.svgPainter.setColors(b),wp.svgPainter.paint())}a.post(ajaxurl,{action:"save-user-color-scheme",color_scheme:f.children('input[name="admin_color"]').val(),nonce:a("#color-nonce").val()}).done(function(b){b.success&&a("body").removeClass(b.data.previousScheme).addClass(b.data.currentScheme)})}}),f()}),a("#destroy-sessions").on("click",function(b){var c=a(this);wp.ajax.post("destroy-sessions",{nonce:a("#_wpnonce").val(),user_id:a("#user_id").val()}).done(function(a){c.prop("disabled",!0),c.siblings(".notice").remove(),c.before('<div class="notice notice-success inline"><p>'+a.message+"</p></div>")}).fail(function(a){c.siblings(".notice").remove(),c.before('<div class="notice notice-error inline"><p>'+a.message+"</p></div>")}),b.preventDefault()}),window.generatePassword=b,a(window).on("beforeunload",function(){if(!0===v)return userProfileL10n.warn})}(jQuery);