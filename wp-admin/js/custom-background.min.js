!function(n){n(document).ready(function(){var c,o=n("#custom-background-image");n("#background-color").wpColorPicker({change:function(n,c){o.css("background-color",c.color.toString())},clear:function(){o.css("background-color","")}}),n('select[name="background-size"]').change(function(){o.css("background-size",n(this).val())}),n('input[name="background-position"]').change(function(){o.css("background-position",n(this).val())}),n('input[name="background-repeat"]').change(function(){o.css("background-repeat",n(this).is(":checked")?"repeat":"no-repeat")}),n('input[name="background-attachment"]').change(function(){o.css("background-attachment",n(this).is(":checked")?"scroll":"fixed")}),n("#choose-from-library-link").click(function(o){var e=n(this);o.preventDefault(),c?c.open():((c=wp.media.frames.customBackground=wp.media({title:e.data("choose"),library:{type:"image"},button:{text:e.data("update"),close:!1}})).on("select",function(){var o=c.state().get("selection").first(),e=n("#_wpnonce").val()||"";n.post(ajaxurl,{action:"set-background-image",attachment_id:o.id,_ajax_nonce:e,size:"full"}).done(function(){window.location.reload()})}),c.open())})})}(jQuery);