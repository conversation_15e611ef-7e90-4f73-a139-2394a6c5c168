"undefined"==typeof window.wp&&(window.wp={}),"undefined"==typeof window.wp.codeEditor&&(window.wp.codeEditor={}),function(a,b){"use strict";function c(b,c){function d(){c.onUpdateErrorNotice&&!_.isEqual(f,g)&&(c.onUpdateErrorNotice(f,b),g=f)}function e(){var e=b.getOption("lint");return!!e&&(!0===e?e={}:_.isObject(e)&&(e=a.extend({},e)),e.options||(e.options={}),"javascript"===c.codemirror.mode&&c.jshint&&a.extend(e.options,c.jshint),"css"===c.codemirror.mode&&c.csslint&&a.extend(e.options,c.csslint),"htmlmixed"===c.codemirror.mode&&c.htmlhint&&(e.options.rules=a.extend({},c.htmlhint),c.jshint&&(e.options.rules.jshint=c.jshint),c.csslint&&(e.options.rules.csslint=c.csslint)),e.onUpdateLinting=function(a){return function(e,h,i){var j=_.filter(e,function(a){return"error"===a.severity});a&&a.apply(e,h,i),_.isEqual(j,f)||(f=j,c.onChangeLintingErrors&&c.onChangeLintingErrors(j,e,h,i),(!b.state.focused||0===f.length||g.length>0)&&d())}}(e.onUpdateLinting),e)}var f=[],g=[];b.setOption("lint",e()),b.on("optionChange",function(a,c){var g,h,i="CodeMirror-lint-markers";"lint"===c&&(h=b.getOption("gutters")||[],g=b.getOption("lint"),!0===g?(_.contains(h,i)||b.setOption("gutters",[i].concat(h)),b.setOption("lint",e())):g||b.setOption("gutters",_.without(h,i)),b.getOption("lint")?b.performLint():(f=[],d()))}),b.on("blur",d),b.on("startCompletion",function(){b.off("blur",d)}),b.on("endCompletion",function(){var a=500;b.on("blur",d),_.delay(function(){b.state.focused||d()},a)}),a(document.body).on("mousedown",function(c){!b.state.focused||a.contains(b.display.wrapper,c.target)||a(c.target).hasClass("CodeMirror-hint")||d()})}function d(b,c){var d=a(b.getTextArea());b.on("blur",function(){d.data("next-tab-blurs",!1)}),b.on("keydown",function(a,e){var f=9,g=27;return g===e.keyCode?void d.data("next-tab-blurs",!0):void(f===e.keyCode&&d.data("next-tab-blurs")&&(e.shiftKey?c.onTabPrevious(b,e):c.onTabNext(b,e),d.data("next-tab-blurs",!1),e.preventDefault()))})}b.codeEditor.defaultSettings={codemirror:{},csslint:{},htmlhint:{},jshint:{},onTabNext:function(){},onTabPrevious:function(){},onChangeLintingErrors:function(){},onUpdateErrorNotice:function(){}},b.codeEditor.initialize=function(e,f){var g,h,i,j;return g=a("string"==typeof e?"#"+e:e),i=a.extend({},b.codeEditor.defaultSettings,f),i.codemirror=a.extend({},i.codemirror),h=b.CodeMirror.fromTextArea(g[0],i.codemirror),c(h,i),j={settings:i,codemirror:h},h.showHint&&h.on("keyup",function(a,c){var d,e,f,g,i=/^[a-zA-Z]$/.test(c.key);h.state.completionActive&&i||(g=h.getTokenAt(h.getCursor()),"string"!==g.type&&"comment"!==g.type&&(f=b.CodeMirror.innerMode(h.getMode(),g.state).mode.name,e=h.doc.getLine(h.doc.getCursor().line).substr(0,h.doc.getCursor().ch),"html"===f||"xml"===f?d="<"===c.key||"/"===c.key&&"tag"===g.type||i&&"tag"===g.type||i&&"attribute"===g.type||"="===g.string&&g.state.htmlState&&g.state.htmlState.tagName:"css"===f?d=i||":"===c.key||" "===c.key&&/:\s+$/.test(e):"javascript"===f?d=i||"."===c.key:"clike"===f&&"application/x-httpd-php"===h.options.mode&&(d="keyword"===g.type||"variable"===g.type),d&&h.showHint({completeSingle:!1})))}),d(h,f),j}}(window.jQuery,window.wp);