jQuery(document).ready(function(a){a("#the-list").on("click",".delete-tag",function(){var b,c=a(this),d=c.parents("tr"),e=!0;return"undefined"!=showNotice&&(e=showNotice.warn()),e&&(b=c.attr("href").replace(/[^?]*\?/,"").replace(/action=delete/,"action=delete-tag"),a.post(ajaxurl,b,function(c){"1"==c?(a("#ajax-response").empty(),d.fadeOut("normal",function(){d.remove()}),a('select#parent option[value="'+b.match(/tag_ID=(\d+)/)[1]+'"]').remove(),a("a.tag-link-"+b.match(/tag_ID=(\d+)/)[1]).remove()):"-1"==c?(a("#ajax-response").empty().append('<div class="error"><p>'+tagsl10n.noPerm+"</p></div>"),d.children().css("backgroundColor","")):(a("#ajax-response").empty().append('<div class="error"><p>'+tagsl10n.broken+"</p></div>"),d.children().css("backgroundColor",""))}),d.children().css("backgroundColor","#f33")),!1}),a("#edittag").on("click",".delete",function(a){if("undefined"==typeof showNotice)return!0;var b=showNotice.warn();b||a.preventDefault()}),a("#submit").click(function(){var b=a(this).parents("form");return!!validateForm(b)&&(a.post(ajaxurl,a("#addtag").serialize(),function(c){var d,e,f,g,h;if(a("#ajax-response").empty(),d=wpAjax.parseAjaxResponse(c,"ajax-response"),d&&!d.errors){if(e=b.find("select#parent").val(),e>0&&a("#tag-"+e).length>0?a(".tags #tag-"+e).after(d.responses[0].supplemental.noparents):a(".tags").prepend(d.responses[0].supplemental.parents),a(".tags .no-items").remove(),b.find("select#parent")){for(f=d.responses[1].supplemental,g="",h=0;h<d.responses[1].position;h++)g+="&nbsp;&nbsp;&nbsp;";b.find("select#parent option:selected").after('<option value="'+f.term_id+'">'+g+f.name+"</option>")}a('input[type="text"]:visible, textarea:visible',b).val("")}}),!1)})});