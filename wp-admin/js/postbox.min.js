var postboxes;!function(a){var b=a(document);postboxes={handle_click:function(){var c,d=a(this),e=d.parent(".postbox"),f=e.attr("id");"dashboard_browser_nag"!==f&&(e.toggleClass("closed"),c=!e.hasClass("closed"),d.hasClass("handlediv")?d.attr("aria-expanded",c):d.closest(".postbox").find("button.handlediv").attr("aria-expanded",c),"press-this"!==postboxes.page&&postboxes.save_state(postboxes.page),f&&(!e.hasClass("closed")&&a.isFunction(postboxes.pbshow)?postboxes.pbshow(f):e.hasClass("closed")&&a.isFunction(postboxes.pbhide)&&postboxes.pbhide(f)),b.trigger("postbox-toggled",e))},add_postbox_toggles:function(c,d){var e=a(".postbox .hndle, .postbox .handlediv");this.page=c,this.init(c,d),e.on("click.postboxes",this.handle_click),a(".postbox .hndle a").click(function(a){a.stopPropagation()}),a(".postbox a.dismiss").on("click.postboxes",function(b){var c=a(this).parents(".postbox").attr("id")+"-hide";b.preventDefault(),a("#"+c).prop("checked",!1).triggerHandler("click")}),a(".hide-postbox-tog").bind("click.postboxes",function(){var d=a(this),e=d.val(),f=a("#"+e);d.prop("checked")?(f.show(),a.isFunction(postboxes.pbshow)&&postboxes.pbshow(e)):(f.hide(),a.isFunction(postboxes.pbhide)&&postboxes.pbhide(e)),postboxes.save_state(c),postboxes._mark_area(),b.trigger("postbox-toggled",f)}),a('.columns-prefs input[type="radio"]').bind("click.postboxes",function(){var b=parseInt(a(this).val(),10);b&&(postboxes._pb_edit(b),postboxes.save_order(c))})},init:function(c,d){var e=a(document.body).hasClass("mobile"),f=a(".postbox .handlediv");a.extend(this,d||{}),a("#wpbody-content").css("overflow","hidden"),a(".meta-box-sortables").sortable({placeholder:"sortable-placeholder",connectWith:".meta-box-sortables",items:".postbox",handle:".hndle",cursor:"move",delay:e?200:0,distance:2,tolerance:"pointer",forcePlaceholderSize:!0,helper:function(a,b){return b.clone().find(":input").attr("name",function(a,b){return"sort_"+parseInt(1e5*Math.random(),10).toString()+"_"+b}).end()},opacity:.65,stop:function(){var b=a(this);return b.find("#dashboard_browser_nag").is(":visible")&&"dashboard_browser_nag"!=this.firstChild.id?void b.sortable("cancel"):void postboxes.save_order(c)},receive:function(c,d){"dashboard_browser_nag"==d.item[0].id&&a(d.sender).sortable("cancel"),postboxes._mark_area(),b.trigger("postbox-moved",d.item)}}),e&&(a(document.body).bind("orientationchange.postboxes",function(){postboxes._pb_change()}),this._pb_change()),this._mark_area(),f.each(function(){var b=a(this);b.attr("aria-expanded",!b.parent(".postbox").hasClass("closed"))})},save_state:function(b){var c,d;"nav-menus"!==b&&(c=a(".postbox").filter(".closed").map(function(){return this.id}).get().join(","),d=a(".postbox").filter(":hidden").map(function(){return this.id}).get().join(","),a.post(ajaxurl,{action:"closed-postboxes",closed:c,hidden:d,closedpostboxesnonce:jQuery("#closedpostboxesnonce").val(),page:b}))},save_order:function(b){var c,d=a(".columns-prefs input:checked").val()||0;c={action:"meta-box-order",_ajax_nonce:a("#meta-box-order-nonce").val(),page_columns:d,page:b},a(".meta-box-sortables").each(function(){c["order["+this.id.split("-")[0]+"]"]=a(this).sortable("toArray").join(",")}),a.post(ajaxurl,c)},_mark_area:function(){var b=a("div.postbox:visible").length,c=a("#post-body #side-sortables");a("#dashboard-widgets .meta-box-sortables:visible").each(function(){var c=a(this);1==b||c.children(".postbox:visible").length?c.removeClass("empty-container"):(c.addClass("empty-container"),c.attr("data-emptyString",postBoxL10n.postBoxEmptyString))}),c.length&&(c.children(".postbox:visible").length?c.removeClass("empty-container"):"280px"==a("#postbox-container-1").css("width")&&c.addClass("empty-container"))},_pb_edit:function(b){var c=a(".metabox-holder").get(0);c&&(c.className=c.className.replace(/columns-\d+/,"columns-"+b)),a(document).trigger("postboxes-columnchange")},_pb_change:function(){var b=a('label.columns-prefs-1 input[type="radio"]');switch(window.orientation){case 90:case-90:b.length&&b.is(":checked")||this._pb_edit(2);break;case 0:case 180:a("#poststuff").length?this._pb_edit(1):b.length&&b.is(":checked")||this._pb_edit(2)}},pbshow:!1,pbhide:!1}}(jQuery);