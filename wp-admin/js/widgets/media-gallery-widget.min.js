!function(a){"use strict";var b,c,d;d=wp.media.view.MediaFrame.Post.extend({createStates:function(){this.states.add([new wp.media.controller.Library({id:"gallery",title:wp.media.view.l10n.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!0,library:wp.media.query(_.defaults({type:"image"},this.options.library))}),new wp.media.controller.GalleryEdit({library:this.options.selection,editing:this.options.editing,menu:"gallery"}),new wp.media.controller.GalleryAdd])}}),b=a.MediaWidgetModel.extend({}),c=a.MediaWidgetControl.extend({events:_.extend({},a.MediaWidgetControl.prototype.events,{"click .media-widget-gallery-preview":"editMedia"}),initialize:function(b){var c=this;a.MediaWidgetControl.prototype.initialize.call(c,b),_.bindAll(c,"updateSelectedAttachments","handleAttachmentDestroy"),c.selectedAttachments=new wp.media.model.Attachments,c.model.on("change:ids",c.updateSelectedAttachments),c.selectedAttachments.on("change",c.renderPreview),c.selectedAttachments.on("reset",c.renderPreview),c.updateSelectedAttachments(),wp.customize&&wp.customize.previewer&&c.selectedAttachments.on("change",function(){wp.customize.previewer.send("refresh-widget-partial",c.model.get("widget_id"))})},updateSelectedAttachments:function(){var a,b,c,d,e,f=this;a=f.model.get("ids"),b=_.pluck(f.selectedAttachments.models,"id"),c=_.difference(b,a),_.each(c,function(a){f.selectedAttachments.remove(f.selectedAttachments.get(a))}),d=_.difference(a,b),d.length&&(e=wp.media.query({order:"ASC",orderby:"post__in",perPage:-1,post__in:a,query:!0,type:"image"}),e.more().done(function(){f.selectedAttachments.reset(e.models)}))},renderPreview:function(){var a,b,c,d=this;a=d.$el.find(".media-widget-preview"),b=wp.template("wp-media-widget-gallery-preview"),c=d.previewTemplateProps.toJSON(),c.attachments={},d.selectedAttachments.each(function(a){c.attachments[a.id]=a.toJSON()}),a.html(b(c))},isSelected:function(){var a=this;return!a.model.get("error")&&a.model.get("ids").length>0},editMedia:function(){var a,b,c,e=this;a=new wp.media.model.Selection(e.selectedAttachments.models,{multiple:!0}),c=e.mapModelToMediaFrameProps(e.model.toJSON()),a.gallery=new Backbone.Model(c),c.size&&e.displaySettings.set("size",c.size),b=new d({frame:"manage",text:e.l10n.add_to_widget,selection:a,mimeType:e.mime_type,selectedDisplaySettings:e.displaySettings,showDisplaySettings:e.showDisplaySettings,metadata:c,editing:!0,multiple:!0,state:"gallery-edit"}),wp.media.frame=b,b.on("update",function(a){var c,d=b.state();c=a||d.get("selection"),c&&(c.gallery&&e.model.set(e.mapMediaToModelProps(c.gallery.toJSON())),e.selectedAttachments.reset(c.models),e.model.set({ids:_.pluck(c.models,"id")}))}),b.$el.addClass("media-widget"),b.open(),a&&a.on("destroy",e.handleAttachmentDestroy)},selectMedia:function(){var a,b,c,e=this;a=new wp.media.model.Selection(e.selectedAttachments.models,{multiple:!0}),c=e.mapModelToMediaFrameProps(e.model.toJSON()),c.size&&e.displaySettings.set("size",c.size),b=new d({frame:"select",text:e.l10n.add_to_widget,selection:a,mimeType:e.mime_type,selectedDisplaySettings:e.displaySettings,showDisplaySettings:e.showDisplaySettings,metadata:c,state:"gallery"}),wp.media.frame=b,b.on("update",function(a){var c,d=b.state();c=a||d.get("selection"),c&&(c.gallery&&e.model.set(e.mapMediaToModelProps(c.gallery.toJSON())),e.selectedAttachments.reset(c.models),e.model.set({ids:_.pluck(c.models,"id")}))}),b.$el.addClass("media-widget"),b.open(),a&&a.on("destroy",e.handleAttachmentDestroy),b.$el.find(":focusable:first").focus()},handleAttachmentDestroy:function(a){var b=this;b.model.set({ids:_.difference(b.model.get("ids"),[a.id])})}}),a.controlConstructors.media_gallery=c,a.modelConstructors.media_gallery=b}(wp.mediaWidgets);