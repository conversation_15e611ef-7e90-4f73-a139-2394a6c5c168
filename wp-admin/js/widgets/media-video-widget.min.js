!function(a){"use strict";var b,c,d;d=wp.media.view.MediaFrame.VideoDetails.extend({createStates:function(){this.states.add([new wp.media.controller.VideoDetails({media:this.media}),new wp.media.controller.MediaLibrary({type:"video",id:"add-video-source",title:wp.media.view.l10n.videoAddSourceTitle,toolbar:"add-video-source",media:this.media,menu:!1}),new wp.media.controller.MediaLibrary({type:"text",id:"add-track",title:wp.media.view.l10n.videoAddTrackTitle,toolbar:"add-track",media:this.media,menu:"video-details"})])}}),b=a.MediaWidgetModel.extend({}),c=a.MediaWidgetControl.extend({showDisplaySettings:!1,oembedResponses:{},mapModelToMediaFrameProps:function(b){var c,d=this;return c=a.MediaWidgetControl.prototype.mapModelToMediaFrameProps.call(d,b),c.link="embed",c},fetchEmbed:function(){var a,b=this;a=b.model.get("url"),b.oembedResponses[a]||(b.fetchEmbedDfd&&"pending"===b.fetchEmbedDfd.state()&&b.fetchEmbedDfd.abort(),b.fetchEmbedDfd=wp.apiRequest({url:wp.media.view.settings.oEmbedProxyUrl,data:{url:b.model.get("url"),maxwidth:b.model.get("width"),maxheight:b.model.get("height"),discover:!1},type:"GET",dataType:"json",context:b}),b.fetchEmbedDfd.done(function(c){b.oembedResponses[a]=c,b.renderPreview()}),b.fetchEmbedDfd.fail(function(){b.oembedResponses[a]=null}))},isHostedVideo:function(){return!0},renderPreview:function(){var a,b,c,d,e,f,g,h,i,j=this,k="",l=!1;c=j.model.get("attachment_id"),d=j.model.get("url"),g=j.model.get("error"),(c||d)&&(f=j.selectedAttachment.get("mime"),f&&c?_.contains(_.values(wp.media.view.settings.embedMimes),f)||(g="unsupported_file_type"):c||(h=document.createElement("a"),h.href=d,i=h.pathname.toLowerCase().match(/\.(\w+)$/),i?_.contains(_.keys(wp.media.view.settings.embedMimes),i[1])||(g="unsupported_file_type"):l=!0),l&&(j.fetchEmbed(),j.oembedResponses[d]&&(e=j.oembedResponses[d].thumbnail_url,k=j.oembedResponses[d].html.replace(/\swidth="\d+"/,' width="100%"').replace(/\sheight="\d+"/,""))),a=j.$el.find(".media-widget-preview"),b=wp.template("wp-media-widget-video-preview"),a.html(b({model:{attachment_id:c,html:k,src:d,poster:e},is_oembed:l,error:g})),wp.mediaelement.initialize())},editMedia:function(){var a,b,c,e=this;b=e.mapModelToMediaFrameProps(e.model.toJSON()),a=new d({frame:"video",state:"video-details",metadata:b}),wp.media.frame=a,a.$el.addClass("media-widget"),c=function(a){e.selectedAttachment.set(a),e.model.set(_.extend(_.omit(e.model.defaults(),"title"),e.mapMediaToModelProps(a),{error:!1}))},a.state("video-details").on("update",c),a.state("replace-video").on("replace",c),a.on("close",function(){a.detach()}),a.open()}}),a.controlConstructors.media_video=c,a.modelConstructors.media_video=b}(wp.mediaWidgets);