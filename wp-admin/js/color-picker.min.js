!function(a,b){var c,d='<button type="button" class="button wp-color-result" aria-expanded="false"><span class="wp-color-result-text"></span></button>',e='<div class="wp-picker-holder" />',f='<div class="wp-picker-container" />',g='<input type="button" class="button button-small" />',h="<label></label>",i='<span class="screen-reader-text"></span>';c={options:{defaultColor:!1,change:!1,clear:!1,hide:!0,palettes:!0,width:255,mode:"hsv",type:"full",slider:"horizontal"},_createHueOnly:function(){var b,c=this,d=c.element;d.hide(),b="hsl("+d.val()+", 100, 50)",d.iris({mode:"hsl",type:"hue",hide:!1,color:b,change:function(b,d){a.isFunction(c.options.change)&&c.options.change.call(this,b,d)},width:c.options.width,slider:c.options.slider})},_create:function(){if(a.support.iris){var b=this,c=b.element;if(a.extend(b.options,c.data()),"hue"===b.options.type)return b._createHueOnly();b.close=a.proxy(b.close,b),b.initialValue=c.val(),c.addClass("wp-color-picker"),c.parent("label").length||(c.wrap(h),b.wrappingLabelText=a(i).insertBefore(c).text(wpColorPickerL10n.defaultLabel)),b.wrappingLabel=c.parent(),b.wrappingLabel.wrap(f),b.wrap=b.wrappingLabel.parent(),b.toggler=a(d).insertBefore(b.wrappingLabel).css({backgroundColor:b.initialValue}),b.toggler.find(".wp-color-result-text").text(wpColorPickerL10n.pick),b.pickerContainer=a(e).insertAfter(b.wrappingLabel),b.button=a(g),b.options.defaultColor?b.button.addClass("wp-picker-default").val(wpColorPickerL10n.defaultString).attr("aria-label",wpColorPickerL10n.defaultAriaLabel):b.button.addClass("wp-picker-clear").val(wpColorPickerL10n.clear).attr("aria-label",wpColorPickerL10n.clearAriaLabel),b.wrappingLabel.wrap('<span class="wp-picker-input-wrap hidden" />').after(b.button),b.inputWrapper=c.closest(".wp-picker-input-wrap"),c.iris({target:b.pickerContainer,hide:b.options.hide,width:b.options.width,mode:b.options.mode,palettes:b.options.palettes,change:function(c,d){b.toggler.css({backgroundColor:d.color.toString()}),a.isFunction(b.options.change)&&b.options.change.call(this,c,d)}}),c.val(b.initialValue),b._addListeners(),b.options.hide||b.toggler.click()}},_addListeners:function(){var b=this;b.wrap.on("click.wpcolorpicker",function(a){a.stopPropagation()}),b.toggler.click(function(){b.toggler.hasClass("wp-picker-open")?b.close():b.open()}),b.element.change(function(c){var d=a(this),e=d.val();""!==e&&"#"!==e||(b.toggler.css("backgroundColor",""),a.isFunction(b.options.clear)&&b.options.clear.call(this,c))}),b.button.click(function(c){var d=a(this);d.hasClass("wp-picker-clear")?(b.element.val(""),b.toggler.css("backgroundColor",""),a.isFunction(b.options.clear)&&b.options.clear.call(this,c)):d.hasClass("wp-picker-default")&&b.element.val(b.options.defaultColor).change()})},open:function(){this.element.iris("toggle"),this.inputWrapper.removeClass("hidden"),this.wrap.addClass("wp-picker-active"),this.toggler.addClass("wp-picker-open").attr("aria-expanded","true"),a("body").trigger("click.wpcolorpicker").on("click.wpcolorpicker",this.close)},close:function(){this.element.iris("toggle"),this.inputWrapper.addClass("hidden"),this.wrap.removeClass("wp-picker-active"),this.toggler.removeClass("wp-picker-open").attr("aria-expanded","false"),a("body").off("click.wpcolorpicker",this.close)},color:function(a){return a===b?this.element.iris("option","color"):void this.element.iris("option","color",a)},defaultColor:function(a){return a===b?this.options.defaultColor:void(this.options.defaultColor=a)}},a.widget("wp.wpColorPicker",c)}(jQuery);