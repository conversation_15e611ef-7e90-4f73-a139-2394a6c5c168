jQuery(function(a){a("body").bind("click.wp-gallery",function(b){var c,d,e=a(b.target);e.hasClass("wp-set-header")?((window.dialogArguments||opener||parent||top).location.href=e.data("location"),b.preventDefault()):e.hasClass("wp-set-background")&&(c=e.data("attachment-id"),d=a('input[name="attachments['+c+'][image-size]"]:checked').val(),jQuery.post(ajaxurl,{action:"set-background-image",attachment_id:c,size:d},function(){var a=window.dialogArguments||opener||parent||top;a.tb_remove(),a.location.reload()}),b.preventDefault())})});