var commentsBox,pm_sanitize,WPSetThumbnailHTML,WPSetThumbnailID,WPRemoveThumbnail,wptitlehint,makeSlugeditClickable,editPermalink;makeSlugeditClickable=editPermalink=function(){},window.wp=window.wp||{},function(t){var e=!1;commentsBox={st:0,get:function(e,i){var n,a=this.st;return i||(i=20),this.st+=i,this.total=e,t("#commentsdiv .spinner").addClass("is-active"),n={action:"get-comments",mode:"single",_ajax_nonce:t("#add_comment_nonce").val(),p:t("#post_ID").val(),start:a,number:i},t.post(ajaxurl,n,function(e){if(e=wpAjax.parseAjaxResponse(e),t("#commentsdiv .widefat").show(),t("#commentsdiv .spinner").removeClass("is-active"),"object"==typeof e&&e.responses[0])return t("#the-comment-list").append(e.responses[0].data),theList=theExtraList=null,t("a[className*=':']").unbind(),void(commentsBox.st>commentsBox.total?t("#show-comments").hide():t("#show-comments").show().children("a").html(postL10n.showcomm));1!=e?t("#the-comment-list").append('<tr><td colspan="2">'+wpAjax.broken+"</td></tr>"):t("#show-comments").html(postL10n.endcomm)}),!1},load:function(t){this.st=jQuery("#the-comment-list tr.comment:visible").length,this.get(t)}},pm_sanitize={stripTags:function(t){return(t=t||"").replace(/<!--[\s\S]*?(-->|$)/g,"").replace(/<(script|style)[^>]*>[\s\S]*?(<\/\1>|$)/gi,"").replace(/<\/?[a-z][\s\S]*?(>|$)/gi,"")},stripTagsAndEncodeText:function(t){var e=pm_sanitize.stripTags(t),i=document.createElement("textarea");try{i.innerHTML=e,e=pm_sanitize.stripTags(i.value)}catch(t){}return e}},WPSetThumbnailHTML=function(e){t(".inside","#postimagediv").html(e)},WPSetThumbnailID=function(e){var i=t('input[value="_thumbnail_id"]',"#list-table");i.length>0&&t("#meta\\["+i.attr("id").match(/[0-9]+/)+"\\]\\[value\\]").text(e)},WPRemoveThumbnail=function(e){t.post(ajaxurl,{action:"set-post-thumbnail",post_id:t("#post_ID").val(),thumbnail_id:-1,_ajax_nonce:e,cookie:encodeURIComponent(document.cookie)},function(t){"0"==t?alert(setPostThumbnailL10n.error):WPSetThumbnailHTML(t)})},t(document).on("heartbeat-send.refresh-lock",function(e,i){var n=t("#active_post_lock").val(),a=t("#post_ID").val(),s={};a&&t("#post-lock-dialog").length&&(s.post_id=a,n&&(s.lock=n),i["wp-refresh-post-lock"]=s)}).on("heartbeat-tick.refresh-lock",function(e,i){var n,a,s;i["wp-refresh-post-lock"]&&((n=i["wp-refresh-post-lock"]).lock_error?(a=t("#post-lock-dialog")).length&&!a.is(":visible")&&(wp.autosave&&(t(document).one("heartbeat-tick",function(){wp.autosave.server.suspend(),a.removeClass("saving").addClass("saved"),t(window).off("beforeunload.edit-post")}),a.addClass("saving"),wp.autosave.server.triggerSave()),n.lock_error.avatar_src&&(s=t('<img class="avatar avatar-64 photo" width="64" height="64" alt="" />').attr("src",n.lock_error.avatar_src.replace(/&amp;/g,"&")),a.find("div.post-locked-avatar").empty().append(s)),a.show().find(".currently-editing").text(n.lock_error.text),a.find(".wp-tab-first").focus()):n.new_lock&&t("#active_post_lock").val(n.new_lock))}).on("before-autosave.update-post-slug",function(){e=document.activeElement&&"title"===document.activeElement.id}).on("after-autosave.update-post-slug",function(){t("#edit-slug-box > *").length||e||t.post(ajaxurl,{action:"sample-permalink",post_id:t("#post_ID").val(),new_title:t("#title").val(),samplepermalinknonce:t("#samplepermalinknonce").val()},function(e){"-1"!=e&&t("#edit-slug-box").html(e)})})}(jQuery),function(t){var e,i;function n(){e=!1,window.clearTimeout(i),i=window.setTimeout(function(){e=!0},3e5)}t(document).on("heartbeat-send.wp-refresh-nonces",function(i,n){var a,s=t("#wp-auth-check-wrap");(e||s.length&&!s.hasClass("hidden"))&&(a=t("#post_ID").val())&&t("#_wpnonce").val()&&(n["wp-refresh-post-nonces"]={post_id:a})}).on("heartbeat-tick.wp-refresh-nonces",function(e,i){var a=i["wp-refresh-post-nonces"];a&&(n(),a.replace&&t.each(a.replace,function(e,i){t("#"+e).val(i)}),a.heartbeatNonce&&(window.heartbeatSettings.nonce=a.heartbeatNonce))}).ready(function(){n()})}(jQuery),jQuery(document).ready(function(t){var e,i,n,a,s,o,l="",c=t("#content"),r=t(document),p=t("#post_ID").val()||0,d=t("#submitpost"),u=!0,h=t("#post-visibility-select"),f=t("#timestampdiv"),v=t("#post-status-select"),m=!!window.navigator.platform&&-1!==window.navigator.platform.indexOf("Mac");(postboxes.add_postbox_toggles(pagenow),window.name="",t("#post-lock-dialog .notification-dialog").on("keydown",function(e){if(9==e.which){var i=t(e.target);i.hasClass("wp-tab-first")&&e.shiftKey?(t(this).find(".wp-tab-last").focus(),e.preventDefault()):i.hasClass("wp-tab-last")&&!e.shiftKey&&(t(this).find(".wp-tab-first").focus(),e.preventDefault())}}).filter(":visible").find(".wp-tab-first").focus(),wp.heartbeat&&t("#post-lock-dialog").length&&wp.heartbeat.interval(15),n=d.find(":submit, a.submitdelete, #post-preview").on("click.edit-post",function(e){var i=t(this);i.hasClass("disabled")?e.preventDefault():i.hasClass("submitdelete")||i.is("#post-preview")||t("form#post").off("submit.edit-post").on("submit.edit-post",function(e){if(!e.isDefaultPrevented()){if(wp.autosave&&wp.autosave.server.suspend(),"undefined"!=typeof commentReply){if(!commentReply.discardCommentChanges())return!1;commentReply.close()}u=!1,t(window).off("beforeunload.edit-post"),n.addClass("disabled"),"publish"===i.attr("id")?d.find("#major-publishing-actions .spinner").addClass("is-active"):d.find("#minor-publishing .spinner").addClass("is-active")}})}),t("#post-preview").on("click.post-preview",function(e){var i=t(this),n=t("form#post"),a=t("input#wp-preview"),s=i.attr("target")||"wp-preview",o=navigator.userAgent.toLowerCase();e.preventDefault(),i.hasClass("disabled")||(wp.autosave&&wp.autosave.server.tempBlockSave(),a.val("dopreview"),n.attr("target",s).submit().attr("target",""),-1!==o.indexOf("safari")&&-1===o.indexOf("chrome")&&n.attr("action",function(t,e){return e+"?t="+(new Date).getTime()}),a.val(""))}),t("#title").on("keydown.editor-focus",function(t){var e;if(9===t.keyCode&&!t.ctrlKey&&!t.altKey&&!t.shiftKey){if((e="undefined"!=typeof tinymce&&tinymce.get("content"))&&!e.isHidden())e.focus();else{if(!c.length)return;c.focus()}t.preventDefault()}}),t("#auto_draft").val()&&t("#title").blur(function(){var e;this.value&&!t("#edit-slug-box > *").length&&(t("form#post").one("submit",function(){e=!0}),window.setTimeout(function(){!e&&wp.autosave&&wp.autosave.server.triggerSave()},200))}),r.on("autosave-disable-buttons.edit-post",function(){n.addClass("disabled")}).on("autosave-enable-buttons.edit-post",function(){wp.heartbeat&&wp.heartbeat.hasConnectionError()||n.removeClass("disabled")}).on("before-autosave.edit-post",function(){t(".autosave-message").text(postL10n.savingText)}).on("after-autosave.edit-post",function(e,i){t(".autosave-message").text(i.message),t(document.body).hasClass("post-new-php")&&t(".submitbox .submitdelete").show()}),t(window).on("beforeunload.edit-post",function(){var t="undefined"!=typeof tinymce&&tinymce.get("content");if(t&&!t.isHidden()&&t.isDirty()||wp.autosave&&wp.autosave.server.postChanged())return postL10n.saveAlert}).on("unload.edit-post",function(e){if(u&&(!e.target||"#document"==e.target.nodeName)){var i=t("#post_ID").val(),n=t("#active_post_lock").val();if(i&&n){var a={action:"wp-remove-post-lock",_wpnonce:t("#_wpnonce").val(),post_ID:i,active_post_lock:n};if(window.FormData&&window.navigator.sendBeacon){var s=new window.FormData;if(t.each(a,function(t,e){s.append(t,e)}),window.navigator.sendBeacon(ajaxurl,s))return}t.post({async:!1,data:a,url:ajaxurl})}}}),t("#tagsdiv-post_tag").length?window.tagBox&&window.tagBox.init():t(".meta-box-sortables").children("div.postbox").each(function(){if(0===this.id.indexOf("tagsdiv-"))return window.tagBox&&window.tagBox.init(),!1}),t(".categorydiv").each(function(){var e,i,n,a,s;(n=t(this).attr("id").split("-")).shift(),a=n.join("-"),s=a+"_tab","category"==a&&(s="cats"),t("a","#"+a+"-tabs").click(function(e){e.preventDefault();var i=t(this).attr("href");t(this).parent().addClass("tabs").siblings("li").removeClass("tabs"),t("#"+a+"-tabs").siblings(".tabs-panel").hide(),t(i).show(),"#"+a+"-all"==i?deleteUserSetting(s):setUserSetting(s,"pop")}),getUserSetting(s)&&t('a[href="#'+a+'-pop"]',"#"+a+"-tabs").click(),t("#new"+a).one("focus",function(){t(this).val("").removeClass("form-input-tip")}),t("#new"+a).keypress(function(e){13===e.keyCode&&(e.preventDefault(),t("#"+a+"-add-submit").click())}),t("#"+a+"-add-submit").click(function(){t("#new"+a).focus()}),e=function(e){return!!t("#new"+a).val()&&(e.data+="&"+t(":checked","#"+a+"checklist").serialize(),t("#"+a+"-add-submit").prop("disabled",!0),e)},i=function(e,i){var n,s=t("#new"+a+"_parent");t("#"+a+"-add-submit").prop("disabled",!1),"undefined"!=i.parsed.responses[0]&&(n=i.parsed.responses[0].supplemental.newcat_parent)&&(s.before(n),s.remove())},t("#"+a+"checklist").wpList({alt:"",response:a+"-ajax-response",addBefore:e,addAfter:i}),t("#"+a+"-add-toggle").click(function(e){e.preventDefault(),t("#"+a+"-adder").toggleClass("wp-hidden-children"),t('a[href="#'+a+'-all"]',"#"+a+"-tabs").click(),t("#new"+a).focus()}),t("#"+a+"checklist, #"+a+"checklist-pop").on("click",'li.popular-category > label input[type="checkbox"]',function(){var e=t(this),i=e.is(":checked"),n=e.val();n&&e.parents("#taxonomy-"+a).length&&t("#in-"+a+"-"+n+", #in-popular-"+a+"-"+n).prop("checked",i)})}),t("#postcustom").length&&t("#the-list").wpList({addBefore:function(e){return e.data+="&post_id="+t("#post_ID").val(),e},addAfter:function(){t("table#list-table").show()}}),t("#submitdiv").length&&(e=t("#timestamp").html(),i=t("#post-visibility-display").html(),a=function(){"public"!=h.find("input:radio:checked").val()?(t("#sticky").prop("checked",!1),t("#sticky-span").hide()):t("#sticky-span").show(),"password"!=h.find("input:radio:checked").val()?t("#password-span").hide():t("#password-span").show()},s=function(){if(!f.length)return!0;var i,n,a,s,o=t("#post_status"),l=t('option[value="publish"]',o),c=t("#aa").val(),r=t("#mm").val(),p=t("#jj").val(),d=t("#hh").val(),u=t("#mn").val();return i=new Date(c,r-1,p,d,u),n=new Date(t("#hidden_aa").val(),t("#hidden_mm").val()-1,t("#hidden_jj").val(),t("#hidden_hh").val(),t("#hidden_mn").val()),a=new Date(t("#cur_aa").val(),t("#cur_mm").val()-1,t("#cur_jj").val(),t("#cur_hh").val(),t("#cur_mn").val()),i.getFullYear()!=c||1+i.getMonth()!=r||i.getDate()!=p||i.getMinutes()!=u?(f.find(".timestamp-wrap").addClass("form-invalid"),!1):(f.find(".timestamp-wrap").removeClass("form-invalid"),i>a&&"future"!=t("#original_post_status").val()?(s=postL10n.publishOnFuture,t("#publish").val(postL10n.schedule)):i<=a&&"publish"!=t("#original_post_status").val()?(s=postL10n.publishOn,t("#publish").val(postL10n.publish)):(s=postL10n.publishOnPast,t("#publish").val(postL10n.update)),n.toUTCString()==i.toUTCString()?t("#timestamp").html(e):t("#timestamp").html("\n"+s+" <b>"+postL10n.dateFormat.replace("%1$s",t('option[value="'+r+'"]',"#mm").attr("data-text")).replace("%2$s",parseInt(p,10)).replace("%3$s",c).replace("%4$s",("00"+d).slice(-2)).replace("%5$s",("00"+u).slice(-2))+"</b> "),"private"==h.find("input:radio:checked").val()?(t("#publish").val(postL10n.update),0===l.length?o.append('<option value="publish">'+postL10n.privatelyPublished+"</option>"):l.html(postL10n.privatelyPublished),t('option[value="publish"]',o).prop("selected",!0),t("#misc-publishing-actions .edit-post-status").hide()):("future"==t("#original_post_status").val()||"draft"==t("#original_post_status").val()?l.length&&(l.remove(),o.val(t("#hidden_post_status").val())):l.html(postL10n.published),o.is(":hidden")&&t("#misc-publishing-actions .edit-post-status").show()),t("#post-status-display").text(pm_sanitize.stripTagsAndEncodeText(t("option:selected",o).text())),"private"==t("option:selected",o).val()||"publish"==t("option:selected",o).val()?t("#save-post").hide():(t("#save-post").show(),"pending"==t("option:selected",o).val()?t("#save-post").show().val(postL10n.savePending):t("#save-post").show().val(postL10n.saveDraft)),!0)},t("#visibility .edit-visibility").click(function(e){e.preventDefault(),h.is(":hidden")&&(a(),h.slideDown("fast",function(){h.find('input[type="radio"]').first().focus()}),t(this).hide())}),h.find(".cancel-post-visibility").click(function(e){h.slideUp("fast"),t("#visibility-radio-"+t("#hidden-post-visibility").val()).prop("checked",!0),t("#post_password").val(t("#hidden-post-password").val()),t("#sticky").prop("checked",t("#hidden-post-sticky").prop("checked")),t("#post-visibility-display").html(i),t("#visibility .edit-visibility").show().focus(),s(),e.preventDefault()}),h.find(".save-post-visibility").click(function(e){h.slideUp("fast"),t("#visibility .edit-visibility").show().focus(),s(),"public"!=h.find("input:radio:checked").val()&&t("#sticky").prop("checked",!1),l=t("#sticky").prop("checked")?"Sticky":"",t("#post-visibility-display").html(postL10n[h.find("input:radio:checked").val()+l]),e.preventDefault()}),h.find("input:radio").change(function(){a()}),f.siblings("a.edit-timestamp").click(function(e){f.is(":hidden")&&(f.slideDown("fast",function(){t("input, select",f.find(".timestamp-wrap")).first().focus()}),t(this).hide()),e.preventDefault()}),f.find(".cancel-timestamp").click(function(e){f.slideUp("fast").siblings("a.edit-timestamp").show().focus(),t("#mm").val(t("#hidden_mm").val()),t("#jj").val(t("#hidden_jj").val()),t("#aa").val(t("#hidden_aa").val()),t("#hh").val(t("#hidden_hh").val()),t("#mn").val(t("#hidden_mn").val()),s(),e.preventDefault()}),f.find(".save-timestamp").click(function(t){s()&&(f.slideUp("fast"),f.siblings("a.edit-timestamp").show().focus()),t.preventDefault()}),t("#post").on("submit",function(e){s()||(e.preventDefault(),f.show(),wp.autosave&&wp.autosave.enableButtons(),t("#publishing-action .spinner").removeClass("is-active"))}),v.siblings("a.edit-post-status").click(function(e){v.is(":hidden")&&(v.slideDown("fast",function(){v.find("select").focus()}),t(this).hide()),e.preventDefault()}),v.find(".save-post-status").click(function(t){v.slideUp("fast").siblings("a.edit-post-status").show().focus(),s(),t.preventDefault()}),v.find(".cancel-post-status").click(function(e){v.slideUp("fast").siblings("a.edit-post-status").show().focus(),t("#post_status").val(t("#hidden_post_status").val()),s(),e.preventDefault()})),t("#titlediv").on("click",".edit-slug",function(){!function(){var e,i,n,a,s=0,o=t("#post_name"),l=o.val(),c=t("#sample-permalink"),r=c.html(),d=t("#sample-permalink a").html(),u=t("#edit-slug-buttons"),h=u.html(),f=t("#editable-post-name-full");for(f.find("img").replaceWith(function(){return this.alt}),f=f.html(),c.html(d),n=t("#editable-post-name"),a=n.html(),u.html('<button type="button" class="save button button-small">'+postL10n.ok+'</button> <button type="button" class="cancel button-link">'+postL10n.cancel+"</button>"),u.children(".save").click(function(){var e=n.children("input").val();e!=t("#editable-post-name-full").text()?t.post(ajaxurl,{action:"sample-permalink",post_id:p,new_slug:e,new_title:t("#title").val(),samplepermalinknonce:t("#samplepermalinknonce").val()},function(i){var n=t("#edit-slug-box");n.html(i),n.hasClass("hidden")&&n.fadeIn("fast",function(){n.removeClass("hidden")}),u.html(h),c.html(r),o.val(e),t(".edit-slug").focus(),wp.a11y.speak(postL10n.permalinkSaved)}):u.children(".cancel").click()}),u.children(".cancel").click(function(){t("#view-post-btn").show(),n.html(a),u.html(h),c.html(r),o.val(l),t(".edit-slug").focus()}),e=0;e<f.length;++e)"%"==f.charAt(e)&&s++;i=s>f.length/4?"":f,n.html('<input type="text" id="new-post-slug" value="'+i+'" autocomplete="off" />').children("input").keydown(function(t){var e=t.which;13===e&&(t.preventDefault(),u.children(".save").click()),27===e&&u.children(".cancel").click()}).keyup(function(){o.val(this.value)}).focus()}()}),(wptitlehint=function(e){var i=t("#"+(e=e||"title")),n=t("#"+e+"-prompt-text");""===i.val()&&n.removeClass("screen-reader-text"),n.click(function(){t(this).addClass("screen-reader-text"),i.focus()}),i.blur(function(){""===this.value&&n.removeClass("screen-reader-text")}).focus(function(){n.addClass("screen-reader-text")}).keydown(function(e){n.addClass("screen-reader-text"),t(this).unbind(e)})})(),function(){var e,i,n,a=t("#post-status-info"),s=t("#postdivrich");function o(t){s.hasClass("wp-editor-expand")||(n?e.theme.resizeTo(null,i+t.pageY):c.height(Math.max(50,i+t.pageY)),t.preventDefault())}function l(){var i,a;s.hasClass("wp-editor-expand")||(n?(e.focus(),((a=parseInt(t("#wp-content-editor-container .mce-toolbar-grp").height(),10))<10||a>200)&&(a=30),i=parseInt(t("#content_ifr").css("height"),10)+a-28):(c.focus(),i=parseInt(c.css("height"),10)),r.off(".wp-editor-resize"),i&&i>50&&i<5e3&&setUserSetting("ed_size",i))}!c.length||"ontouchstart"in window?t("#content-resize-handle").hide():a.on("mousedown.wp-editor-resize",function(a){"undefined"!=typeof tinymce&&(e=tinymce.get("content")),e&&!e.isHidden()?(n=!0,i=t("#content_ifr").height()-a.pageY):(n=!1,i=c.height()-a.pageY,c.blur()),r.on("mousemove.wp-editor-resize",o).on("mouseup.wp-editor-resize mouseleave.wp-editor-resize",l),a.preventDefault()}).on("mouseup.wp-editor-resize",l)}(),"undefined"!=typeof tinymce&&(t("#post-formats-select input.post-format").on("change.set-editor-class",function(){var e,i,n=this.id;n&&t(this).prop("checked")&&(e=tinymce.get("content"))&&((i=e.getBody()).className=i.className.replace(/\bpost-format-[^ ]+/,""),e.dom.addClass(i,"post-format-0"==n?"post-format-standard":n),t(document).trigger("editor-classchange"))}),t("#page_template").on("change.set-editor-class",function(){var e,i,n=t(this).val()||"";(n=n.substr(n.lastIndexOf("/")+1,n.length).replace(/\.php$/,"").replace(/\./g,"-"))&&(e=tinymce.get("content"))&&((i=e.getBody()).className=i.className.replace(/\bpage-template-[^ ]+/,""),e.dom.addClass(i,"page-template-"+n),t(document).trigger("editor-classchange"))})),c.on("keydown.wp-autosave",function(t){if(83===t.which){if(t.shiftKey||t.altKey||m&&(!t.metaKey||t.ctrlKey)||!m&&!t.ctrlKey)return;wp.autosave&&wp.autosave.server.triggerSave(),t.preventDefault()}}),"auto-draft"===t("#original_post_status").val()&&window.history.replaceState)&&t("#publish").on("click",function(){o=window.location.href,o+=-1!==o.indexOf("?")?"&":"?",o+="wp-post-new-reload=true",window.history.replaceState(null,null,o)})}),function(t,e){t(function(){var i,n=t("#content"),a=t("#wp-word-count").find(".word-count"),s=0;function o(){var t,o;t=!i||i.isHidden()?n.val():i.getContent({format:"raw"}),(o=e.count(t))!==s&&a.text(o),s=o}t(document).on("tinymce-editor-init",function(t,e){"content"===e.id&&(i=e,e.on("nodechange keyup",_.debounce(o,1e3)))}),n.on("input keyup",_.debounce(o,1e3)),o()})}(jQuery,new wp.utils.WordCounter);