!function(e,t,n){"use strict";function i(i){return i=i||"",i=t.sanitize.stripTagsAndEncodeText(i),(i=n.trim(i))||e.Menus.data.l10n.unnamed}wpNavMenu.originalInit=wpNavMenu.init,wpNavMenu.options.menuItemDepthPerLevel=20,wpNavMenu.options.sortableItems="> .customize-control-nav_menu_item",wpNavMenu.options.targetTolerance=10,wpNavMenu.init=function(){this.jQueryExtensions()},e.<PERSON>us=e.<PERSON>us||{},e.Menus.data={itemTypes:[],l10n:{},settingTransport:"refresh",phpIntMax:0,defaultSettingValues:{nav_menu:{},nav_menu_item:{}},locationSlugMappedToName:{}},"undefined"!=typeof _wpCustomizeNavMenusSettings&&n.extend(e.Menus.data,_wpCustomizeNavMenusSettings),e.Menus.generatePlaceholderAutoIncrementId=function(){return-Math.ceil(e.Menus.data.phpIntMax*Math.random())},e.Menus.AvailableItemModel=Backbone.Model.extend(n.extend({id:null},e.Menus.data.defaultSettingValues.nav_menu_item)),e.Menus.AvailableItemCollection=Backbone.Collection.extend({model:e.Menus.AvailableItemModel,sort_key:"order",comparator:function(e){return-e.get(this.sort_key)},sortByField:function(e){this.sort_key=e,this.sort()}}),e.Menus.availableMenuItems=new e.Menus.AvailableItemCollection(e.Menus.data.availableMenuItems),e.Menus.insertAutoDraftPost=function(i){var a,o=n.Deferred();return(a=t.ajax.post("customize-nav-menus-insert-auto-draft",{"customize-menus-nonce":e.settings.nonce["customize-menus"],wp_customize:"on",customize_changeset_uuid:e.settings.changeset.uuid,params:i})).done(function(t){t.post_id&&(e("nav_menus_created_posts").set(e("nav_menus_created_posts").get().concat([t.post_id])),"page"===i.post_type&&(e.section.has("static_front_page")&&e.section("static_front_page").activate(),e.control.each(function(e){"dropdown-pages"===e.params.type&&e.container.find('select[name^="_customize-dropdown-pages-"]').append(new Option(i.post_title,t.post_id))})),o.resolve(t))}),a.fail(function(e){var t=e||"";void 0!==e.message&&(t=e.message),console.error(t),o.rejectWith(t)}),o.promise()},e.Menus.AvailableMenuItemsPanelView=t.Backbone.View.extend({el:"#available-menu-items",events:{"input #menu-items-search":"debounceSearch","keyup #menu-items-search":"debounceSearch","focus .menu-item-tpl":"focus","click .menu-item-tpl":"_submit","click #custom-menu-item-submit":"_submitLink","keypress #custom-menu-item-name":"_submitLink","click .new-content-item .add-content":"_submitNew","keypress .create-item-input":"_submitNew",keydown:"keyboardAccessible"},selected:null,currentMenuControl:null,debounceSearch:null,$search:null,$clearResults:null,searchTerm:"",rendered:!1,pages:{},sectionContent:"",loading:!1,addingNew:!1,initialize:function(){var t=this;e.panel.has("nav_menus")&&(this.$search=n("#menu-items-search"),this.$clearResults=this.$el.find(".clear-results"),this.sectionContent=this.$el.find(".available-menu-items-list"),this.debounceSearch=_.debounce(t.search,500),_.bindAll(this,"close"),n("#customize-controls, .customize-section-back").on("click keydown",function(e){var i=n(e.target).is(".item-delete, .item-delete *"),a=n(e.target).is(".add-new-menu-item, .add-new-menu-item *");!n("body").hasClass("adding-menu-items")||i||a||t.close()}),this.$clearResults.on("click",function(){t.$search.val("").focus().trigger("keyup")}),this.$el.on("input","#custom-menu-item-name.invalid, #custom-menu-item-url.invalid",function(){n(this).removeClass("invalid")}),e.panel("nav_menus").container.bind("expanded",function(){t.rendered||(t.initList(),t.rendered=!0)}),this.sectionContent.scroll(function(){var e=t.$el.find(".accordion-section.open .available-menu-items-list").prop("scrollHeight"),i=t.$el.find(".accordion-section.open").height();if(!t.loading&&n(this).scrollTop()>.75*e-i){var a=n(this).data("type"),o=n(this).data("object");"search"===a?t.searchTerm&&t.doSearch(t.pages.search):t.loadItems([{type:a,object:o}])}}),e.previewer.bind("url",this.close),t.delegateEvents())},search:function(e){var t=n("#available-menu-items-search"),i=n("#available-menu-items .accordion-section").not(t);e&&this.searchTerm!==e.target.value&&(""===e.target.value||t.hasClass("open")?""===e.target.value&&(t.removeClass("open"),i.show(),this.$clearResults.removeClass("is-visible")):(i.fadeOut(100),t.find(".accordion-section-content").slideDown("fast"),t.addClass("open"),this.$clearResults.addClass("is-visible")),this.searchTerm=e.target.value,this.pages.search=1,this.doSearch(1))},doSearch:function(i){var a,o=this,s=n("#available-menu-items-search"),r=s.find(".accordion-section-content"),u=t.template("available-menu-item");if(o.currentRequest&&o.currentRequest.abort(),!(i<0)){if(i>1)s.addClass("loading-more"),r.attr("aria-busy","true"),t.a11y.speak(e.Menus.data.l10n.itemsLoadingMore);else if(""===o.searchTerm)return r.html(""),void t.a11y.speak("");s.addClass("loading"),o.loading=!0,a=e.previewer.query({excludeCustomizedSaved:!0}),_.extend(a,{"customize-menus-nonce":e.settings.nonce["customize-menus"],wp_customize:"on",search:o.searchTerm,page:i}),o.currentRequest=t.ajax.post("search-available-menu-items-customizer",a),o.currentRequest.done(function(n){var a;1===i&&r.empty(),s.removeClass("loading loading-more"),r.attr("aria-busy","false"),s.addClass("open"),o.loading=!1,a=new e.Menus.AvailableItemCollection(n.items),o.collection.add(a.models),a.each(function(e){r.append(u(e.attributes))}),20>a.length?o.pages.search=-1:o.pages.search=o.pages.search+1,a&&i>1?t.a11y.speak(e.Menus.data.l10n.itemsFoundMore.replace("%d",a.length)):a&&1===i&&t.a11y.speak(e.Menus.data.l10n.itemsFound.replace("%d",a.length))}),o.currentRequest.fail(function(e){e.message&&(r.empty().append(n('<li class="nothing-found"></li>').text(e.message)),t.a11y.speak(e.message)),o.pages.search=-1}),o.currentRequest.always(function(){s.removeClass("loading loading-more"),r.attr("aria-busy","false"),o.loading=!1,o.currentRequest=null})}},initList:function(){var t=this;_.each(e.Menus.data.itemTypes,function(e){t.pages[e.type+":"+e.object]=0}),t.loadItems(e.Menus.data.itemTypes)},loadItems:function(i,a){var o,s,r,u,d=this,c=[],l={};u=t.template("available-menu-item"),o=_.isString(i)&&_.isString(a)?[{type:i,object:a}]:i,_.each(o,function(e){var t,i=e.type+":"+e.object;-1!==d.pages[i]&&((t=n("#available-menu-items-"+e.type+"-"+e.object)).find(".accordion-section-title").addClass("loading"),l[i]=t,c.push({object:e.object,type:e.type,page:d.pages[i]}))}),0!==c.length&&(d.loading=!0,s=e.previewer.query({excludeCustomizedSaved:!0}),_.extend(s,{"customize-menus-nonce":e.settings.nonce["customize-menus"],wp_customize:"on",item_types:c}),(r=t.ajax.post("load-available-menu-items-customizer",s)).done(function(t){var n;_.each(t.items,function(t,i){if(0===t.length)return 0===d.pages[i]&&l[i].find(".accordion-section-title").addClass("cannot-expand").removeClass("loading").find(".accordion-section-title > button").prop("tabIndex",-1),void(d.pages[i]=-1);"post_type:page"!==i||l[i].hasClass("open")||l[i].find(".accordion-section-title > button").click(),t=new e.Menus.AvailableItemCollection(t),d.collection.add(t.models),n=l[i].find(".available-menu-items-list"),t.each(function(e){n.append(u(e.attributes))}),d.pages[i]+=1})}),r.fail(function(e){"undefined"!=typeof console&&console.error&&console.error(e)}),r.always(function(){_.each(l,function(e){e.find(".accordion-section-title").removeClass("loading")}),d.loading=!1}))},itemSectionHeight:function(){var e,t,n,i;n=window.innerHeight,e=this.$el.find(".accordion-section:not( #available-menu-items-search ) .accordion-section-content"),t=this.$el.find('.accordion-section:not( #available-menu-items-search ) .available-menu-items-list:not(":only-child")'),120<(i=n-(46*(1+e.length)+14))&&290>i&&(e.css("max-height",i),t.css("max-height",i-60))},select:function(e){this.selected=n(e),this.selected.siblings(".menu-item-tpl").removeClass("selected"),this.selected.addClass("selected")},focus:function(e){this.select(n(e.currentTarget))},_submit:function(e){"keypress"===e.type&&13!==e.which&&32!==e.which||this.submit(n(e.currentTarget))},submit:function(e){var t,i;e||(e=this.selected),e&&this.currentMenuControl&&(this.select(e),t=n(this.selected).data("menu-item-id"),(i=this.collection.findWhere({id:t}))&&(this.currentMenuControl.addItemToMenu(i.attributes),n(e).find(".menu-item-handle").addClass("item-added")))},_submitLink:function(e){"keypress"===e.type&&13!==e.which||this.submitLink()},submitLink:function(){var t,i,a=n("#custom-menu-item-name"),o=n("#custom-menu-item-url");this.currentMenuControl&&(i=/^((\w+:)?\/\/\w.*|\w+:(?!\/\/$)|\/|\?|#)/,""!==a.val()?i.test(o.val())?(t={title:a.val(),url:o.val(),type:"custom",type_label:e.Menus.data.l10n.custom_label,object:"custom"},this.currentMenuControl.addItemToMenu(t),o.val("http://"),a.val("")):o.addClass("invalid"):a.addClass("invalid"))},_submitNew:function(e){var t;"keypress"===e.type&&13!==e.which||this.addingNew||(t=n(e.target).closest(".accordion-section"),this.submitNew(t))},submitNew:function(i){var a=this,o=i.find(".create-item-input"),s=o.val(),r=i.find(".available-menu-items-list"),u=r.data("type"),d=r.data("object"),c=r.data("type_label");if(this.currentMenuControl&&"post_type"===u){if(""===n.trim(o.val()))return o.addClass("invalid"),void o.focus();o.removeClass("invalid"),i.find(".accordion-section-title").addClass("loading"),a.addingNew=!0,o.attr("disabled","disabled"),e.Menus.insertAutoDraftPost({post_title:s,post_type:d}).done(function(s){var r,l,m;r=new e.Menus.AvailableItemModel({id:"post-"+s.post_id,title:o.val(),type:u,type_label:c,object:d,object_id:s.post_id,url:s.url}),a.currentMenuControl.addItemToMenu(r.attributes),e.Menus.availableMenuItemsPanel.collection.add(r),l=i.find(".available-menu-items-list"),(m=n(t.template("available-menu-item")(r.attributes))).find(".menu-item-handle:first").addClass("item-added"),l.prepend(m),l.scrollTop(),o.val("").removeAttr("disabled"),a.addingNew=!1,i.find(".accordion-section-title").removeClass("loading")})}},open:function(t){var i,a=this;this.currentMenuControl=t,this.itemSectionHeight(),e.section.has("publish_settings")&&e.section("publish_settings").collapse(),n("body").addClass("adding-menu-items"),i=function(){a.close(),n(this).off("click",i)},n("#customize-preview").on("click",i),_(this.currentMenuControl.getMenuItemControls()).each(function(e){e.collapseForm()}),this.$el.find(".selected").removeClass("selected"),this.$search.focus()},close:function(e){(e=e||{}).returnFocus&&this.currentMenuControl&&this.currentMenuControl.container.find(".add-new-menu-item").focus(),this.currentMenuControl=null,this.selected=null,n("body").removeClass("adding-menu-items"),n("#available-menu-items .menu-item-handle.item-added").removeClass("item-added"),this.$search.val("").trigger("keyup")},keyboardAccessible:function(e){var t=13===e.which,i=27===e.which,a=9===e.which&&e.shiftKey,o=n(e.target).is(this.$search);t&&!this.$search.val()||(o&&a?(this.currentMenuControl.container.find(".add-new-menu-item").focus(),e.preventDefault()):i&&this.close({returnFocus:!0}))}}),e.Menus.MenusPanel=e.Panel.extend({attachEvents:function(){e.Panel.prototype.attachEvents.call(this);var t=this.container.find(".panel-meta"),i=t.find(".customize-help-toggle"),a=t.find(".customize-panel-description"),o=n("#screen-options-wrap"),s=t.find(".customize-screen-options-toggle");s.on("click keydown",function(n){if(!e.utils.isKeydownButNotEnterEvent(n))return n.preventDefault(),a.not(":hidden")&&(a.slideUp("fast"),i.attr("aria-expanded","false")),"true"===s.attr("aria-expanded")?(s.attr("aria-expanded","false"),t.removeClass("open"),t.removeClass("active-menu-screen-options"),o.slideUp("fast")):(s.attr("aria-expanded","true"),t.addClass("open"),t.addClass("active-menu-screen-options"),o.slideDown("fast")),!1}),i.on("click keydown",function(n){e.utils.isKeydownButNotEnterEvent(n)||(n.preventDefault(),"true"===s.attr("aria-expanded")&&(s.attr("aria-expanded","false"),i.attr("aria-expanded","true"),t.addClass("open"),t.removeClass("active-menu-screen-options"),o.slideUp("fast"),a.slideDown("fast")))})},ready:function(){var n=this;n.container.find(".hide-column-tog").click(function(){n.saveManageColumnsState()}),e.section("menu_locations",function(n){n.headContainer.prepend(t.template("nav-menu-locations-header")(e.Menus.data))})},saveManageColumnsState:_.debounce(function(){var e=this;e._updateHiddenColumnsRequest&&e._updateHiddenColumnsRequest.abort(),e._updateHiddenColumnsRequest=t.ajax.post("hidden-columns",{hidden:e.hidden(),screenoptionnonce:n("#screenoptionnonce").val(),page:"nav-menus"}),e._updateHiddenColumnsRequest.always(function(){e._updateHiddenColumnsRequest=null})},2e3),checked:function(){},unchecked:function(){},hidden:function(){return n(".hide-column-tog").not(":checked").map(function(){var e=this.id;return e.substring(0,e.length-5)}).get().join(",")}}),e.Menus.MenuSection=e.Section.extend({initialize:function(t,i){e.Section.prototype.initialize.call(this,t,i),this.deferred.initSortables=n.Deferred()},ready:function(){var t,i,a=this;if(void 0===a.params.menu_id)throw new Error("params.menu_id was not defined");a.active.validate=function(){return!!e.has(a.id)&&!!e(a.id).get()},a.populateControls(),a.navMenuLocationSettings={},a.assignedLocations=new e.Value([]),e.each(function(e,t){var n=t.match(/^nav_menu_locations\[(.+?)]/);n&&(a.navMenuLocationSettings[n[1]]=e,e.bind(function(){a.refreshAssignedLocations()}))}),a.assignedLocations.bind(function(e){a.updateAssignedLocationsInSectionTitle(e)}),a.refreshAssignedLocations(),e.bind("pane-contents-reflowed",function(){a.contentContainer.parent().length&&(a.container.find(".menu-item .menu-item-reorder-nav button").attr({tabindex:"0","aria-hidden":"false"}),a.container.find(".menu-item.move-up-disabled .menus-move-up").attr({tabindex:"-1","aria-hidden":"true"}),a.container.find(".menu-item.move-down-disabled .menus-move-down").attr({tabindex:"-1","aria-hidden":"true"}),a.container.find(".menu-item.move-left-disabled .menus-move-left").attr({tabindex:"-1","aria-hidden":"true"}),a.container.find(".menu-item.move-right-disabled .menus-move-right").attr({tabindex:"-1","aria-hidden":"true"}))}),i=function(){var e="field-"+n(this).val()+"-active";a.contentContainer.toggleClass(e,n(this).prop("checked"))},(t=e.panel("nav_menus").contentContainer.find(".metabox-prefs:first").find(".hide-column-tog")).each(i),t.on("click",i)},populateControls:function(){var t,n,i,a,o,s,r,u,d,c=this;t=c.id+"[name]",(s=e.control(t))||(s=new e.controlConstructor.nav_menu_name(t,{type:"nav_menu_name",label:e.Menus.data.l10n.menuNameLabel,section:c.id,priority:0,settings:{default:c.id}}),e.control.add(s),s.active.set(!0)),(o=e.control(c.id))||(o=new e.controlConstructor.nav_menu(c.id,{type:"nav_menu",section:c.id,priority:998,settings:{default:c.id},menu_id:c.params.menu_id}),e.control.add(o),o.active.set(!0)),n=c.id+"[locations]",(r=e.control(n))||(r=new e.controlConstructor.nav_menu_locations(n,{section:c.id,priority:999,settings:{default:c.id},menu_id:c.params.menu_id}),e.control.add(r.id,r),o.active.set(!0)),i=c.id+"[auto_add]",(u=e.control(i))||(u=new e.controlConstructor.nav_menu_auto_add(i,{type:"nav_menu_auto_add",label:"",section:c.id,priority:1e3,settings:{default:c.id}}),e.control.add(u),u.active.set(!0)),a=c.id+"[delete]",(d=e.control(a))||(d=new e.Control(a,{section:c.id,priority:1001,templateId:"nav-menu-delete-button"}),e.control.add(d.id,d),d.active.set(!0),d.deferred.embedded.done(function(){d.container.find("button").on("click",function(){var t=c.params.menu_id;e.Menus.getMenuControl(t).setting.set(!1)})}))},refreshAssignedLocations:function(){var e=this.params.menu_id,t=[];_.each(this.navMenuLocationSettings,function(n,i){n()===e&&t.push(i)}),this.assignedLocations.set(t)},updateAssignedLocationsInSectionTitle:function(t){var i;(i=this.container.find(".accordion-section-title:first")).find(".menu-in-location").remove(),_.each(t,function(t){var a,o;a=n('<span class="menu-in-location"></span>'),o=e.Menus.data.locationSlugMappedToName[t],a.text(e.Menus.data.l10n.menuLocation.replace("%s",o)),i.append(a)}),this.container.toggleClass("assigned-to-menu-location",0!==t.length)},onChangeExpanded:function(t,i){var a,o=this;t&&(wpNavMenu.menuList=o.contentContainer,wpNavMenu.targetList=wpNavMenu.menuList,n("#menu-to-edit").removeAttr("id"),wpNavMenu.menuList.attr("id","menu-to-edit").addClass("menu"),_.each(e.section(o.id).controls(),function(e){"nav_menu_item"===e.params.type&&e.actuallyEmbed()}),i.completeCallback&&(a=i.completeCallback),i.completeCallback=function(){"resolved"!==o.deferred.initSortables.state()&&(wpNavMenu.initSortables(),o.deferred.initSortables.resolve(wpNavMenu.menuList),e.control("nav_menu["+String(o.params.menu_id)+"]").reflowMenuItems()),_.isFunction(a)&&a()}),e.Section.prototype.onChangeExpanded.call(o,t,i)},highlightNewItemButton:function(){e.utils.highlightButton(this.contentContainer.find(".add-new-menu-item"),{delay:2e3})}}),e.Menus.createNavMenu=function(t){var a,o;return o=e.Menus.generatePlaceholderAutoIncrementId(),a="nav_menu["+String(o)+"]",e.create(a,a,{},{type:"nav_menu",transport:e.Menus.data.settingTransport,previewer:e.previewer}).set(n.extend({},e.Menus.data.defaultSettingValues.nav_menu,{name:t||""})),e.section.add(new e.Menus.MenuSection(a,{panel:"nav_menus",title:i(t),customizeAction:e.Menus.data.l10n.customizingMenus,priority:10,menu_id:o}))},e.Menus.NewMenuSection=e.Section.extend({attachEvents:function(){var n=this,i=n.container,a=n.contentContainer,o=/^nav_menu\[/;function s(){var t;i.find(".add-new-menu-notice").prop("hidden",(t=0,e.each(function(e){o.test(e.id)&&!1!==e.get()&&(t+=1)}),t>0))}function r(e){o.test(e.id)&&(e.bind(s),s())}n.headContainer.find(".accordion-section-title").replaceWith(t.template("nav-menu-create-menu-section-title")),i.on("click",".customize-add-menu-button",function(){n.expand()}),a.on("keydown",".menu-name-field",function(e){13===e.which&&n.submit()}),a.on("click","#customize-new-menu-submit",function(e){n.submit(),e.stopPropagation(),e.preventDefault()}),e.each(r),e.bind("add",r),e.bind("removed",function(e){o.test(e.id)&&(e.unbind(s),s())}),s(),e.Section.prototype.attachEvents.apply(n,arguments)},ready:function(){this.populateControls()},populateControls:function(){var t,n,i,a,o,s;t=this.id+"[name]",(a=e.control(t))||(a=new e.controlConstructor.nav_menu_name(t,{label:e.Menus.data.l10n.menuNameLabel,description:e.Menus.data.l10n.newMenuNameDescription,section:this.id,priority:0}),e.control.add(a.id,a),a.active.set(!0)),n=this.id+"[locations]",(o=e.control(n))||(o=new e.controlConstructor.nav_menu_locations(n,{section:this.id,priority:1,menu_id:"",isCreating:!0}),e.control.add(n,o),o.active.set(!0)),i=this.id+"[submit]",(s=e.control(i))||(s=new e.Control(i,{section:this.id,priority:1,templateId:"nav-menu-submit-new-button"}),e.control.add(i,s),s.active.set(!0))},submit:function(){var i,a=this.contentContainer,o=a.find(".menu-name-field").first(),s=o.val();if(!s)return o.addClass("invalid"),void o.focus();i=e.Menus.createNavMenu(s),o.val(""),o.removeClass("invalid"),a.find(".assigned-menu-location input[type=checkbox]").each(function(){var t=n(this);t.prop("checked")&&(e("nav_menu_locations["+t.data("location-id")+"]").set(i.params.menu_id),t.prop("checked",!1))}),t.a11y.speak(e.Menus.data.l10n.menuAdded),i.focus({completeCallback:function(){i.highlightNewItemButton()}})},selectDefaultLocation:function(t){var n=e.control(this.id+"[locations]"),i={};null!==t&&(i[t]=!0),n.setSelections(i)}}),e.Menus.MenuLocationControl=e.Control.extend({initialize:function(t,n){var i=t.match(/^nav_menu_locations\[(.+?)]/);this.themeLocation=i[1],e.Control.prototype.initialize.call(this,t,n)},ready:function(){var t=this,n=/^nav_menu\[(-?\d+)]/;t.setting.validate=function(e){return""===e?0:parseInt(e,10)},t.container.find(".create-menu").on("click",function(){var t=e.section("add_menu");t.selectDefaultLocation(this.dataset.locationId),t.focus()}),t.container.find(".edit-menu").on("click",function(){var n=t.setting();e.section("nav_menu["+n+"]").focus()}),t.setting.bind("change",function(){var e=0!==t.setting();t.container.find(".create-menu").toggleClass("hidden",e),t.container.find(".edit-menu").toggleClass("hidden",!e)}),e.bind("add",function(e){var a,o,s=e.id.match(n);s&&!1!==e()&&(o=s[1],a=new Option(i(e().name),o),t.container.find("select").append(a))}),e.bind("remove",function(e){var i,a=e.id.match(n);a&&(i=parseInt(a[1],10),t.setting()===i&&t.setting.set(""),t.container.find("option[value="+i+"]").remove())}),e.bind("change",function(e){var a,o=e.id.match(n);o&&(a=parseInt(o[1],10),!1===e()?(t.setting()===a&&t.setting.set(""),t.container.find("option[value="+a+"]").remove()):t.container.find("option[value="+a+"]").text(i(e().name)))})}}),e.Menus.MenuItemControl=e.Control.extend({initialize:function(t,i){var a=this;a.expanded=new e.Value(!1),a.expandedArgumentsQueue=[],a.expanded.bind(function(e){var t=a.expandedArgumentsQueue.shift();t=n.extend({},a.defaultExpandedArguments,t),a.onChangeExpanded(e,t)}),e.Control.prototype.initialize.call(a,t,i),a.active.validate=function(){var t=e.section(a.section());return!!t&&t.active()}},embed:function(){var t,n=this.section();n&&((t=e.section(n))&&t.expanded()||e.settings.autofocus.control===this.id)&&this.actuallyEmbed()},actuallyEmbed:function(){"resolved"!==this.deferred.embedded.state()&&(this.renderContent(),this.deferred.embedded.resolve())},ready:function(){if(void 0===this.params.menu_item_id)throw new Error("params.menu_item_id was not defined");this._setupControlToggle(),this._setupReorderUI(),this._setupUpdateUI(),this._setupRemoveUI(),this._setupLinksUI(),this._setupTitleUI()},_setupControlToggle:function(){var t=this;this.container.find(".menu-item-handle").on("click",function(i){i.preventDefault(),i.stopPropagation();var a=t.getMenuControl(),o=n(i.target).is(".item-delete, .item-delete *"),s=n(i.target).is(".add-new-menu-item, .add-new-menu-item *");!n("body").hasClass("adding-menu-items")||o||s||e.Menus.availableMenuItemsPanel.close(),a.isReordering||a.isSorting||t.toggleForm()})},_setupReorderUI:function(){var e,i=this;e=t.template("menu-item-reorder-nav"),i.container.find(".item-controls").after(e),i.container.find(".menu-item-reorder-nav").find(".menus-move-up, .menus-move-down, .menus-move-left, .menus-move-right").on("click",function(){var e=n(this);e.focus();var t=e.is(".menus-move-up"),a=e.is(".menus-move-down"),o=e.is(".menus-move-left"),s=e.is(".menus-move-right");t?i.moveUp():a?i.moveDown():o?i.moveLeft():s&&i.moveRight(),e.focus()})},_setupUpdateUI:function(){var t,n=this,i=n.setting();n.elements={},n.elements.url=new e.Element(n.container.find(".edit-menu-item-url")),n.elements.title=new e.Element(n.container.find(".edit-menu-item-title")),n.elements.attr_title=new e.Element(n.container.find(".edit-menu-item-attr-title")),n.elements.target=new e.Element(n.container.find(".edit-menu-item-target")),n.elements.classes=new e.Element(n.container.find(".edit-menu-item-classes")),n.elements.xfn=new e.Element(n.container.find(".edit-menu-item-xfn")),n.elements.description=new e.Element(n.container.find(".edit-menu-item-description")),_.each(n.elements,function(e,t){e.bind(function(i){e.element.is("input[type=checkbox]")&&(i=i?e.element.val():"");var a=n.setting();a&&a[t]!==i&&((a=_.clone(a))[t]=i,n.setting.set(a))}),i&&("classes"!==t&&"xfn"!==t||!_.isArray(i[t])?e.set(i[t]):e.set(i[t].join(" ")))}),n.setting.bind(function(t,i){var a,o=n.params.menu_item_id,s=[],r=[];!1===t?(a=e.control("nav_menu["+String(i.nav_menu_term_id)+"]"),n.container.remove(),_.each(a.getMenuItemControls(),function(e){i.menu_item_parent===e.setting().menu_item_parent&&e.setting().position>i.position?s.push(e):e.setting().menu_item_parent===o&&r.push(e)}),_.each(s,function(e){var t=_.clone(e.setting());t.position+=r.length,e.setting.set(t)}),_.each(r,function(e,t){var n=_.clone(e.setting());n.position=i.position+t,n.menu_item_parent=i.menu_item_parent,e.setting.set(n)}),a.debouncedReflowMenuItems()):(_.each(t,function(e,i){n.elements[i]&&n.elements[i].set(t[i])}),n.container.find(".menu-item-data-parent-id").val(t.menu_item_parent),t.position===i.position&&t.menu_item_parent===i.menu_item_parent||n.getMenuControl().debouncedReflowMenuItems())}),t=function(){n.elements.url.element.toggleClass("invalid",n.setting.notifications.has("invalid_url"))},n.setting.notifications.bind("add",t),n.setting.notifications.bind("removed",t)},_setupRemoveUI:function(){var i=this;i.container.find(".item-delete").on("click",function(){var a,o,s,r=!0;n("body").hasClass("adding-menu-items")||(r=!1),o=i.container.nextAll(".customize-control-nav_menu_item:visible").first(),s=i.container.prevAll(".customize-control-nav_menu_item:visible").first(),a=o.length?o.find(!1===r?".item-edit":".item-delete").first():s.length?s.find(!1===r?".item-edit":".item-delete").first():i.container.nextAll(".customize-control-nav_menu").find(".add-new-menu-item").first(),i.container.slideUp(function(){i.setting.set(!1),t.a11y.speak(e.Menus.data.l10n.itemDeleted),a.focus()}),i.setting.set(!1)})},_setupLinksUI:function(){this.container.find("a.original-link").on("click",function(t){t.preventDefault(),e.previewer.previewUrl(t.target.toString())})},_setupTitleUI:function(){var t;this.container.find(".edit-menu-item-title").on("blur",function(){n(this).val(n.trim(n(this).val()))}),t=this.container.find(".menu-item-title"),this.setting.bind(function(i){var a,o;i&&(o=(a=n.trim(i.title))||i.original_title||e.Menus.data.l10n.untitled,i._invalid&&(o=e.Menus.data.l10n.invalidTitleTpl.replace("%s",o)),a||i.original_title?t.text(o).removeClass("no-title"):t.text(o).addClass("no-title"))})},getDepth:function(){var t=this,n=t.setting(),i=0;if(!n)return 0;for(;n&&n.menu_item_parent&&(i+=1,t=e.control("nav_menu_item["+n.menu_item_parent+"]"));)n=t.setting();return i},renderContent:function(){var t,n=this.setting();this.params.title=n.title||"",this.params.depth=this.getDepth(),this.container.data("item-depth",this.params.depth),t=["menu-item","menu-item-depth-"+String(this.params.depth),"menu-item-"+n.object,"menu-item-edit-inactive"],n._invalid?(t.push("menu-item-invalid"),this.params.title=e.Menus.data.l10n.invalidTitleTpl.replace("%s",this.params.title)):"draft"===n.status&&(t.push("pending"),this.params.title=e.Menus.data.pendingTitleTpl.replace("%s",this.params.title)),this.params.el_classes=t.join(" "),this.params.item_type_label=n.type_label,this.params.item_type=n.type,this.params.url=n.url,this.params.target=n.target,this.params.attr_title=n.attr_title,this.params.classes=_.isArray(n.classes)?n.classes.join(" "):n.classes,this.params.attr_title=n.attr_title,this.params.xfn=n.xfn,this.params.description=n.description,this.params.parent=n.menu_item_parent,this.params.original_title=n.original_title||"",this.container.addClass(this.params.el_classes),e.Control.prototype.renderContent.call(this)},getMenuControl:function(){var t=this.setting();return t&&t.nav_menu_term_id?e.control("nav_menu["+t.nav_menu_term_id+"]"):null},expandControlSection:function(){var e=this.container.closest(".accordion-section");e.hasClass("open")||e.find(".accordion-section-title:first").trigger("click")},_toggleExpanded:e.Section.prototype._toggleExpanded,expand:e.Section.prototype.expand,expandForm:function(e){this.expand(e)},collapse:e.Section.prototype.collapse,collapseForm:function(e){this.collapse(e)},toggleForm:function(e,t){void 0===e&&(e=!this.expanded()),e?this.expand(t):this.collapse(t)},onChangeExpanded:function(t,n){var i,a,o,s=this;a=(i=this.container).find(".menu-item-settings:first"),void 0===t&&(t=!a.is(":visible")),a.is(":visible")!==t?t?(e.control.each(function(e){s.params.type===e.params.type&&s!==e&&e.collapseForm()}),o=function(){i.removeClass("menu-item-edit-inactive").addClass("menu-item-edit-active"),s.container.trigger("expanded"),n&&n.completeCallback&&n.completeCallback()},i.find(".item-edit").attr("aria-expanded","true"),a.slideDown("fast",o),s.container.trigger("expand")):(o=function(){i.addClass("menu-item-edit-inactive").removeClass("menu-item-edit-active"),s.container.trigger("collapsed"),n&&n.completeCallback&&n.completeCallback()},s.container.trigger("collapse"),i.find(".item-edit").attr("aria-expanded","false"),a.slideUp("fast",o)):n&&n.completeCallback&&n.completeCallback()},focus:function(t){var n,i=this,a=(t=t||{}).completeCallback;n=function(){i.expandControlSection(),t.completeCallback=function(){i.container.find(".menu-item-settings").find("input, select, textarea, button, object, a[href], [tabindex]").filter(":visible").first().focus(),a&&a()},i.expandForm(t)},e.section.has(i.section())?e.section(i.section()).expand({completeCallback:n}):n()},moveUp:function(){this._changePosition(-1),t.a11y.speak(e.Menus.data.l10n.movedUp)},moveDown:function(){this._changePosition(1),t.a11y.speak(e.Menus.data.l10n.movedDown)},moveLeft:function(){this._changeDepth(-1),t.a11y.speak(e.Menus.data.l10n.movedLeft)},moveRight:function(){this._changeDepth(1),t.a11y.speak(e.Menus.data.l10n.movedRight)},_changePosition:function(e){var t,i,a=_.clone(this.setting()),o=[];if(1!==e&&-1!==e)throw new Error("Offset changes by 1 are only supported.");if(this.setting()){if(_(this.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===a.menu_item_parent&&o.push(e.setting)}),o.sort(function(e,t){return e().position-t().position}),-1===(i=_.indexOf(o,this.setting)))throw new Error("Expected setting to be among siblings.");0===i&&e<0||i===o.length-1&&e>0||((t=o[i+e])&&t.set(n.extend(_.clone(t()),{position:a.position})),a.position+=e,this.setting.set(a))}},_changeDepth:function(t){if(1!==t&&-1!==t)throw new Error("Offset changes by 1 are only supported.");var i,a,o,s=this,r=_.clone(s.setting()),u=[];if(_(s.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===r.menu_item_parent&&u.push(e)}),u.sort(function(e,t){return e.setting().position-t.setting().position}),-1===(i=_.indexOf(u,s)))throw new Error("Expected control to be among siblings.");if(-1===t){if(!r.menu_item_parent)return;o=e.control("nav_menu_item["+r.menu_item_parent+"]"),_(u).chain().slice(i).each(function(e,t){e.setting.set(n.extend({},e.setting(),{menu_item_parent:s.params.menu_item_id,position:t}))}),_(s.getMenuControl().getMenuItemControls()).each(function(e){var t;e.setting().menu_item_parent===o.setting().menu_item_parent&&e.setting().position>o.setting().position&&(t=_.clone(e.setting()),e.setting.set(n.extend(t,{position:t.position+1})))}),r.position=o.setting().position+1,r.menu_item_parent=o.setting().menu_item_parent,s.setting.set(r)}else if(1===t){if(0===i)return;a=u[i-1],r.menu_item_parent=a.params.menu_item_id,r.position=0,_(s.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===r.menu_item_parent&&(r.position=Math.max(r.position,e.setting().position))}),r.position+=1,s.setting.set(r)}}}),e.Menus.MenuNameControl=e.Control.extend({ready:function(){var t=this;if(t.setting){var n=t.setting();t.nameElement=new e.Element(t.container.find(".menu-name-field")),t.nameElement.bind(function(e){var n=t.setting();n&&n.name!==e&&((n=_.clone(n)).name=e,t.setting.set(n))}),n&&t.nameElement.set(n.name),t.setting.bind(function(e){e&&t.nameElement.set(e.name)})}}}),e.Menus.MenuLocationsControl=e.Control.extend({ready:function(){var t=this;t.container.find(".assigned-menu-location").each(function(){var a=n(this),o=a.find("input[type=checkbox]"),s=new e.Element(o),r=e("nav_menu_locations["+o.data("location-id")+"]"),u=""===t.params.menu_id,d=u?_.noop:function(e){s.set(e)},c=u?_.noop:function(e){r.set(e?t.params.menu_id:0)},l=function(t){var n=e("nav_menu["+String(t)+"]");t&&n&&n()?a.find(".theme-location-set").show().find("span").text(i(n().name)):a.find(".theme-location-set").hide()};d(r.get()===t.params.menu_id),o.on("change",function(){c(this.checked)}),r.bind(function(e){d(e===t.params.menu_id),l(e)}),l(r.get())})},setSelections:function(e){this.container.find(".menu-location").each(function(t,n){var i=n.dataset.locationId;n.checked=i in e&&e[i]})}}),e.Menus.MenuAutoAddControl=e.Control.extend({ready:function(){var t=this,n=t.setting();t.active.validate=function(){var n=e.section(t.section());return!!n&&n.active()},t.autoAddElement=new e.Element(t.container.find("input[type=checkbox].auto_add")),t.autoAddElement.bind(function(e){var n=t.setting();n&&n.name!==e&&((n=_.clone(n)).auto_add=e,t.setting.set(n))}),n&&t.autoAddElement.set(n.auto_add),t.setting.bind(function(e){e&&t.autoAddElement.set(e.auto_add)})}}),e.Menus.MenuControl=e.Control.extend({ready:function(){var t,a,o,s=this,r=e.section(s.section()),u=s.params.menu_id,d=s.setting();if(void 0===this.params.menu_id)throw new Error("params.menu_id was not defined");s.active.validate=function(){return!!r&&r.active()},s.$controlSection=r.headContainer,s.$sectionContent=s.container.closest(".accordion-section-content"),this._setupModel(),e.section(s.section(),function(e){e.deferred.initSortables.done(function(e){s._setupSortable(e)})}),this._setupAddition(),this._setupTitle(),d&&(t=i(d.name),e.control.each(function(n){n.extended(e.controlConstructor.widget_form)&&"nav_menu"===n.params.widget_id_base&&(n.container.find(".nav-menu-widget-form-controls:first").show(),n.container.find(".nav-menu-widget-no-menus-message:first").hide(),0===(o=n.container.find("select")).find("option[value="+String(u)+"]").length&&o.append(new Option(t,u)))}),(a=n("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").show(),a.find(".nav-menu-widget-no-menus-message:first").hide(),0===(o=a.find(".widget-inside select:first")).find("option[value="+String(u)+"]").length&&o.append(new Option(t,u))),_.defer(function(){s.updateInvitationVisibility()})},_setupModel:function(){var t=this,n=t.params.menu_id;t.setting.bind(function(a){var o;!1===a?t._handleDeletion():(o=i(a.name),e.control.each(function(t){t.extended(e.controlConstructor.widget_form)&&"nav_menu"===t.params.widget_id_base&&t.container.find("select").find("option[value="+String(n)+"]").text(o)}))})},_setupSortable:function(t){var n=this;if(!t.is(n.$sectionContent))throw new Error("Unexpected menuList.");t.on("sortstart",function(){n.isSorting=!0}),t.on("sortstop",function(){setTimeout(function(){var t=n.$sectionContent.sortable("toArray"),i=[],a=0,o=10;n.isSorting=!1,n.$sectionContent.scrollLeft(0),_.each(t,function(t){var n,a,o;(o=t.match(/^customize-control-nav_menu_item-(-?\d+)$/,""))&&(n=parseInt(o[1],10),(a=e.control("nav_menu_item["+String(n)+"]"))&&i.push(a))}),_.each(i,function(e){if(!1!==e.setting()){var t=_.clone(e.setting());a+=1,o+=1,t.position=a,e.priority(o),t.menu_item_parent=parseInt(e.container.find(".menu-item-data-parent-id").val(),10),t.menu_item_parent||(t.menu_item_parent=0),e.setting.set(t)}})})}),n.isReordering=!1,this.container.find(".reorder-toggle").on("click",function(){n.toggleReordering(!n.isReordering)})},_setupAddition:function(){var t=this;this.container.find(".add-new-menu-item").on("click",function(i){t.$sectionContent.hasClass("reordering")||(n("body").hasClass("adding-menu-items")?(n(this).attr("aria-expanded","false"),e.Menus.availableMenuItemsPanel.close(),i.stopPropagation()):(n(this).attr("aria-expanded","true"),e.Menus.availableMenuItemsPanel.open(t)))})},_handleDeletion:function(){var i,a,o,s=this.params.menu_id,r=0;i=e.section(this.section()),a=function(){i.container.remove(),e.section.remove(i.id)},i&&i.expanded()?i.collapse({completeCallback:function(){a(),t.a11y.speak(e.Menus.data.l10n.menuDeleted),e.panel("nav_menus").focus()}}):a(),e.each(function(e){/^nav_menu\[/.test(e.id)&&!1!==e()&&(r+=1)}),e.control.each(function(t){if(t.extended(e.controlConstructor.widget_form)&&"nav_menu"===t.params.widget_id_base){var n=t.container.find("select");n.val()===String(s)&&n.prop("selectedIndex",0).trigger("change"),t.container.find(".nav-menu-widget-form-controls:first").toggle(0!==r),t.container.find(".nav-menu-widget-no-menus-message:first").toggle(0===r),t.container.find("option[value="+String(s)+"]").remove()}}),(o=n("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").toggle(0!==r),o.find(".nav-menu-widget-no-menus-message:first").toggle(0===r),o.find("option[value="+String(s)+"]").remove()},_setupTitle:function(){var t=this;t.setting.bind(function(a){if(a){var o=e.section(t.section()),s=t.params.menu_id,r=o.headContainer.find(".accordion-section-title"),u=o.contentContainer.find(".customize-section-title h3"),d=o.headContainer.find(".menu-in-location"),c=u.find(".customize-action"),l=i(a.name);r.text(l),d.length&&d.appendTo(r),u.text(l),c.length&&c.prependTo(u),e.control.each(function(e){/^nav_menu_locations\[/.test(e.id)&&e.container.find("option[value="+s+"]").text(l)}),o.contentContainer.find(".customize-control-checkbox input").each(function(){n(this).prop("checked")&&n(".current-menu-location-name-"+n(this).data("location-id")).text(l)})}})},toggleReordering:function(n){var i=this.container.find(".add-new-menu-item"),a=this.container.find(".reorder-toggle"),o=this.$sectionContent.find(".item-title");(n=Boolean(n))!==this.$sectionContent.hasClass("reordering")&&(this.isReordering=n,this.$sectionContent.toggleClass("reordering",n),this.$sectionContent.sortable(this.isReordering?"disable":"enable"),this.isReordering?(i.attr({tabindex:"-1","aria-hidden":"true"}),a.attr("aria-label",e.Menus.data.l10n.reorderLabelOff),t.a11y.speak(e.Menus.data.l10n.reorderModeOn),o.attr("aria-hidden","false")):(i.removeAttr("tabindex aria-hidden"),a.attr("aria-label",e.Menus.data.l10n.reorderLabelOn),t.a11y.speak(e.Menus.data.l10n.reorderModeOff),o.attr("aria-hidden","true")),n&&_(this.getMenuItemControls()).each(function(e){e.collapseForm()}))},getMenuItemControls:function(){var t=[],n=this.params.menu_id;return e.control.each(function(e){"nav_menu_item"===e.params.type&&e.setting()&&n===e.setting().nav_menu_term_id&&t.push(e)}),t},reflowMenuItems:function(){var e,t=this.getMenuItemControls();(e=function(t){var n=[],i=t.currentParent;_.each(t.menuItemControls,function(e){i===e.setting().menu_item_parent&&n.push(e)}),n.sort(function(e,t){return e.setting().position-t.setting().position}),_.each(n,function(n){t.currentAbsolutePosition+=1,n.priority.set(t.currentAbsolutePosition),n.container.hasClass("menu-item-depth-"+String(t.currentDepth))||(_.each(n.container.prop("className").match(/menu-item-depth-\d+/g),function(e){n.container.removeClass(e)}),n.container.addClass("menu-item-depth-"+String(t.currentDepth))),n.container.data("item-depth",t.currentDepth),t.currentDepth+=1,t.currentParent=n.params.menu_item_id,e(t),t.currentDepth-=1,t.currentParent=i}),n.length&&(_(n).each(function(e){e.container.removeClass("move-up-disabled move-down-disabled move-left-disabled move-right-disabled"),0===t.currentDepth?e.container.addClass("move-left-disabled"):10===t.currentDepth&&e.container.addClass("move-right-disabled")}),n[0].container.addClass("move-up-disabled").addClass("move-right-disabled").toggleClass("move-down-disabled",1===n.length),n[n.length-1].container.addClass("move-down-disabled").toggleClass("move-up-disabled",1===n.length))})({menuItemControls:t,currentParent:0,currentDepth:0,currentAbsolutePosition:0}),this.updateInvitationVisibility(t),this.container.find(".reorder-toggle").toggle(t.length>1)},debouncedReflowMenuItems:_.debounce(function(){this.reflowMenuItems.apply(this,arguments)},0),addItemToMenu:function(i){var a,o,s,r,u,d=0,c=10;return _.each(this.getMenuItemControls(),function(e){!1!==e.setting()&&(c=Math.max(c,e.priority()),0===e.setting().menu_item_parent&&(d=Math.max(d,e.setting().position)))}),d+=1,c+=1,delete(i=n.extend({},e.Menus.data.defaultSettingValues.nav_menu_item,i,{nav_menu_term_id:this.params.menu_id,original_title:i.title,position:d})).id,u=e.Menus.generatePlaceholderAutoIncrementId(),a="nav_menu_item["+String(u)+"]",o={type:"nav_menu_item",transport:e.Menus.data.settingTransport,previewer:e.previewer},(s=e.create(a,a,{},o)).set(i),r=new e.controlConstructor.nav_menu_item(a,{type:"nav_menu_item",section:this.id,priority:c,settings:{default:a},menu_item_id:u}),e.control.add(r),s.preview(),this.debouncedReflowMenuItems(),t.a11y.speak(e.Menus.data.l10n.itemAdded),r},updateInvitationVisibility:function(e){var t=e||this.getMenuItemControls();this.container.find(".new-menu-item-invitation").toggle(0===t.length)}}),e.Menus.NewMenuControl=e.Control.extend({initialize:function(){"undefined"!=typeof console&&console.warn&&console.warn("[DEPRECATED] wp.customize.NewMenuControl will be removed. Please use wp.customize.Menus.createNavMenu() instead."),e.Control.prototype.initialize.apply(this,arguments)},ready:function(){this._bindHandlers()},_bindHandlers:function(){var e=this,t=n("#customize-control-new_menu_name input"),i=n("#create-new-menu-submit");t.on("keydown",function(t){13===t.which&&e.submit()}),i.on("click",function(t){e.submit(),t.stopPropagation(),t.preventDefault()})},submit:function(){var n,i=this.container.closest(".accordion-section-new-menu").find(".menu-name-field").first(),a=i.val();if(!a)return i.addClass("invalid"),void i.focus();n=e.Menus.createNavMenu(a),i.val(""),i.removeClass("invalid"),t.a11y.speak(e.Menus.data.l10n.menuAdded),n.focus()}}),n.extend(e.controlConstructor,{nav_menu_location:e.Menus.MenuLocationControl,nav_menu_item:e.Menus.MenuItemControl,nav_menu:e.Menus.MenuControl,nav_menu_name:e.Menus.MenuNameControl,new_menu:e.Menus.NewMenuControl,nav_menu_locations:e.Menus.MenuLocationsControl,nav_menu_auto_add:e.Menus.MenuAutoAddControl}),n.extend(e.panelConstructor,{nav_menus:e.Menus.MenusPanel}),n.extend(e.sectionConstructor,{nav_menu:e.Menus.MenuSection,new_menu:e.Menus.NewMenuSection}),e.bind("ready",function(){e.Menus.availableMenuItemsPanel=new e.Menus.AvailableMenuItemsPanelView({collection:e.Menus.availableMenuItems}),e.bind("saved",function(t){(t.nav_menu_updates||t.nav_menu_item_updates)&&e.Menus.applySavedData(t)}),e.state("changesetStatus").bind(function(t){"publish"===t&&(e("nav_menus_created_posts")._value=[])}),e.previewer.bind("focus-nav-menu-item-control",e.Menus.focusMenuItemControl)}),e.Menus.applySavedData=function(i){var a={},o={};_(i.nav_menu_updates).each(function(i){var o,s,r,u,d,c,l,m,p,f,h,v,g;if("inserted"===i.status){if(!i.previous_term_id)throw new Error("Expected previous_term_id");if(!i.term_id)throw new Error("Expected term_id");if(o="nav_menu["+String(i.previous_term_id)+"]",!e.has(o))throw new Error("Expected setting to exist: "+o);if(u=e(o),!e.section.has(o))throw new Error("Expected control to exist: "+o);if(m=e.section(o),!(l=u.get()))throw new Error("Did not expect setting to be empty (deleted).");l=n.extend(_.clone(l),i.saved_value),a[i.previous_term_id]=i.term_id,s="nav_menu["+String(i.term_id)+"]",d=e.create(s,s,l,{type:"nav_menu",transport:e.Menus.data.settingTransport,previewer:e.previewer}),(g=m.expanded())&&m.collapse(),p=new e.Menus.MenuSection(s,{panel:"nav_menus",title:l.name,customizeAction:e.Menus.data.l10n.customizingMenus,type:"nav_menu",priority:m.priority.get(),menu_id:i.term_id}),e.section.add(p),e.control.each(function(t){var n,a;t.extended(e.controlConstructor.widget_form)&&"nav_menu"===t.params.widget_id_base&&(a=(n=t.container.find("select")).find("option[value="+String(i.previous_term_id)+"]"),n.find("option[value="+String(i.term_id)+"]").prop("selected",a.prop("selected")),a.remove())}),u.callbacks.disable(),u.set(!1),u.preview(),d.preview(),u._dirty=!1,m.container.remove(),e.section.remove(o),v=0,e.each(function(e){/^nav_menu\[/.test(e.id)&&!1!==e()&&(v+=1)}),(h=n("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").toggle(0!==v),h.find(".nav-menu-widget-no-menus-message:first").toggle(0===v),h.find("option[value="+String(i.previous_term_id)+"]").remove(),t.customize.control.each(function(e){/^nav_menu_locations\[/.test(e.id)&&e.container.find("option[value="+String(i.previous_term_id)+"]").remove()}),e.each(function(t){var n=e.state("saved").get();/^nav_menu_locations\[/.test(t.id)&&t.get()===i.previous_term_id&&(t.set(i.term_id),t._dirty=!1,e.state("saved").set(n),t.preview())}),g&&p.expand()}else if("updated"===i.status){if(r="nav_menu["+String(i.term_id)+"]",!e.has(r))throw new Error("Expected setting to exist: "+r);c=e(r),_.isEqual(i.saved_value,c.get())||(f=e.state("saved").get(),c.set(i.saved_value),c._dirty=!1,e.state("saved").set(f))}}),_(i.nav_menu_item_updates).each(function(e){e.previous_post_id&&(o[e.previous_post_id]=e.post_id)}),_(i.nav_menu_item_updates).each(function(t){var n,i,s,r,u,d,c;if("inserted"===t.status){if(!t.previous_post_id)throw new Error("Expected previous_post_id");if(!t.post_id)throw new Error("Expected post_id");if(n="nav_menu_item["+String(t.previous_post_id)+"]",!e.has(n))throw new Error("Expected setting to exist: "+n);if(s=e(n),!e.control.has(n))throw new Error("Expected control to exist: "+n);if(d=e.control(n),!(u=s.get()))throw new Error("Did not expect setting to be empty (deleted).");if((u=_.clone(u)).menu_item_parent<0){if(!o[u.menu_item_parent])throw new Error("inserted ID for menu_item_parent not available");u.menu_item_parent=o[u.menu_item_parent]}a[u.nav_menu_term_id]&&(u.nav_menu_term_id=a[u.nav_menu_term_id]),i="nav_menu_item["+String(t.post_id)+"]",r=e.create(i,i,u,{type:"nav_menu_item",transport:e.Menus.data.settingTransport,previewer:e.previewer}),c=new e.controlConstructor.nav_menu_item(i,{type:"nav_menu_item",menu_id:t.post_id,section:"nav_menu["+String(u.nav_menu_term_id)+"]",priority:d.priority.get(),settings:{default:i},menu_item_id:t.post_id}),d.container.remove(),e.control.remove(n),e.control.add(c),s.callbacks.disable(),s.set(!1),s.preview(),r.preview(),s._dirty=!1,c.container.toggleClass("menu-item-edit-inactive",d.container.hasClass("menu-item-edit-inactive"))}}),_.each(i.widget_nav_menu_updates,function(t,n){var i=e(n);i&&(i._value=t,i.preview())})},e.Menus.focusMenuItemControl=function(t){var n=e.Menus.getMenuItemControl(t);n&&n.focus()},e.Menus.getMenuControl=function(t){return e.control("nav_menu["+t+"]")},e.Menus.getMenuItemControl=function(t){return e.control(function(e){return"nav_menu_item["+e+"]"}(t))}}(wp.customize,wp,jQuery);