window.wp=window.wp||{};var inlineEditPost;!function(a,b){inlineEditPost={init:function(){var b=this,c=a("#inline-edit"),d=a("#bulk-edit");b.type=a("table.widefat").hasClass("pages")?"page":"post",b.what="#post-",c.keyup(function(a){if(27===a.which)return inlineEditPost.revert()}),d.keyup(function(a){if(27===a.which)return inlineEditPost.revert()}),a(".cancel",c).click(function(){return inlineEditPost.revert()}),a(".save",c).click(function(){return inlineEditPost.save(this)}),a("td",c).keydown(function(b){if(13===b.which&&!a(b.target).hasClass("cancel"))return inlineEditPost.save(this)}),a(".cancel",d).click(function(){return inlineEditPost.revert()}),a('#inline-edit .inline-edit-private input[value="private"]').click(function(){var b=a("input.inline-edit-password-input");a(this).prop("checked")?b.val("").prop("disabled",!0):b.prop("disabled",!1)}),a("#the-list").on("click","a.editinline",function(a){a.preventDefault(),inlineEditPost.edit(this)}),a("#bulk-edit").find("fieldset:first").after(a("#inline-edit fieldset.inline-edit-categories").clone()).siblings("fieldset:last").prepend(a("#inline-edit label.inline-edit-tags").clone()),a('select[name="_status"] option[value="future"]',d).remove(),a("#doaction, #doaction2").click(function(c){var d;b.whichBulkButtonId=a(this).attr("id"),d=b.whichBulkButtonId.substr(2),"edit"===a('select[name="'+d+'"]').val()?(c.preventDefault(),b.setBulk()):a("form#posts-filter tr.inline-editor").length>0&&b.revert()})},toggle:function(b){var c=this;"none"===a(c.what+c.getId(b)).css("display")?c.revert():c.edit(b)},setBulk:function(){var b="",c=this.type,d=!0;return this.revert(),a("#bulk-edit td").attr("colspan",a("th:visible, td:visible",".widefat:first thead").length),a("table.widefat tbody").prepend(a("#bulk-edit")).prepend('<tr class="hidden"></tr>'),a("#bulk-edit").addClass("inline-editor").show(),a('tbody th.check-column input[type="checkbox"]').each(function(){if(a(this).prop("checked")){d=!1;var c,e=a(this).val();c=a("#inline_"+e+" .post_title").html()||inlineEditL10n.notitle,b+='<div id="ttle'+e+'"><a id="_'+e+'" class="ntdelbutton" title="'+inlineEditL10n.ntdeltitle+'">X</a>'+c+"</div>"}}),d?this.revert():(a("#bulk-titles").html(b),a("#bulk-titles a").click(function(){var b=a(this).attr("id").substr(1);a('table.widefat input[value="'+b+'"]').prop("checked",!1),a("#ttle"+b).remove()}),"post"===c&&a("tr.inline-editor textarea[data-wp-taxonomy]").each(function(b,c){a(c).autocomplete("instance")||a(c).wpTagsSuggest()}),void a("html, body").animate({scrollTop:0},"fast"))},edit:function(b){var c,d,e,f,g,h,i,j,k,l,m,n=this,o=!0;for(n.revert(),"object"==typeof b&&(b=n.getId(b)),c=["post_title","post_name","post_author","_status","jj","mm","aa","hh","mn","ss","post_password","post_format","menu_order","page_template"],"page"===n.type&&c.push("post_parent"),d=a("#inline-edit").clone(!0),a("td",d).attr("colspan",a("th:visible, td:visible",".widefat:first thead").length),a(n.what+b).removeClass("is-expanded").hide().after(d).after('<tr class="hidden"></tr>'),e=a("#inline_"+b),a(':input[name="post_author"] option[value="'+a(".post_author",e).text()+'"]',d).val()||a(':input[name="post_author"]',d).prepend('<option value="'+a(".post_author",e).text()+'">'+a("#"+n.type+"-"+b+" .author").text()+"</option>"),1===a(':input[name="post_author"] option',d).length&&a("label.inline-edit-author",d).hide(),k=0;k<c.length;k++)l=a("."+c[k],e),l.find("img").replaceWith(function(){return this.alt}),l=l.text(),a(':input[name="'+c[k]+'"]',d).val(l);if("open"===a(".comment_status",e).text()&&a('input[name="comment_status"]',d).prop("checked",!0),"open"===a(".ping_status",e).text()&&a('input[name="ping_status"]',d).prop("checked",!0),"sticky"===a(".sticky",e).text()&&a('input[name="sticky"]',d).prop("checked",!0),a(".post_category",e).each(function(){var c,e=a(this).text();e&&(c=a(this).attr("id").replace("_"+b,""),a("ul."+c+"-checklist :checkbox",d).val(e.split(",")))}),a(".tags_input",e).each(function(){var c=a(this),e=a(this).attr("id").replace("_"+b,""),f=a("textarea.tax_input_"+e,d),g=inlineEditL10n.comma;c.find("img").replaceWith(function(){return this.alt}),c=c.text(),c&&(","!==g&&(c=c.replace(/,/g,g)),f.val(c)),f.wpTagsSuggest()}),f=a("._status",e).text(),"future"!==f&&a('select[name="_status"] option[value="future"]',d).remove(),m=a(".inline-edit-password-input").prop("disabled",!1),"private"===f&&(a('input[name="keep_private"]',d).prop("checked",!0),m.val("").prop("disabled",!0)),g=a('select[name="post_parent"] option[value="'+b+'"]',d),g.length>0){for(h=g[0].className.split("-")[1],i=g;o&&(i=i.next("option"),0!==i.length);)j=i[0].className.split("-")[1],j<=h?o=!1:(i.remove(),i=g);g.remove()}return a(d).attr("id","edit-"+b).addClass("inline-editor").show(),a(".ptitle",d).focus(),!1},save:function(c){var d,e,f=a(".post_status_page").val()||"";return"object"==typeof c&&(c=this.getId(c)),a("table.widefat .spinner").addClass("is-active"),d={action:"inline-save",post_type:typenow,post_ID:c,edit_date:"true",post_status:f},e=a("#edit-"+c).find(":input").serialize(),d=e+"&"+a.param(d),a.post(ajaxurl,d,function(d){var e=a("#edit-"+c+" .inline-edit-save .notice-error"),f=e.find(".error");a("table.widefat .spinner").removeClass("is-active"),a(".ac_results").hide(),d?-1!==d.indexOf("<tr")?(a(inlineEditPost.what+c).siblings("tr.hidden").addBack().remove(),a("#edit-"+c).before(d).remove(),a(inlineEditPost.what+c).hide().fadeIn(400,function(){a(this).find(".editinline").focus(),b.a11y.speak(inlineEditL10n.saved)})):(d=d.replace(/<.[^<>]*?>/g,""),e.removeClass("hidden"),f.html(d),b.a11y.speak(f.text())):(e.removeClass("hidden"),f.html(inlineEditL10n.error),b.a11y.speak(inlineEditL10n.error))},"html"),!1},revert:function(){var b=a(".widefat"),c=a(".inline-editor",b).attr("id");return c&&(a(".spinner",b).removeClass("is-active"),a(".ac_results").hide(),"bulk-edit"===c?(a("#bulk-edit",b).removeClass("inline-editor").hide().siblings(".hidden").remove(),a("#bulk-titles").empty(),a("#inlineedit").append(a("#bulk-edit")),a("#"+inlineEditPost.whichBulkButtonId).focus()):(a("#"+c).siblings("tr.hidden").addBack().remove(),c=c.substr(c.lastIndexOf("-")+1),a(this.what+c).show().find(".editinline").focus())),!1},getId:function(b){var c=a(b).closest("tr").attr("id"),d=c.split("-");return d[d.length-1]}},a(document).ready(function(){inlineEditPost.init()}),a(document).on("heartbeat-tick.wp-check-locked-posts",function(b,c){var d=c["wp-check-locked-posts"]||{};a("#the-list tr").each(function(b,c){var e,f,g=c.id,h=a(c);d.hasOwnProperty(g)?h.hasClass("wp-locked")||(e=d[g],h.find(".column-title .locked-text").text(e.text),h.find(".check-column checkbox").prop("checked",!1),e.avatar_src&&(f=a('<img class="avatar avatar-18 photo" width="18" height="18" alt="" />').attr("src",e.avatar_src.replace(/&amp;/g,"&")),h.find(".column-title .locked-avatar").empty().append(f)),h.addClass("wp-locked")):h.hasClass("wp-locked")&&h.removeClass("wp-locked").delay(1e3).find(".locked-info span").empty()})}).on("heartbeat-send.wp-check-locked-posts",function(b,c){var d=[];a("#the-list tr").each(function(a,b){b.id&&d.push(b.id)}),d.length&&(c["wp-check-locked-posts"]=d)}).ready(function(){"undefined"!=typeof b&&b.heartbeat&&b.heartbeat.interval(15)})}(jQuery,window.wp);