!function(a){function b(a){return a.split(new RegExp(e+"\\s*"))}function c(a){return b(a).pop()}if("undefined"!=typeof window.tagsSuggestL10n&&"undefined"!=typeof window.uiAutocompleteL10n){var d=0,e=window.tagsSuggestL10n.tagDelimiter||",";a.fn.wpTagsSuggest=function(f){var g,h,i=a(this);f=f||{};var j=f.taxonomy||i.attr("data-wp-taxonomy")||"post_tag";return delete f.taxonomy,f=a.extend({source:function(b,e){var f;return h===b.term?void e(g):(f=c(b.term),a.get(window.ajaxurl,{action:"ajax-tag-search",tax:j,q:f}).always(function(){i.removeClass("ui-autocomplete-loading")}).done(function(a){var b,c=[];if(a){a=a.split("\n");for(b in a){var f=++d;c.push({id:f,name:a[b]})}g=c,e(c)}else e(c)}),void(h=b.term))},focus:function(a,b){i.attr("aria-activedescendant","wp-tags-autocomplete-"+b.item.id),a.preventDefault()},select:function(c,d){var f=b(i.val());return f.pop(),f.push(d.item.name,""),i.val(f.join(e+" ")),a.ui.keyCode.TAB===c.keyCode?(window.wp.a11y.speak(window.tagsSuggestL10n.termSelected,"assertive"),c.preventDefault()):a.ui.keyCode.ENTER===c.keyCode&&(c.preventDefault(),c.stopPropagation()),!1},open:function(){i.attr("aria-expanded","true")},close:function(){i.attr("aria-expanded","false")},minLength:2,position:{my:"left top+2",at:"left bottom",collision:"none"},messages:{noResults:window.uiAutocompleteL10n.noResults,results:function(a){return a>1?window.uiAutocompleteL10n.manyResults.replace("%d",a):window.uiAutocompleteL10n.oneResult}}},f),i.on("keydown",function(){i.removeAttr("aria-activedescendant")}).autocomplete(f).autocomplete("instance")._renderItem=function(b,c){return a('<li role="option" id="wp-tags-autocomplete-'+c.id+'">').text(c.name).appendTo(b)},i.attr({role:"combobox","aria-autocomplete":"list","aria-expanded":"false","aria-owns":i.autocomplete("widget").attr("id")}).on("focus",function(){var a=b(i.val()).pop();a&&i.autocomplete("search")}).autocomplete("widget").addClass("wp-tags-autocomplete").attr("role","listbox").removeAttr("tabindex").on("menufocus",function(a,b){b.item.attr("aria-selected","true")}).on("menublur",function(){a(this).find('[aria-selected="true"]').removeAttr("aria-selected")}),this}}}(jQuery);