/*! This file is auto-generated */
.media-item .describe{border-collapse:collapse;width:100%;border-top:1px solid #ddd;clear:both;cursor:default}.media-item.media-blank .describe{border:0}.media-item .describe th{vertical-align:top;text-align:right;padding:5px 10px 10px;width:140px}.media-item .describe .align th{padding-top:0}.media-item .media-item-info tr{background-color:transparent}.media-item .describe td{padding:0 0 8px 8px;vertical-align:top}.media-item thead.media-item-info td{padding:4px 10px 0}.media-item .media-item-info .A1B1{padding:0 10px 0 0}.media-item td.savesend{padding-bottom:15px}.media-item .thumbnail{max-height:128px;max-width:128px}#wpbody-content #async-upload-wrap a{display:none}.media-upload-form{margin-top:20px}.media-upload-form td label{margin-left:6px;margin-right:2px}.media-upload-form .align .field label{display:inline;padding:0 23px 0 0;margin:0 3px 0 1em;font-weight:600}.media-upload-form tr.image-size label{margin:0 5px 0 0;font-weight:600}.media-upload-form th.label label{font-weight:600;margin:.5em;font-size:13px}.media-upload-form th.label label span{padding:0 5px}.media-item .describe input[type=text],.media-item .describe textarea{width:460px}.media-item .describe p.help{margin:0;padding:0 5px 0 0}.describe-toggle-off,.describe-toggle-on,.media-item .edit-attachment{display:block;line-height:36px;float:left;margin-left:10px}.media-item .describe-toggle-off,.media-item.open .describe-toggle-on{display:none}.media-item.open .describe-toggle-off{display:block}.js .html-uploader #plupload-upload-ui,.media-item .startclosed,.media-item .startopen{display:none}.media-upload-form .media-item{min-height:36px;margin-bottom:1px;position:relative;width:100%;background:#fff}.media-upload-form .media-item,.media-upload-form .media-item .error{box-shadow:0 1px 0 #ddd}#media-items:empty{border:0}.media-item .filename{line-height:36px;overflow:hidden;margin-right:6px}.media-item .pinkynail{float:right;margin:2px 3px 0 10px;max-width:40px;max-height:32px}.media-item .original{position:relative;height:34px}.media-item .progress{float:left;height:22px;margin:7px 6px;width:200px;line-height:2em;padding:0;overflow:hidden;border-radius:22px;background:#ddd;box-shadow:inset 0 1px 2px rgba(0,0,0,.1)}.media-item .bar{z-index:9;width:0;height:100%;margin-top:-22px;border-radius:22px;background-color:#0073aa;box-shadow:inset 0 0 2px rgba(0,0,0,.3)}.media-item .progress .percent{z-index:10;position:relative;width:200px;padding:0;color:#fff;text-align:center;line-height:22px;font-weight:400;text-shadow:0 1px 2px rgba(0,0,0,.2)}.upload-php .fixed .column-parent{width:15%}.js .html-uploader #html-upload-ui{display:block}.media-upload-form .media-item .error,.media-upload-form .media-item.error{width:auto;margin:0 0 1px}.media-upload-form .media-item .error{padding:10px 14px 10px 0}.media-item .error-div a.dismiss{display:block;float:left;margin:0 15px 0 10px}.find-box{background-color:#fff;box-shadow:0 3px 6px rgba(0,0,0,.3);width:600px;overflow:hidden;margin-right:-300px;position:fixed;top:30px;bottom:30px;right:50%;z-index:100105}.find-box-head{background:#fcfcfc;border-bottom:1px solid #ddd;height:36px;font-size:18px;font-weight:600;line-height:36px;padding:0 16px 0 36px;position:absolute;top:0;right:0;left:0}.find-box-inside{overflow:auto;padding:16px;background-color:#fff;position:absolute;top:37px;bottom:45px;overflow-y:scroll;width:100%;box-sizing:border-box}.find-box-search{padding-bottom:16px}.find-box-search .spinner{float:none;right:105px;position:absolute}#find-posts-response,.find-box-search{position:relative}#find-posts-input,#find-posts-search{float:right}#find-posts-input{width:140px;height:28px;margin:0 0 0 4px}.widefat .found-radio{padding-left:0;width:16px}#find-posts-close{width:36px;height:36px;border:none;padding:0;position:absolute;top:0;left:0;cursor:pointer;text-align:center;background:0 0;color:#666}#find-posts-close:focus,#find-posts-close:hover{color:#00a0d2}#find-posts-close:focus{outline:0;box-shadow:0 0 0 1px #5b9dd9,0 0 2px 1px rgba(30,140,190,.8)}#find-posts-close:before{font:400 20px/36px dashicons;vertical-align:top;speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;content:"\f158"}.find-box-buttons{padding:8px 16px;background:#fcfcfc;border-top:1px solid #ddd;position:absolute;bottom:0;right:0;left:0}@media screen and (max-width:782px){.find-box-inside{bottom:57px}}@media screen and (max-width:660px){.find-box{top:0;bottom:0;right:0;left:0;margin:0;width:100%}}.ui-find-overlay{position:fixed;top:0;right:0;left:0;bottom:0;background:#000;opacity:.7;filter:alpha(opacity=70);z-index:100100}ul#dismissed-updates{display:none}form.upgrade{margin-top:8px}form.upgrade .hint{font-style:italic;font-size:85%;margin:-.5em 0 2em}#poststuff .inside .the-tagcloud{margin:5px 0 10px;padding:8px;border:1px solid #ddd;line-height:1.8em;word-spacing:3px}.drag-drop #drag-drop-area{border:4px dashed #b4b9be;height:200px}.drag-drop .drag-drop-inside{margin:70px auto 0;width:250px}.drag-drop-inside p{color:#a0a5aa;font-size:14px;margin:5px 0;display:none}.drag-drop .drag-drop-inside p{text-align:center}.drag-drop-inside p.drag-drop-info{font-size:20px}.drag-drop .drag-drop-inside p,.drag-drop-inside p.drag-drop-buttons{display:block}.media-frame.mode-grid .media-frame-menu,.media-frame.mode-grid .media-frame-router,.media-frame.mode-grid .media-frame-title,.upload-php .mode-grid .hide-sidebar .media-sidebar{display:none}.drag-drop.drag-over #drag-drop-area{border-color:#83b4d8}#plupload-upload-ui{position:relative}.media-frame.mode-grid,.media-frame.mode-grid .attachments-browser .attachments,.media-frame.mode-grid .media-frame-content,.media-frame.mode-grid .uploader-inline-content{position:static}.media-frame.mode-grid .media-frame-content{background-color:transparent;border:none}.upload-php .mode-grid .media-sidebar{position:relative;width:auto;margin-top:12px;padding:0 16px;border-right:4px solid #dd3d36;box-shadow:0 1px 1px 0 rgba(0,0,0,.1);background-color:#fff}.upload-php .mode-grid .media-sidebar .media-uploader-status{border-bottom:none;padding-bottom:0;max-width:100%}.upload-php .mode-grid .media-sidebar .upload-error{margin:12px 0;padding:4px 0 0;border:none;box-shadow:none;background:0 0}.upload-php .mode-grid .media-sidebar .media-uploader-status .upload-dismiss-errors{top:-10px;left:-14px;padding:10px}.upload-php .mode-grid .media-sidebar .media-uploader-status .upload-dismiss-errors:before{content:"\f153";display:block;font:400 16px/1 dashicons;color:#72777c}.upload-php .mode-grid .media-sidebar .media-uploader-status .upload-dismiss-errors:focus:before,.upload-php .mode-grid .media-sidebar .media-uploader-status .upload-dismiss-errors:hover:before{color:#c00}.upload-php .mode-grid .media-sidebar .media-uploader-status.errors h2,.upload-php .mode-grid .media-sidebar .media-uploader-status.errors h3{display:none}.media-frame.mode-grid .uploader-inline{position:relative;top:auto;left:auto;right:auto;bottom:auto;padding-top:0;margin-top:20px;border:4px dashed #b4b9be}.media-frame.mode-select .attachments-browser.fixed .attachments{position:relative;top:94px;padding-bottom:94px}.media-frame.mode-grid .attachment.details:focus,.media-frame.mode-grid .attachment:focus,.media-frame.mode-grid .selected.attachment:focus{box-shadow:inset 0 0 2px 3px #f1f1f1,inset 0 0 0 7px #5b9dd9;outline:0}.media-frame.mode-grid .selected.attachment{box-shadow:inset 0 0 0 5px #f1f1f1,inset 0 0 0 7px #ccc}.media-frame.mode-grid .attachment.details{box-shadow:inset 0 0 0 3px #f1f1f1,inset 0 0 0 7px #1e8cbe}.media-frame.mode-grid.mode-select .attachment .thumbnail{opacity:.65}.media-frame.mode-select .attachment.selected .thumbnail{opacity:1}.media-frame.mode-grid .media-toolbar{margin-bottom:15px;height:auto}.media-frame.mode-grid .media-toolbar select{margin:0 0 0 10px;font-size:14px}.media-frame.mode-grid.mode-edit .media-toolbar-secondary>.select-mode-toggle-button{margin:0 0 0 8px;vertical-align:middle}.media-frame.mode-grid .attachments-browser .bulk-select{display:inline-block;margin:0 0 0 10px}.media-frame.mode-grid .search{margin-top:0}.media-frame.mode-grid .spinner{margin-top:16px}.attachments-browser .media-toolbar-secondary>.media-button{margin-left:10px}.media-frame.mode-select .attachments-browser.fixed .media-toolbar{position:fixed;top:32px;right:auto;left:20px;margin-top:0}.media-frame.mode-grid .attachments-browser{padding:0}.media-frame.mode-grid .attachments-browser .attachments{padding:2px}.media-frame.mode-grid .attachments-browser .no-media{color:#666;font-size:18px;font-style:normal;margin:0;padding:100px 0 0;text-align:center}.edit-attachment-frame{display:block;height:100%;width:100%}.edit-attachment-frame .edit-media-header{overflow:hidden}.upload-php .media-modal-close .media-modal-icon:before{content:"\f335";font-size:22px}.edit-attachment-frame .edit-media-header .left,.edit-attachment-frame .edit-media-header .right,.upload-php .media-modal-close{cursor:pointer;color:#72777c;background-color:transparent;height:50px;width:50px;padding:0;position:absolute;text-align:center;border:0;border-right:1px solid #ddd;transition:color .1s ease-in-out,background .1s ease-in-out}.upload-php .media-modal-close{top:0;left:0}.edit-attachment-frame .edit-media-header .left{left:102px}.edit-attachment-frame .edit-media-header .right{left:51px}.edit-attachment-frame .media-frame-title{right:0;left:150px}.edit-attachment-frame .edit-media-header .left:before,.edit-attachment-frame .edit-media-header .right:before{font:400 20px/50px dashicons!important;display:inline;font-weight:300}.edit-attachment-frame .edit-media-header .left:focus,.edit-attachment-frame .edit-media-header .left:hover,.edit-attachment-frame .edit-media-header .right:focus,.edit-attachment-frame .edit-media-header .right:hover,.upload-php .media-modal-close:focus,.upload-php .media-modal-close:hover{background:#ddd;border-color:#ccc;color:#000;outline:0;box-shadow:none}.upload-php .media-modal-close:focus .media-modal-icon:before,.upload-php .media-modal-close:hover .media-modal-icon:before{color:#000}.edit-attachment-frame .edit-media-header .left:before,.rtl .edit-attachment-frame .edit-media-header .right:before{content:"\f345"}.edit-attachment-frame .edit-media-header .right:before,.rtl .edit-attachment-frame .edit-media-header .left:before{content:"\f341"}.edit-attachment-frame .edit-media-header .left.disabled,.edit-attachment-frame .edit-media-header .left.disabled:hover,.edit-attachment-frame .edit-media-header .right.disabled,.edit-attachment-frame .edit-media-header .right.disabled:hover{color:#ccc;background:inherit;cursor:default;pointer-events:none}.edit-attachment-frame .media-frame-content,.edit-attachment-frame .media-frame-router{right:0}.edit-attachment-frame .media-frame-content{border-bottom:none;bottom:0;top:50px}.edit-attachment-frame .attachment-details{position:absolute;overflow:auto;top:0;bottom:0;left:0;right:0;box-shadow:inset 0 4px 4px -4px rgba(0,0,0,.1)}.edit-attachment-frame .attachment-media-view{float:right;width:65%;height:100%}.edit-attachment-frame .attachment-media-view .thumbnail{box-sizing:border-box;padding:16px;height:100%}.edit-attachment-frame .attachment-media-view .details-image{display:block;margin:0 auto 16px;max-width:100%;max-height:90%;max-height:calc(100% - 42px);background-image:linear-gradient(-45deg,#c4c4c4 25%,transparent 25%,transparent 75%,#c4c4c4 75%,#c4c4c4),linear-gradient(-45deg,#c4c4c4 25%,transparent 25%,transparent 75%,#c4c4c4 75%,#c4c4c4);background-position:100% 0,10px 10px;background-size:20px 20px}.edit-attachment-frame .attachment-media-view .attachment-actions{text-align:center}.edit-attachment-frame .wp-media-wrapper{margin-bottom:12px}.edit-attachment-frame input,.edit-attachment-frame textarea{padding:6px 8px;line-height:16px}.edit-attachment-frame .attachment-info{overflow:auto;box-sizing:border-box;margin-bottom:0;padding:12px 16px 0;width:35%;height:100%;box-shadow:inset 0 4px 4px -4px rgba(0,0,0,.1);border-bottom:0;border-right:1px solid #ddd;background:#f3f3f3}.edit-attachment-frame .attachment-info .details,.edit-attachment-frame .attachment-info .settings{position:relative;overflow:hidden;float:none;margin-bottom:15px;padding-bottom:15px;border-bottom:1px solid #ddd}.edit-attachment-frame .attachment-info .filename{font-weight:400;color:#666}.edit-attachment-frame .attachment-info .thumbnail{margin-bottom:12px}.attachment-info .actions{margin-bottom:16px}.attachment-info .actions a{display:inline;text-decoration:none}.wp_attachment_details label[for=content]{font-size:13px;line-height:1.5;margin:1em 0}.wp_attachment_details #attachment_caption{height:4em}.describe .image-editor{vertical-align:top}.imgedit-wrap{position:relative;padding-top:10px}.imgedit-settings fieldset,.imgedit-settings p{margin:8px 0}.imgedit-settings legend{margin-bottom:5px}.describe .imgedit-wrap .imgedit-settings{padding:0 5px}.wp_attachment_holder div.updated{margin-top:0}.wp_attachment_holder .imgedit-wrap>div{height:auto}.wp_attachment_holder .imgedit-wrap .imgedit-panel-content{padding:3px 0 0 16px;float:right}.wp_attachment_holder .imgedit-wrap .imgedit-settings{float:left;width:250px}.imgedit-settings input{margin-top:0;vertical-align:middle}.imgedit-wait{position:absolute;top:0;bottom:0;width:100%;background:#fff;opacity:.7;filter:alpha(opacity=70);display:none}.imgedit-wait:before{content:"";display:block;width:20px;height:20px;position:absolute;right:50%;top:50%;margin:-10px -10px 0 0;background:url(../images/spinner.gif) center no-repeat;background-size:20px 20px;-webkit-transform:translateZ(0);transform:translateZ(0)}.no-float{float:none}.A1B1 .button,.A1B1 .spinner,.wp_attachment_image .button,.wp_attachment_image .spinner{float:right}.imgedit-settings .disabled,.media-disabled{color:#a0a5aa}.A1B1{overflow:hidden}.no-js .wp_attachment_image .button{display:none}.imgedit-menu{min-width:300px;margin:0 0 12px}.imgedit-menu .note-no-rotate{clear:both;margin:0;padding:1em 0 0}.image-editor .imgedit-menu .button{float:right;width:32px;height:32px;margin:0 0 0 8px;padding:0;background:#f1f1f1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;line-height:16px;color:#72777c}.imgedit-menu .button:before{font:400 20px/1 dashicons;speak:none;vertical-align:middle}.imgedit-menu .button.disabled{border-color:#ccc;background-color:#ddd;color:#72777c;filter:alpha(opacity=50);opacity:.5;cursor:default}.imgedit-crop:before{content:"\f165"}.imgedit-rleft:before{content:"\f166"}.imgedit-rright:before{content:"\f167"}.imgedit-flipv:before{content:"\f168"}.imgedit-fliph:before{content:"\f169"}.imgedit-undo:before{content:"\f171"}.imgedit-redo:before{content:"\f172"}.imgedit-crop-wrap{position:relative}.imgedit-crop-wrap img{background-image:linear-gradient(-45deg,#c4c4c4 25%,transparent 25%,transparent 75%,#c4c4c4 75%,#c4c4c4),linear-gradient(-45deg,#c4c4c4 25%,transparent 25%,transparent 75%,#c4c4c4 75%,#c4c4c4);background-position:100% 0,10px 10px;background-size:20px 20px}.imgedit-crop{margin:0 0 0 8px}.imgedit-rleft{margin:0 3px}.imgedit-rright{margin:0 3px 0 8px}.imgedit-flipv{margin:0 3px}.imgedit-fliph{margin:0 3px 0 8px}.imgedit-undo{margin:0 3px}.imgedit-redo{margin:0 3px 0 8px}.imgedit-thumbnail-preview{margin:10px 0 0 8px}.imgedit-thumbnail-preview-caption{display:block}#poststuff .imgedit-group-top h2,#poststuff .imgedit-group-top h3{display:inline-block;margin:0;padding:0;font-size:14px;line-height:1.4}#poststuff .imgedit-group-top .button-link{text-decoration:none;color:#23282d}.imgedit-applyto .imgedit-label{display:block;padding:.5em 0 0}.imgedit-help{display:none;font-style:italic}.image-editor .imgedit-settings .imgedit-help-toggle,.image-editor .imgedit-settings .imgedit-help-toggle:active,.image-editor .imgedit-settings .imgedit-help-toggle:hover{border:1px solid transparent;margin:-1px -1px 0 0;padding:0;background:0 0;color:#0074a2;font-size:20px;line-height:1;cursor:pointer;box-sizing:content-box;box-shadow:none}.image-editor .imgedit-settings .imgedit-help-toggle:focus{color:#0074a2;border-color:#5b9dd9;outline:0;box-shadow:0 0 3px rgba(0,115,170,.8)}.form-table td.imgedit-response{padding:0}.imgedit-submit{margin:8px 0 0}.imgedit-submit-btn{margin-right:20px}.imgedit-wrap .nowrap{white-space:nowrap;font-size:12px;line-height:inherit}span.imgedit-scale-warn{color:#dc3232;font-size:20px;font-style:normal;visibility:hidden;vertical-align:middle}.imgedit-save-target{margin:8px 0}.imgedit-group{margin-bottom:8px;padding:10px}.imgedit-settings .imgedit-crop-ratio input[type=text],.imgedit-settings .imgedit-crop-sel input[type=text],.imgedit-settings .imgedit-scale input[type=text]{width:50px;font-size:14px;padding:5px 8px}.imgedit-separator{display:inline-block;width:7px;text-align:center;vertical-align:middle;font-size:13px;color:#444}.imgedit-settings .imgedit-scale .button{margin-bottom:0}audio,video{display:inline-block;max-width:100%}.mejs-container{width:100%;max-width:100%}@media print,(-webkit-min-device-pixel-ratio:1.25),(min-resolution:120dpi){.imgedit-wait:before{background-image:url(../images/spinner-2x.gif)}}@media screen and (max-width:782px){.wp_attachment_details label[for=content]{font-size:14px;line-height:1.5em}.media-upload-form .media-item .error,.media-upload-form .media-item.error{font-size:13px;line-height:1.5}.media-upload-form .media-item.error{padding:1px 10px}.media-upload-form .media-item .error{padding:10px 12px 10px 0}.imgedit-settings .imgedit-crop-ratio input[type=text],.imgedit-settings .imgedit-crop-sel input[type=text],.imgedit-settings .imgedit-scale input[type=text]{width:60px;font-size:16px;padding:6px 10px}.imgedit-applyto .imgedit-label{vertical-align:middle}}@media only screen and (max-width:1120px){#wp-media-grid .wp-filter .attachment-filters{max-width:100%}}@media only screen and (max-width:782px){.media-frame.mode-select .attachments-browser.fixed .media-toolbar{top:46px;left:10px}}@media only screen and (max-width:600px){.media-frame.mode-select .attachments-browser.fixed .media-toolbar{top:0}}@media only screen and (max-width:480px){.edit-attachment-frame .media-frame-title{left:110px}.edit-attachment-frame .edit-media-header .left,.edit-attachment-frame .edit-media-header .right,.upload-php .media-modal-close{width:40px;height:40px}.upload-php .media-modal-close .media-modal-icon{margin:9px 10px}.edit-attachment-frame .edit-media-header .left:before,.edit-attachment-frame .edit-media-header .right:before{line-height:40px!important}.edit-attachment-frame .edit-media-header .left{left:82px}.edit-attachment-frame .edit-media-header .right{left:41px}.edit-attachment-frame .media-frame-content{top:40px}.edit-attachment-frame .attachment-media-view{float:none;height:auto;width:100%}.edit-attachment-frame .attachment-info{height:auto;width:100%}}@media only screen and (max-width:640px),screen and (max-height:400px){.upload-php .mode-grid .media-sidebar{max-width:100%}}